{"extends": "expo/tsconfig.base", "compilerOptions": {"allowJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": "./", "paths": {"@components": ["components/index"], "@ui": ["components/ui"], "@hooks": ["hooks/index"], "@utils": ["utils/index"], "@utils/*": ["utils/*"], "@breez": ["utils/breez/index"], "@breez/*": ["utils/breez/*"], "@locales/*": ["locales/*"], "@providers": ["providers/index"], "@theme": ["theme/index"], "@types/*": ["types/*"], "@factory/*": ["factory/*"], "@const/*": ["const/*"], "@store/*": ["store/*"], "@assets/*": ["assets/*"], "@endpoints/*": ["endpoints/*"], "@api": ["utils/api/index"], "@api/*": ["utils/api/*"], "@services": ["services/index"], "@services/*": ["services/*"]}, "strictNullChecks": true}, "include": ["**/*.ts", "**/*.tsx", "global.d.ts"], "exclude": ["node_modules", "node_modules/**", "**/node_modules/*", "babel.config.js", "metro.config.js"]}