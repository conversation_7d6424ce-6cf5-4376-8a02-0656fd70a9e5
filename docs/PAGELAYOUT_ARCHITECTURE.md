# PageLayout Component Architecture

## Overview
Comprehensive page layout component providing standardized page structure with advanced features including pull-to-refresh, animated headers, loading states, navigation integration, and accessibility support. Serves as the foundation for all application pages with consistent behavior, performance optimization, and extensive customization options.

## Component API

### Core Props
```typescript
interface PageLayoutProps {
  // Content props
  title?: string;                    // Page title displayed in header and content
  description?: string | ReactNode;  // Subtitle or description below title
  children?: ReactNode;              // Main page content
  
  // Header configuration
  headerTitleSpacing?: number;       // Spacing between header and title (default: 4)
  headerBlurOptions?: BlurViewProps; // Blur effect customization
  headerWithProgress?: boolean;      // Show progress bar in header
  headerProgress?: number;           // Progress value (0-1)
  noHeader?: boolean;               // Hide header completely
  noBackButton?: boolean;           // Hide back button
  
  // Layout configuration
  noPadding?: boolean;              // Remove default content padding
  contentMarginTop?: number;        // Top margin for content area
  contentGap?: number;              // Gap between content children
  noScroll?: boolean;               // Disable scrolling
  bgColor?: string;                 // Background color (theme key)
  
  // Button configuration
  buttonText?: string;              // Bottom button text
  buttonProps?: ButtonProps;       // Button customization
  buttonTopLabel?: ReactNode;      // Content above button
  onButtonPress?: () => void;      // Button press handler
  
  // Pull-to-refresh
  pullToRefresh?: boolean;         // Enable pull-to-refresh
  onRefresh?: (pageKey: string) => Promise<void>; // Refresh handler
  refreshIndicatorOffset?: number; // Refresh indicator offset
  
  // Animations
  animEntering?: ReanimatedKeyframe; // Enter animation
  animExiting?: ReanimatedKeyframe;  // Exit animation
  noAnimation?: boolean;             // Disable animations
  
  // Advanced features
  headerOverlayContent?: boolean;   // Overlay header on content
  withBackgroundGradient?: boolean; // Add background gradient
  disableTopOverscroll?: boolean;   // Disable bounce effect
}
```

## Feature Breakdown

### Header System
- **Animated blur header** - Smooth blur transition based on scroll position with customizable intensity and tint
- **Progress indicator** - Optional progress bar for multi-step flows with animated progress updates
- **Dynamic title display** - Large title in content area that transitions to header title on scroll
- **Custom components** - Left and right header components with flexible positioning and styling options

### Scroll Behavior
- **Intelligent scrolling** - Automatic scroll detection based on content height with buffer calculations
- **Smooth animations** - Reanimated-powered scroll animations with configurable start/end points
- **Overscroll control** - Configurable bounce behavior with option to disable top overscroll

### Pull-to-Refresh Integration
- **Page-specific refresh** - Unique refresh state management per page with automatic cleanup
- **Error handling** - Comprehensive error capture and display with retry mechanisms
- **Visual feedback** - Native refresh indicator with theme-aware colors and proper positioning

### Button Management
- **Fixed bottom button** - Persistent action button with blur background and gradient options
- **Keyboard awareness** - Automatic button repositioning when keyboard is visible
- **Loading states** - Integrated loading indicators with disabled state management

## Usage Examples

### Basic Page Layout
```typescript
import { PageLayout } from '@ui';

function BasicPage() {
  return (
    <PageLayout
      title="Page Title"
      description="Optional description text"
      buttonText="Continue"
      onButtonPress={() => console.log('Button pressed')}
    >
      <Text>Page content goes here</Text>
    </PageLayout>
  );
}
```

### Pull-to-Refresh Implementation
```typescript
import { PageLayout } from '@ui';
import { useWalletRefresh } from '@hooks';

function DashboardPage() {
  const { handleRefresh } = useWalletRefresh();
  
  return (
    <PageLayout
      title="Dashboard"
      pullToRefresh
      onRefresh={handleRefresh}
      refreshIndicatorOffset={10}
    >
      <BalanceCard />
      <TransactionsList />
    </PageLayout>
  );
}
```

### Advanced Header Configuration
```typescript
function AdvancedPage() {
  return (
    <PageLayout
      title="Advanced Layout"
      headerBlurOptions={{
        intensity: 40,
        tint: 'systemThinMaterialLight'
      }}
      headerOverlayContent
      headerAnimationStartY={100}
      headerAnimationEndY={140}
      leftComponent={<CustomBackButton />}
      rightComponent={<SettingsButton />}
    >
      <ScrollableContent />
    </PageLayout>
  );
}
```

### Progress Flow Layout
```typescript
function StepPage() {
  const [progress, setProgress] = useState(0.3);
  
  return (
    <PageLayout
      title="Step 2 of 5"
      headerWithProgress
      headerProgress={progress}
      buttonText="Next Step"
      buttonProps={{
        disabled: !isStepComplete,
        loading: isProcessing
      }}
      onButtonPress={handleNextStep}
    >
      <StepContent onComplete={() => setProgress(0.6)} />
    </PageLayout>
  );
}
```

### Custom Background and Styling
```typescript
function ThemedPage() {
  return (
    <PageLayout
      bgColor="grey.900"
      withBackgroundGradient
      backgroundGradientOptions={{
        colors: ['#1FAD8C', 'transparent'],
        locations: [0, 0.7]
      }}
      buttonBlurOptions={{
        tint: 'dark',
        intensity: 25
      }}
      statusBarOptions={{
        barStyle: 'light-content'
      }}
    >
      <DarkThemedContent />
    </PageLayout>
  );
}
```

### No-Scroll Layout for Modals
```typescript
function ModalPage() {
  return (
    <PageLayout
      title="Modal Content"
      noScroll
      noBackButton
      noPadding
      noAnimation
      bgColor="white"
      buttonText="Close"
      onButtonPress={() => router.back()}
    >
      <ModalContent />
    </PageLayout>
  );
}
```

## Integration Patterns

### Authentication Flow Integration
```typescript
// Public pages
<PageLayout
  noBackButton
  bgColor="primary.50"
  buttonText="Sign In"
  buttonProps={{ href: '/login' }}
>
  <WelcomeContent />
</PageLayout>

// Protected pages
<PageLayout
  title="Account Settings"
  leftComponent={<ProfileAvatar />}
  rightComponent={<NotificationBadge />}
>
  <SettingsContent />
</PageLayout>
```

### Navigation Integration
```typescript
import { useRouter } from 'expo-router';

function NavigationPage() {
  const router = useRouter();
  
  return (
    <PageLayout
      title="Navigation Example"
      onBackPress={() => router.back()}
      buttonText="Go Forward"
      buttonProps={{
        href: '/next-page',
        icon: <ArrowRightIcon />
      }}
    >
      <NavigationContent />
    </PageLayout>
  );
}
```

### Form Integration
```typescript
function FormPage() {
  const { handleSubmit, isSubmitting, isValid } = useForm();
  
  return (
    <PageLayout
      title="User Information"
      description="Please fill out your details"
      buttonText="Save Changes"
      buttonProps={{
        disabled: !isValid,
        loading: isSubmitting
      }}
      onButtonPress={handleSubmit}
      pullToRefresh
      onRefresh={async () => {
        await refetchUserData();
      }}
    >
      <FormFields />
    </PageLayout>
  );
}
```

## Performance Optimizations

### Memory Management
- **Automatic cleanup** - Scroll listeners and animations are properly disposed on unmount
- **Ref management** - Efficient ref usage for layout calculations without causing re-renders
- **State optimization** - Minimal state updates with useCallback and useMemo for expensive calculations

### Scroll Performance
- **Native driver** - All scroll animations use native driver for 60fps performance
- **Throttled calculations** - Layout calculations are throttled to prevent excessive computation
- **Lazy evaluation** - Scroll-dependent features only activate when needed

### Render Optimization
- **Memoization** - Component is wrapped with React.memo to prevent unnecessary re-renders
- **Conditional rendering** - Features are conditionally rendered to minimize component tree size
- **Style caching** - Computed styles are cached and only recalculated when dependencies change

## Accessibility Features

### Screen Reader Support
- **Semantic structure** - Proper heading hierarchy and content organization for screen readers
- **Focus management** - Automatic focus handling for navigation and interactive elements
- **Accessibility labels** - Comprehensive labeling for all interactive components

### Keyboard Navigation
- **Tab order** - Logical tab sequence through interactive elements
- **Keyboard shortcuts** - Support for common keyboard shortcuts where applicable
- **Focus indicators** - Clear visual focus indicators for keyboard navigation

### Motion and Animation
- **Reduced motion** - Respects system accessibility settings for reduced motion
- **Animation controls** - Option to disable animations for users with motion sensitivity
- **Performance considerations** - Animations are optimized to not interfere with assistive technologies

## Troubleshooting Guide

### Common Issues

**Pull-to-refresh not working**
- Ensure `pullToRefresh={true}` and `onRefresh` handler are provided
- Check that content height allows for scroll gesture
- Verify refresh indicator offset doesn't conflict with header content

**Header animation glitches**
- Adjust `headerAnimationStartY` and `headerAnimationEndY` values
- Ensure content has sufficient height for scroll animation
- Check for conflicting scroll event handlers

**Button positioning issues**
- Verify keyboard awareness is working correctly
- Check button height calculations in layout measurements
- Ensure safe area insets are properly configured

**Performance issues with large content**
- Enable `noScroll` for static content that doesn't need scrolling
- Use `FlatList` or `VirtualizedList` for large datasets instead of ScrollView
- Implement proper key props for list items

### Debugging Tips

**Layout debugging**
```typescript
// Enable layout debugging
<PageLayout
  contentContainerStyle={{
    borderWidth: 1,
    borderColor: 'red' // Visualize content boundaries
  }}
  onLayout={(event) => {
    console.log('Layout:', event.nativeEvent.layout);
  }}
>
```

**Scroll debugging**
```typescript
// Monitor scroll events
<PageLayout
  onScroll={(event) => {
    console.log('Scroll Y:', event.nativeEvent.contentOffset.y);
  }}
>
```

## Migration Guide

### From Basic Layouts to PageLayout

**Before (Basic Layout)**
```typescript
function OldPage() {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView>
        <View style={{ padding: 16 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold' }}>Title</Text>
          <Text>Content</Text>
        </View>
      </ScrollView>
      <TouchableOpacity style={buttonStyles}>
        <Text>Button</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
}
```

**After (PageLayout)**
```typescript
function NewPage() {
  return (
    <PageLayout
      title="Title"
      buttonText="Button"
      onButtonPress={handlePress}
    >
      <Text>Content</Text>
    </PageLayout>
  );
}
```

### Migration Checklist
- [ ] Replace SafeAreaView + ScrollView with PageLayout
- [ ] Move title from content to title prop
- [ ] Convert bottom buttons to buttonText/buttonProps
- [ ] Update styling to use semantic props
- [ ] Add pull-to-refresh if needed
- [ ] Configure header options for advanced features
- [ ] Test accessibility and keyboard navigation
- [ ] Verify performance with large content
