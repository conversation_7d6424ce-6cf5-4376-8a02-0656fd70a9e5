# API Architecture

## Overview

Comprehensive HTTP client implementation with automatic authentication, token refresh, and React Query integration. Provides production-ready API layer with type-safe endpoints, standardized error handling, and optimized caching strategies for seamless data management across the application.

## File Structure

### Core HTTP Client

-   **`utils/api/axios.ts`** - Main axios instance with pre-configured base URL, default headers, and automatic authentication. Implements request interceptors for Bearer token injection and response interceptors for automatic token refresh on 401/418 errors. Handles authentication failures by clearing tokens and redirecting to login. Excludes authentication for public endpoints like login and registration.

-   **`utils/api/fetcher.ts`** - Generic data fetching function with overloaded TypeScript signatures for type safety. Supports full response or data-only returns, blob/multipart uploads, custom headers, timeout configuration, and selective error ignoring. Includes development debugging with detailed request/response logging and automatic error transformation for network issues.

### API Functions & Query Integration

-   **`utils/api/api-functions.ts`** - High-level API utilities providing standardized interfaces for data fetching and React Query integration. Exports `fetchAPI()` for imperative API calls and `getQueryData()` for declarative query configuration. Handles URL parameter substitution, query key generation, and dependency tracking for cache invalidation.

-   **`utils/api/query-client.ts`** - TanStack Query client configuration with custom defaults optimized for mobile applications. Disables refetch on window focus, sets 60-second stale time, implements custom retry logic (no retry on 403, max 1 retry for others), and provides server-side rendering support with proper query dehydration.

### Endpoint Management

-   **`utils/api/helpers.ts`** - Utility functions for endpoint creation and URL manipulation. Provides `createEndpoint()` for typed endpoint definitions, `setSlugs()` for URL parameter replacement (`:param` placeholders), and `setParams()` for query parameter appending. Ensures consistent endpoint structure and URL encoding across the application.

-   **`utils/api/types.ts`** - Core type definitions including HTTP method constants and Endpoint interface. Defines the standard endpoint structure with key, route, and method properties. Provides METHOD constants for type-safe HTTP method specification and ensures consistent typing across all API interactions.

### Authentication & Token Management

-   **`utils/api/refresh-token.ts`** - Automatic token refresh implementation using separate axios instance to avoid interceptor conflicts. Handles refresh token validation, new token storage, and cleanup on refresh failures.

-   **`utils/token.ts`** - Secure token storage utilities using expo-secure-store with hardware encryption. Implements save, load, and clear operations for both access and refresh tokens with appropriate keychain accessibility settings.

## Dependencies

### External Dependencies

-   **`axios`** - HTTP client for API requests with interceptor support and request/response transformation
-   **`@tanstack/react-query`** - Data fetching and caching library with automatic background updates and optimistic updates
-   **`expo-secure-store`** - Secure token storage with hardware encryption and keychain integration
-   **`expo-router`** - Navigation system for authentication redirects and route management

### Internal Dependencies

-   **`@store/auth.store`** - Authentication state management for user session tracking
-   **`@store/error.store`** - Global error state management for API error handling
-   **`const/secure-storage-keys`** - Centralized secure storage key constants

## Data Flow

**Request Flow:** Component action → Endpoint preparation → Request interceptor → HTTP request → Response processing
**Authentication Flow:** Token validation → Token injection → Token refresh on 401/418 → Retry request → Authentication failure handling
**Error Handling Flow:** Network error transformation → HTTP error propagation → Authentication error handling → Custom error ignoring

## Core API Functions

### Direct API Calls

-   **`fetchAPI<T>(endpoint, options)`** - Execute immediate API calls with type safety and error handling
-   **`getQueryData<T>(endpoint, options)`** - Prepare React Query configuration for declarative data fetching
-   **`createEndpoint({ method, route })`** - Create typed endpoint definitions with consistent structure

### Token Management

-   **`saveToken(token)`** - Store access token securely with hardware encryption
-   **`loadToken()`** - Retrieve stored access token for request authentication
-   **`refreshToken()`** - Automatically refresh expired tokens using refresh token
-   **`clearToken()`** - Remove stored tokens on logout or authentication failure

## Usage Examples

### 1. Query Pattern (`useQuery + getQueryData`)

**When to use:** Data fetching scenarios requiring caching, automatic refetching, and background updates. Ideal for GET requests where data should be cached and shared across components.

**Available Options (`PrepareDataOptions<T>`):**

-   `slugs` - URL parameter replacements for `:param` placeholders (`/users/:userId/`)
-   `dynamicKeys` - Additional cache keys for invalidation strategies
-   `params` - Query parameters appended to URL (`?key=value`)
-   `fetcherOptions` - HTTP configuration (timeout, headers, error handling)
-   `queryOptions` - React Query configuration (enabled, staleTime, retry logic)

```typescript
import { getQueryData } from "@api";
import { getUserInfo, getWalletAddresses } from "@endpoints";
import { useQuery } from "@tanstack/react-query";

// Basic query with conditional fetching
const {
    data: userInfo,
    isLoading,
    error,
} = useQuery(
    getQueryData<{ username: string }>(getUserInfo, {
        queryOptions: {
            enabled: isAuthenticated,
            staleTime: 5 * 60 * 1000, // 5 minutes
            retry: 2,
        },
    })
);

// Query with URL slugs and parameters
const { data: addresses } = useQuery(
    getQueryData<WalletAddress[]>(getWalletAddresses, {
        slugs: { userId: currentUser.id },
        params: { limit: "10", offset: "0" },
        dynamicKeys: [currentUser.id, "addresses"],
        queryOptions: {
            staleTime: 2 * 60 * 1000,
            enabled: !!currentUser.id,
        },
    })
);

// Query with custom error handling
const { data: termsData } = useQuery(
    getQueryData<TermsResponse>(getTerms, {
        fetcherOptions: {
            ignoreError: [403, 404],
            timeout: 10000,
        },
        queryOptions: {
            retry: false,
            refetchOnMount: false,
        },
    })
);
```

### 2. Direct API Calls (`fetchAPI`)

**When to use:** Imperative API calls, non-cached requests, custom response handling, or when you need full control over the request lifecycle.

**Available Options (`FetcherOptions`):**

-   `fullResponse` - Return full AxiosResponse instead of data only
-   `timeout` - Request timeout in milliseconds
-   `isBlob` - Enable multipart/form-data for file uploads
-   `headers` - Custom request headers
-   `ignoreError` - Status codes to ignore (won't throw errors)
-   `baseURL` - Override default base URL

```typescript
import { fetchAPI } from "@api";
import { createAccount, uploadDocument } from "@endpoints";

// Basic API call with data
const registerUser = async (userData: RegisterData) => {
    try {
        const response = await fetchAPI(createAccount, {
            data: { username: userData.email, password: userData.password },
        });
        return response;
    } catch (error) {
        handleRegistrationError(error);
    }
};

// Full response with custom headers
const getFullResponse = async () => {
    const fullResponse = await fetchAPI(getUserInfo, {
        fetcherOptions: {
            fullResponse: true,
            headers: { "Custom-Header": "value" },
            timeout: 15000,
        },
    });
    console.log("Status:", fullResponse.status);
    console.log("Headers:", fullResponse.headers);
    return fullResponse.data;
};

// File upload with blob handling
const uploadFile = async (file: File, documentType: string) => {
    const formData = new FormData();
    formData.append("document", file);
    formData.append("type", documentType);

    return await fetchAPI(uploadDocument, {
        data: formData,
        fetcherOptions: {
            isBlob: true,
            timeout: 30000,
            ignoreError: [413], // Ignore file too large errors
        },
    });
};
```

### 3. Mutation Pattern (`useMutation + fetchAPI`)

**When to use:** Data modification operations (POST, PUT, PATCH, DELETE) requiring loading states, error handling, and cache updates.

**Available Options:**

-   `onMutate` - Optimistic updates before request
-   `onSuccess` - Handle successful responses and cache updates
-   `onError` - Error handling and rollback logic
-   `onSettled` - Cleanup logic regardless of success/failure

```typescript
import { fetchAPI, getQueryClient } from "@api";
import { confirmRegistration, addWalletAddress } from "@endpoints";
import { useMutation, useQueryClient } from "@tanstack/react-query";

// Registration with error handling
const { mutate: confirmCode, isPending: isConfirming } = useMutation({
    mutationFn: (code: string) =>
        fetchAPI(confirmRegistration, {
            data: { code, username: params.username },
        }),
    onSuccess: () => {
        router.push("/activation-success");
    },
    onError: (error: AxiosError) => {
        addError(ErrorKey.RegistrationError, {
            errorText: error.response?.data?.error || error.message,
            status: error.status,
        });
    },
});

// Optimistic updates with cache invalidation
const { mutate: addAddress } = useMutation({
    mutationFn: (addressData: AddressData) => fetchAPI(addWalletAddress, { data: addressData }),

    onMutate: async (newAddress) => {
        // Cancel outgoing refetches
        await queryClient.cancelQueries({ queryKey: ["addresses"] });

        // Snapshot previous value
        const previousAddresses = queryClient.getQueryData(["addresses"]);

        // Optimistically update cache
        queryClient.setQueryData(["addresses"], (old: Address[]) => [...old, { ...newAddress, id: "temp-" + Date.now() }]);

        return { previousAddresses };
    },

    onError: (err, newAddress, context) => {
        // Rollback on error
        queryClient.setQueryData(["addresses"], context?.previousAddresses);
    },

    onSettled: () => {
        // Refetch to ensure consistency
        queryClient.invalidateQueries({ queryKey: ["addresses"] });
    },
});

// Batch operations with loading states
const { mutate: batchUpdate, isPending } = useMutation({
    mutationFn: async (updates: BatchUpdateData) => {
        const results = await Promise.all([
            fetchAPI(updateProfile, { data: updates.profile }),
            fetchAPI(updateSettings, { data: updates.settings }),
        ]);
        return results;
    },
    onSuccess: (results) => {
        // Update multiple cache entries
        queryClient.setQueryData(["profile"], results[0]);
        queryClient.setQueryData(["settings"], results[1]);
        showSuccessMessage("Profile updated successfully");
    },
});
```

### Endpoint Definition & Best Practices

```typescript
import { createEndpoint, METHOD } from "@api";

// Basic endpoint
export const getUserInfo = createEndpoint({
    method: METHOD.GET,
    route: "/reti-app/user-info/",
});

// Endpoint with URL parameters
export const updateProfile = createEndpoint({
    method: METHOD.PATCH,
    route: "/reti-app/user-profile/:userId/",
});

// Endpoint with TypeScript interfaces
export interface CreateAccountRequest {
    username: string;
    password: string;
}

export const createAccount = createEndpoint({
    method: METHOD.POST,
    route: "/register/",
});
```

**Best Practices:**

-   Group related endpoints in feature-specific files
-   Define TypeScript interfaces for request/response types
-   Use descriptive endpoint names that indicate their purpose
-   Include URL parameters as `:param` placeholders for dynamic routes

## Architecture Patterns

**Singleton Pattern** - Axios instance and Query client for consistent configuration
**Factory Pattern** - Endpoint creation and query preparation with standardized helpers
**Interceptor Pattern** - Request/response interceptors for authentication and error handling
**Repository Pattern** - Organized endpoint files and centralized type definitions

## Error Handling & Troubleshooting

**Automatic Recovery:** Token refresh on 401/418, network error transformation, configurable timeouts
**Error Propagation:** Structured error objects, authentication cleanup, field-specific validation errors

**Common Issues:**

-   **Token refresh fails** → Check refresh token validity and network connectivity
-   **Network timeouts** → Adjust timeout settings in fetcher options
-   **Stale data** → Adjust staleTime settings and implement proper cache invalidation
-   **Cache inconsistency** → Use appropriate query keys and dependency tracking

## Configuration Examples

```typescript
// Environment variables
EXPO_PUBLIC_BASE_URL=https://api.example.com
EXPO_PUBLIC_API_PREFIX=api

// Custom fetcher options
const { data } = useQuery(
  getQueryData<UserData>(getUserInfo, {
    fetcherOptions: {
      timeout: 10000,
      ignoreError: [404, 403],
      headers: { 'Custom-Header': 'value' }
    }
  })
);

// Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000,
      retry: (failureCount, error) => {
        if (error.status === 403) return false;
        return failureCount < 1;
      }
    }
  }
});
```
