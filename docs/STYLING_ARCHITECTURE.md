# Styling Architecture

## Overview

Comprehensive styling system built on react-native-unistyles with custom UI components, semantic props, and centralized theming. Provides consistent design language through reusable components (Box, Text, Button), type-safe styling with variants, and responsive design capabilities while maintaining accessibility and performance optimization.

## File Structure

### Core UI Components

-   **`components/ui/layout/box.tsx`** - Flexible layout component with semantic props for spacing, alignment, and styling. Supports polymorphic rendering with `as` prop, variant-based styling for flexbox properties, and dynamic color theming. Provides foundation for all layout compositions with consistent spacing system and type-safe prop validation.

-   **`components/ui/typography/text.tsx`** - Typography component with comprehensive text styling variants including weight, size, and color options. Implements semantic spacing props, dynamic color resolution from theme, and text alignment controls. Optimized with React.memo for performance and supports style composition for complex text layouts.

-   **`components/ui/buttons/button.tsx`** - Feature-rich button component with multiple variants, sizes, loading states, and icon positioning. Supports navigation integration via expo-router, semantic spacing props, and comprehensive accessibility features. Includes disabled states, custom styling options, and automatic color theming.

### Theme Configuration

-   **`unistyles.ts`** - Primary theme configuration defining color palettes, spacing functions, and design tokens. Implements systematic color scales (primary, grey), utility functions for consistent spacing (gap, padding, margin), and theme structure for component variants. Provides foundation for all styling decisions across the application.

-   **`theme/theme.ts`** - Extended theme configuration for react-native-paper integration with additional semantic color definitions. Includes specialized colors for different UI states (error, warning, success), custom color names for brand identity, and compatibility layer for paper components.

-   **`utils/theme-helpers.ts`** - Utility functions for theme color resolution and dynamic styling. Provides `getColorFromTheme()` for nested color object access, theme-aware color parsing, and fallback handling for invalid color references. Essential for dynamic theming and color customization.

### Specialized Components

-   **`components/ui/inputs/text-input.tsx`** - Styled text input component with label, error handling, and password visibility toggle. Implements consistent styling with theme integration, validation state display, and accessibility features. Supports custom rendering patterns and forwarded refs for form integration.

## Dependencies

### External Dependencies

-   **`react-native-unistyles`** - Core styling system providing theme management, variant support, and performance optimization
-   **`react-native-paper`** - UI component library for additional components and theming compatibility
-   **`react-native`** - Base React Native components and styling primitives

### Internal Dependencies

-   **`hooks/use-spacing`** - Spacing hook for semantic prop processing and style generation
-   **`expo-router`** - Navigation integration for button components and routing

## Component APIs

### Box Component

```typescript
interface BoxProps {
    // Layout variants
    flexDirection?: "row" | "column" | "rowReverse" | "columnReverse";
    align?: "start" | "center" | "end" | "stretch" | "baseline";
    justify?: "start" | "center" | "end" | "between" | "around" | "evenly";

    // Styling props
    bgColor?: string; // Theme color key
    borderColor?: string; // Theme color key
    borderWidth?: number;
    radius?: number;
    zIndex?: number;
    wrap?: boolean;

    // Semantic spacing props
    p?: number; // Padding
    px?: number; // Padding inline
    py?: number; // Padding block
    pt?: number; // Padding top
    pb?: number; // Padding bottom
    pl?: number; // Padding left
    pr?: number; // Padding right
    m?: number; // Margin
    mx?: number; // Margin inline
    my?: number; // Margin block
    mt?: number; // Margin top
    mb?: number; // Margin bottom
    ml?: number; // Margin left
    mr?: number; // Margin right
    gap?: number;

    // Polymorphic rendering
    as?: ElementType; // Render as different component
    style?: StyleProp<ViewStyle>;
    children?: React.ReactNode;
}
```

### Text Component

```typescript
interface TextProps {
    // Typography variants
    weight?: "regular" | "medium" | "semibold" | "bold";
    size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "3xl";

    // Styling props
    color?: string; // Theme color key
    textAlign?: "left" | "center" | "right";

    // Semantic spacing props (inherited)
    // Standard React Native Text props
    style?: TextStyle | TextStyle[];
    children?: React.ReactNode;
}
```

### Button Component

```typescript
interface ButtonProps {
    // Appearance variants
    variant?: "primary" | "secondary" | "outline" | "ghost" | "disabled";
    size?: "xs" | "default" | "lg";
    rounded?: boolean;

    // Content props
    children?: React.ReactNode;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right" | "farLeft";

    // Layout props
    w?: DimensionValue; // Width
    align?: ViewStyle["alignItems"];
    self?: ViewStyle["alignSelf"];
    bgColor?: string;

    // Behavior props
    onPress?: (e: GestureResponderEvent) => void;
    disabled?: boolean;
    loading?: boolean;
    // [!!!] VERY IMPORTANT: Use href for navigation instead of creating custom onPress handlers for navigation
    href?: string; // Navigation integration

    // Custom styling
    buttonStyle?: ViewStyle;
    labelStyle?: TextStyle;

    // Semantic spacing props (inherited)
}
```

## Theming System

### Color Palette

```typescript
export const theme = {
    colors: {
        primary: {
            100: "#D4F7EF", // Lightest
            200: "#A8F0DF",
            300: "#7DE8CF",
            400: "#52E0BF",
            500: "#40C1A3", // Base
            600: "#1FAD8C",
            700: "#1F7A65",
            800: "#0F5746",
            900: "#0A2922", // Darkest
        },
        grey: {
            50: "#F9FAFA", // Lightest
            100: "#DEE3E1",
            // ... full scale
            900: "#131615", // Darkest
        },
        // Semantic colors
        red: "#E96160", // Error
        yellow: "#F38600", // Warning
        blue: "#0094FE", // Info
        green: "#24D327", // Success
    },
    // Spacing functions
    gap: (v: number) => v * 4,
    padding: (v: number) => v * 4,
    margin: (v: number) => v * 4,
    spacing: (v: number) => v * 4,
};
```

### Typography Scale

```typescript
const textVariants = {
    size: {
        xs: { fontSize: 12, lineHeight: 16 },
        sm: { fontSize: 14, lineHeight: 20 },
        md: { fontSize: 16, lineHeight: 24 },
        lg: { fontSize: 18, lineHeight: 28 },
        xl: { fontSize: 20, lineHeight: 32 },
        "2xl": { fontSize: 24, lineHeight: 36 },
        "3xl": { fontSize: 32, lineHeight: 48 },
    },
    weight: {
        regular: { fontWeight: "400" },
        medium: { fontWeight: "500" },
        semibold: { fontWeight: "600" },
        bold: { fontWeight: "700" },
    },
};
```

## Usage Examples

### Basic Layout Composition

```typescript
import { Box, Text, Button } from "@ui";

function ProfileCard() {
    return (
        <Box bgColor="white" borderColor="grey.100" borderWidth={1} radius={12} p={4} gap={3}>
            <Box flexDirection="row" align="center" gap={3}>
                <Box bgColor="primary.100" radius={20} style={{ width: 40, height: 40 }} />
                <Box flex={1}>
                    <Text weight="semibold" size="lg">
                        John Doe
                    </Text>
                    <Text color="grey.600" size="sm">
                        Software Engineer
                    </Text>
                </Box>
            </Box>

            <Button variant="primary" size="sm" onPress={() => console.log("Follow pressed")}>
                Follow
            </Button>
        </Box>
    );
}
```

### Semantic Props vs Inline Styles

```typescript
// ✅ Preferred: Semantic props
<Box p={4} mt={2} bgColor="primary.100">
  <Text weight="medium" color="primary.800">
    Semantic styling
  </Text>
</Box>

// ❌ Avoid: Inline styles (use only when semantic props insufficient)
<Box style={{ padding: 16, marginTop: 8, backgroundColor: '#D4F7EF' }}>
  <Text style={{ fontWeight: '500', color: '#0F5746' }}>
    Inline styling
  </Text>
</Box>
```

### Component Variants and Customization

```typescript
// Button variants
<Button variant="primary" size="lg">Primary Action</Button>
<Button variant="outline" size="default">Secondary Action</Button>
<Button variant="ghost" size="xs">Tertiary Action</Button>

// Text variants
<Text size="3xl" weight="bold" color="primary.800">Heading</Text>
<Text size="md" weight="regular" color="grey.600">Body text</Text>
<Text size="sm" weight="medium" color="red">Error message</Text>

// Box layout variants
<Box flexDirection="row" justify="between" align="center">
  <Text>Left content</Text>
  <Text>Right content</Text>
</Box>
```

### Responsive Design Patterns

```typescript
import { useUnistyles } from "react-native-unistyles";

function ResponsiveLayout() {
    const { theme } = useUnistyles();

    return (
        <Box
            flexDirection="row"
            gap={4}
            p={4}
            style={{
                // Responsive breakpoints
                flexDirection: theme.breakpoints?.md ? "row" : "column",
            }}>
            <Box flex={1} bgColor="primary.50" p={3} radius={8}>
                <Text>Content 1</Text>
            </Box>
            <Box flex={1} bgColor="primary.50" p={3} radius={8}>
                <Text>Content 2</Text>
            </Box>
        </Box>
    );
}
```

### Custom Component Creation

```typescript
import { Box, Text, type BoxProps } from "@ui";

interface CardProps extends BoxProps {
    title: string;
    subtitle?: string;
    children?: React.ReactNode;
}

export function Card({ title, subtitle, children, ...boxProps }: CardProps) {
    return (
        <Box bgColor="white" borderColor="grey.100" borderWidth={1} radius={12} p={4} gap={3} {...boxProps}>
            <Box gap={1}>
                <Text weight="semibold" size="lg">
                    {title}
                </Text>
                {subtitle && (
                    <Text color="grey.600" size="sm">
                        {subtitle}
                    </Text>
                )}
            </Box>
            {children}
        </Box>
    );
}
```

## Component Composition Patterns

### Layout Containers

```typescript
// Stack layout
<Box gap={4}>
  <Component1 />
  <Component2 />
  <Component3 />
</Box>

// Horizontal layout
<Box flexDirection="row" gap={3} align="center">
  <Icon />
  <Text flex={1}>Content</Text>
  <Button size="xs">Action</Button>
</Box>

// Grid-like layout
<Box flexDirection="row" wrap gap={2}>
  {items.map(item => (
    <Box key={item.id} w="48%">
      <ItemCard item={item} />
    </Box>
  ))}
</Box>
```

### Form Layouts

```typescript
<Box gap={4}>
    <TextInput label="Email" placeholder="Enter your email" keyboardType="email-address" />
    <TextInput label="Password" placeholder="Enter your password" isPassword />
    <Button variant="primary" loading={isSubmitting} onPress={handleSubmit}>
        Sign In
    </Button>
</Box>
```

## Accessibility Considerations

### Semantic Structure

-   Use proper heading hierarchy with Text size variants
-   Implement accessible touch targets (minimum 44x44 points)
-   Provide meaningful accessibility labels and hints

### Color and Contrast

-   Ensure sufficient color contrast ratios (4.5:1 for normal text)
-   Don't rely solely on color to convey information
-   Support system accessibility settings (high contrast, reduced motion)

### Interactive Elements

-   Implement proper focus management and keyboard navigation
-   Provide haptic feedback for touch interactions
-   Support screen reader navigation patterns

## Performance Optimizations

### Component Optimization

-   Use React.memo for pure components to prevent unnecessary re-renders
-   Implement proper key props for list items
-   Avoid inline style objects and functions in render methods

### Styling Performance

-   Leverage unistyles caching for computed styles
-   Use variant-based styling over dynamic style calculations
-   Minimize style object creation in render cycles

### Memory Management

-   Clean up event listeners and subscriptions in useEffect cleanup
-   Avoid memory leaks from retained component references
-   Use appropriate component lifecycle patterns

## Best Practices

### Component Design

-   Follow single responsibility principle for component creation
-   Use composition over inheritance for component relationships
-   Implement consistent prop naming conventions across components

### Styling Consistency

-   Use semantic props over inline styles for maintainability
-   Follow spacing system multiples of 4 for consistent rhythm
-   Implement consistent border radius and shadow patterns

### Theme Management

-   Define colors in theme configuration rather than hardcoding
-   Use semantic color names that describe purpose, not appearance
-   Maintain consistent color usage patterns across the application
