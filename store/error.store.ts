import type { App<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'types/error.types';
import { create } from 'zustand';

interface ErrorState {
  errors: Map<ErrorKey, AppError>;
  addError: (key: <PERSON><PERSON><PERSON><PERSON><PERSON>, error: AppError) => void;
  clearError: (key: <PERSON><PERSON><PERSON><PERSON><PERSON>) => void;
  clearAllErrors: () => void;
}

const useErrorStore = create<ErrorState>((set) => ({
  errors: new Map(),
  addError: (key, error) =>
    set((state) => ({
      errors: new Map(state.errors).set(key, error),
    })),
  clearError: (key) =>
    set((state) => {
      const newErrors = new Map(state.errors);
      newErrors.delete(key);
      return { errors: newErrors };
    }),
  clearAllErrors: () => set({ errors: new Map() }),
}));

export default useErrorStore;
