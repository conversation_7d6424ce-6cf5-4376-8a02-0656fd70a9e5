/**
 * @fileoverview Wallet Store - Clean Architecture Integration
 *
 * Modern wallet store that integrates with the new clean architecture.
 * This replaces the legacy wallet store with a cleaner, more maintainable implementation.
 */

import { createLogger } from '@utils/logger';
// Simplified wallet store - no complex container setup needed
import {
  deleteItemAsync as deleteItemAsyncSecureStore,
  getItemAsync as getItemAsyncSecureStore,
  setItemAsync as setItemAsyncSecureStore,
} from 'expo-secure-store';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Secure storage adapter for Zustand
const zustandSecureStorage = {
  getItem: async (name: string): Promise<string | null> => {
    try {
      const value = await getItemAsyncSecureStore(name);
      return value;
    } catch (error) {
      walletStoreLogger.error('Failed to get item from secure storage', { name, error });
      return null;
    }
  },
  setItem: async (name: string, value: string): Promise<void> => {
    try {
      await setItemAsyncSecureStore(name, value);
    } catch (error) {
      walletStoreLogger.error('Failed to set item in secure storage', { name, error });
    }
  },
  removeItem: async (name: string): Promise<void> => {
    try {
      await deleteItemAsyncSecureStore(name);
    } catch (error) {
      walletStoreLogger.error('Failed to remove item from secure storage', { name, error });
    }
  },
};

const walletStoreLogger = createLogger('WalletStore');

export const WalletType = {
  Cloud: 'cloud',
  Private: 'private',
} as const;

export type WalletType = (typeof WalletType)[keyof typeof WalletType];

export interface WalletStoreState {
  mnemonic: string | null;
  selectedWallet: WalletType;
  checkedCheckboxes: string[];

  walletAddress: string | null;
  walletId: string | null;
  isConnected: boolean;
  hasAddress: boolean;
  isInitialized: boolean;

  // Loading states
  isCreatingWallet: boolean;
  isConnectingWallet: boolean;
  isSyncing: boolean;
  isLoading: boolean;

  // Success states
  walletCreationSuccess: boolean;
  walletConnectionSuccess: boolean;
  syncSuccess: boolean;

  // Error states
  walletCreationError: string | null;
  walletConnectionError: string | null;
  syncError: string | null;
  error: string | null;

  // Sync state
  lastSyncTime: Date | null;
  hasSyncedThisSession: boolean;

  // Progress tracking
  creationProgress:
  | 'idle'
  | 'validating'
  | 'creating'
  | 'generating-address'
  | 'syncing'
  | 'complete';
}

export interface WalletStoreActions {
  setMnemonic: (mnemonic: string) => void;
  clearMnemonic: () => void;
  setSelectedWallet: (walletType: WalletType) => void;
  toggleCheckbox: (checkboxId: string) => void;
  setCheckboxSelected: (checkboxId: string, selected: boolean) => void;

  setWalletAddress: (address: string | null) => void;
  setWalletId: (id: string | null) => void;
  setIsConnected: (connected: boolean) => void;
  setHasAddress: (hasAddress: boolean) => void;
  setIsInitialized: (initialized: boolean) => void;

  // Loading state management
  setIsCreatingWallet: (creating: boolean) => void;
  setIsConnectingWallet: (connecting: boolean) => void;
  setIsSyncing: (syncing: boolean) => void;
  setIsLoading: (loading: boolean) => void;

  // Success state management
  setWalletCreationSuccess: (success: boolean) => void;
  setWalletConnectionSuccess: (success: boolean) => void;
  setSyncSuccess: (success: boolean) => void;

  // Error state management
  setWalletCreationError: (error: string | null) => void;
  setWalletConnectionError: (error: string | null) => void;
  setSyncError: (error: string | null) => void;
  setError: (error: string | null) => void;

  // Sync state management
  setLastSyncTime: (time: Date | null) => void;
  setHasSyncedThisSession: (synced: boolean) => void;

  // Progress tracking
  setCreationProgress: (progress: WalletStoreState['creationProgress']) => void;

  // Utility actions
  clearAllErrors: () => void;
  clearWalletCreationState: () => void;
  clearWalletConnectionState: () => void;
  clearSyncState: () => void;
  reset: () => void;
}

export type WalletStoreProps = WalletStoreState & WalletStoreActions;

const initialState: WalletStoreState = {
  mnemonic: null,
  selectedWallet: WalletType.Cloud,
  checkedCheckboxes: [],

  walletAddress: null,
  walletId: null,
  isConnected: false,
  hasAddress: false,
  isInitialized: false,

  // Loading states
  isCreatingWallet: false,
  isConnectingWallet: false,
  isSyncing: false,
  isLoading: false,

  // Success states
  walletCreationSuccess: false,
  walletConnectionSuccess: false,
  syncSuccess: false,

  // Error states
  walletCreationError: null,
  walletConnectionError: null,
  syncError: null,
  error: null,

  // Sync state
  lastSyncTime: null,
  hasSyncedThisSession: false,

  // Progress tracking
  creationProgress: 'idle',
};

const useWalletStore = create<WalletStoreProps>()(
  persist(
    (set) => ({
      ...initialState,

      setMnemonic: (mnemonic: string) => {
        walletStoreLogger.debug('Setting mnemonic', { hasValue: !!mnemonic });
        set({ mnemonic });
      },

      clearMnemonic: () => {
        walletStoreLogger.debug('Clearing mnemonic');
        set({ mnemonic: null });
      },

      setSelectedWallet: (walletType: WalletType) => {
        walletStoreLogger.debug('Setting selected wallet type', { walletType });
        set({ selectedWallet: walletType });
      },

      toggleCheckbox: (checkboxId: string) => {
        walletStoreLogger.debug('Toggling checkbox', { checkboxId });
        set((state) => ({
          checkedCheckboxes: state.checkedCheckboxes.includes(checkboxId)
            ? state.checkedCheckboxes.filter((id) => id !== checkboxId)
            : [...state.checkedCheckboxes, checkboxId],
        }));
      },

      setCheckboxSelected: (checkboxId: string, selected: boolean) => {
        walletStoreLogger.debug('Setting checkbox state', {
          checkboxId,
          selected,
        });
        set((state) => ({
          checkedCheckboxes: selected
            ? [...state.checkedCheckboxes, checkboxId]
            : state.checkedCheckboxes.filter((id) => id !== checkboxId),
        }));
      },

      setWalletAddress: (address: string | null) => {
        walletStoreLogger.debug('Setting wallet address', {
          hasAddress: !!address,
          addressLength: address?.length,
        });
        set({ walletAddress: address });
      },

      setWalletId: (id: string | null) => {
        walletStoreLogger.debug('Setting wallet ID', { hasId: !!id });
        set({ walletId: id });
      },

      setIsConnected: (connected: boolean) => {
        walletStoreLogger.debug('Setting connection state', { connected });
        set({ isConnected: connected });
      },

      setHasAddress: (hasAddress: boolean) => {
        walletStoreLogger.debug('Setting address state', { hasAddress });
        set({ hasAddress });
      },

      setIsInitialized: (initialized: boolean) => {
        walletStoreLogger.debug('Setting initialization state', {
          initialized,
        });
        set({ isInitialized: initialized });
      },

      // Loading state management
      setIsCreatingWallet: (creating: boolean) => {
        walletStoreLogger.debug('Setting wallet creation loading state', {
          creating,
        });
        set({ isCreatingWallet: creating });
      },

      setIsConnectingWallet: (connecting: boolean) => {
        walletStoreLogger.debug('Setting wallet connection loading state', {
          connecting,
        });
        set({ isConnectingWallet: connecting });
      },

      setIsSyncing: (syncing: boolean) => {
        walletStoreLogger.debug('Setting sync loading state', { syncing });
        set({ isSyncing: syncing });
      },

      setIsLoading: (loading: boolean) => {
        walletStoreLogger.debug('Setting general loading state', { loading });
        set({ isLoading: loading });
      },

      // Success state management
      setWalletCreationSuccess: (success: boolean) => {
        walletStoreLogger.debug('Setting wallet creation success state', {
          success,
        });
        set({ walletCreationSuccess: success });
      },

      setWalletConnectionSuccess: (success: boolean) => {
        walletStoreLogger.debug('Setting wallet connection success state', {
          success,
        });
        set({ walletConnectionSuccess: success });
      },

      setSyncSuccess: (success: boolean) => {
        walletStoreLogger.debug('Setting sync success state', { success });
        set({ syncSuccess: success });
      },

      // Error state management
      setWalletCreationError: (error: string | null) => {
        if (error) {
          walletStoreLogger.error('Setting wallet creation error', { error });
        } else {
          walletStoreLogger.debug('Clearing wallet creation error');
        }
        set({ walletCreationError: error });
      },

      setWalletConnectionError: (error: string | null) => {
        if (error) {
          walletStoreLogger.error('Setting wallet connection error', { error });
        } else {
          walletStoreLogger.debug('Clearing wallet connection error');
        }
        set({ walletConnectionError: error });
      },

      setSyncError: (error: string | null) => {
        if (error) {
          walletStoreLogger.error('Setting sync error', { error });
        } else {
          walletStoreLogger.debug('Clearing sync error');
        }
        set({ syncError: error });
      },

      setError: (error: string | null) => {
        if (error) {
          walletStoreLogger.error('Setting general error', { error });
        } else {
          walletStoreLogger.debug('Clearing general error');
        }
        set({ error });
      },

      // Sync state management
      setLastSyncTime: (time: Date | null) => {
        walletStoreLogger.debug('Setting last sync time', {
          time: time?.toISOString(),
        });
        set({ lastSyncTime: time });
      },

      setHasSyncedThisSession: (synced: boolean) => {
        walletStoreLogger.debug('Setting sync session state', { synced });
        set({ hasSyncedThisSession: synced });
      },

      // Progress tracking
      setCreationProgress: (progress: WalletStoreState['creationProgress']) => {
        walletStoreLogger.debug('Setting creation progress', { progress });
        set({ creationProgress: progress });
      },

      // Utility actions
      clearAllErrors: () => {
        walletStoreLogger.debug('Clearing all wallet errors');
        set({
          walletCreationError: null,
          walletConnectionError: null,
          syncError: null,
          error: null,
        });
      },

      clearWalletCreationState: () => {
        walletStoreLogger.debug('Clearing wallet creation state');
        set({
          isCreatingWallet: false,
          walletCreationSuccess: false,
          walletCreationError: null,
          creationProgress: 'idle',
        });
      },

      clearWalletConnectionState: () => {
        walletStoreLogger.debug('Clearing wallet connection state');
        set({
          isConnectingWallet: false,
          walletConnectionSuccess: false,
          walletConnectionError: null,
        });
      },

      clearSyncState: () => {
        walletStoreLogger.debug('Clearing sync state');
        set({
          isSyncing: false,
          syncSuccess: false,
          syncError: null,
          lastSyncTime: null,
          hasSyncedThisSession: false,
        });
      },

      reset: () => {
        walletStoreLogger.operation('Resetting entire wallet state');
        set(initialState);
      },
    }),
    {
      name: 'wallet-store',
      storage: createJSONStorage(() => zustandSecureStorage),
      partialize: (state) => ({
        mnemonic: state.mnemonic,
        selectedWallet: state.selectedWallet,
        checkedCheckboxes: state.checkedCheckboxes,
        walletAddress: state.walletAddress,
        walletId: state.walletId,
        isConnected: state.isConnected,
        hasAddress: state.hasAddress,
        isInitialized: state.isInitialized,
        // Note: lastSyncTime is excluded to avoid Date serialization issues
        hasSyncedThisSession: state.hasSyncedThisSession,
      }),
      onRehydrateStorage: () => {
        walletStoreLogger.debug('Rehydrating wallet store from storage');
        return (state, error) => {
          if (error) {
            walletStoreLogger.error('Failed to rehydrate wallet store', error);
          } else {
            walletStoreLogger.success('Wallet store rehydrated successfully', {
              hasWalletAddress: !!state?.walletAddress,
              selectedWallet: state?.selectedWallet,
              isConnected: state?.isConnected,
            });
          }
        };
      },
    }
  )
);

export default useWalletStore;
