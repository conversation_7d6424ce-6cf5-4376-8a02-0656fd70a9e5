import type { Target } from '@endpoints/saving-plan.endpoints';
import {
  deleteItemAsync as deleteItemAsyncSecureStore,
  getItemAsync as getItemAsyncSecureStore,
  setItemAsync as setItemAsyncSecureStore,
} from 'expo-secure-store';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const STORAGE_NAME = 'saving-plan-store';

export type Frequency = 'W' | 'M' | 'Q';

export const PlanType = {
  Percentage: 'PR',
  Amount: 'AM',
  ReccuringPurchase: 'RP',
} as const;

export type PlanType = (typeof PlanType)[keyof typeof PlanType];

export interface SavingPlanStoreState {
  planName?: string;
  adjustedPlanOption?: string;
  adjustedPlanPercentage: number;
  fixedPlanValue: number;
  selectedTarget?: Target;
  timeHorizon: number;
  ignoreTransactionsAbove: number;
  savingAmountAndFrequency: {
    amount: number;
    frequency: Frequency | null;
  };
  selectedPlan?: PlanType;
}

export interface SavingPlanStoreActions {
  setAdjustedPlanPercentage: (percentage: number) => void;
  setFixedPlanValue: (value: number) => void;
  setPlanName: (planName: string) => void;
  setTarget: (target: Target) => void;
  setTimeHorizon: (timeHorizon: number) => void;
  setIgnoreTransactionsAbove: (amount: number) => void;
  setSavingAmountAndFrequency: (data: {
    amount: number;
    frequency: Frequency | null;
  }) => void;
  setSelectedPlan: (planType: PlanType) => void;
  setAdjustedPlanOption: (adjustedPlanOptionName: string) => void;
}

export type SavingPlanStoreProps = SavingPlanStoreState &
  SavingPlanStoreActions;

const zustandSecureStorage = {
  getItem: (name: string): Promise<string | null> => {
    return getItemAsyncSecureStore(name);
  },
  setItem: (name: string, value: string): Promise<void> => {
    return setItemAsyncSecureStore(name, value);
  },
  removeItem: (name: string): Promise<void> => {
    return deleteItemAsyncSecureStore(name);
  },
};

const useSavingPlanStore = create<SavingPlanStoreProps>()(
  persist(
    (set) => ({
      planName: undefined,
      adjustedPlanOption: undefined,
      selectedPlan: undefined,
      selectedTarget: undefined,
      timeHorizon: 1,
      ignoreTransactionsAbove: 0,
      savingAmountAndFrequency: {
        amount: 0,
        frequency: null,
      },
      adjustedPlanPercentage: 2,
      setAdjustedPlanPercentage: (percentage: number) => {
        set({ adjustedPlanPercentage: percentage });
      },
      fixedPlanValue: 5,
      setFixedPlanValue: (value: number) => {
        set({ fixedPlanValue: value });
      },
      setPlanName: (planName: string) => {
        set({ planName });
      },
      setSelectedPlan: (planType: PlanType) => {
        set({ selectedPlan: planType });
      },
      setTarget: (target: Target) => {
        set({ selectedTarget: target });
      },
      setAdjustedPlanOption: (adjustedPlanOptionName: string) => {
        set({ adjustedPlanOption: adjustedPlanOptionName });
      },
      setTimeHorizon: (timeHorizon) => set({ timeHorizon }),
      setIgnoreTransactionsAbove: (amount) =>
        set({ ignoreTransactionsAbove: amount }),
      setSavingAmountAndFrequency: (data) =>
        set({ savingAmountAndFrequency: data }),
    }),
    {
      name: STORAGE_NAME,
      storage: createJSONStorage(() => zustandSecureStorage),
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => !key.startsWith('set'))
        ),
    }
  )
);

export const clearSavingPlanStore = () => {
  useSavingPlanStore.persist.clearStorage();
};

export default useSavingPlanStore;
