import type { TextVariants } from 'components/ui/typography/text';
import { create } from 'zustand';

interface NotificationState {
  isModalVisible: boolean;
  modalMessage: string | null;
  bottomButtonLabel?: string | null;
  onBottomButtonPress?: (() => void) | null;
  fontSize?: TextVariants['size'];
  showModal: (
    message: string,
    options?: {
      bottomButtonLabel?: string;
      onBottomButtonPress?: () => void;
      fontSize?: TextVariants['size'];
    }
  ) => void;
  hideModal: () => void;
}

const useNotificationStore = create<NotificationState>((set) => ({
  isModalVisible: false,
  modalMessage: null,
  bottomButtonLabel: null,
  onBottomButtonPress: null,
  showModal: (modalMessage, options) =>
    set({
      isModalVisible: true,
      modalMessage,
      bottomButtonLabel: options?.bottomButtonLabel ?? null,
      onBottomButtonPress: options?.onBottomButtonPress ?? null,
      fontSize: options?.fontSize ?? undefined,
    }),
  hideModal: () =>
    set({
      isModalVisible: false,
      modalMessage: null,
      bottomButtonLabel: null,
      onBottomButtonPress: null,
    }),
}));

export default useNotificationStore;
