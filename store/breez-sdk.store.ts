/**
 * Consolidated Breez SDK Store
 * Direct SDK integration without service layer
 */

import {
  addEventListener,
  connect,
  defaultConfig,
  disconnect,
  fetchLightningLimits,
  fetchOnchainLimits,
  getInfo,
  listPayments,
  type Payment,
  PaymentMethod,
  type PrepareReceiveRequest,
  prepareReceivePayment,
  type ReceivePaymentRequest,
  receivePayment,
  removeEventListener,
  type SdkEvent,
  SdkEventVariant,
  sync,
  type WalletInfo,
} from '@breeztech/react-native-breez-sdk-liquid';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import walletCacheService from '@services/wallet-cache.service';
import { breezAuthGuard } from '@utils/breez/auth-guard';
import { breezStoreLogger } from '@utils/logger';
import {
  BREEZ_ERROR_MESSAGES,
  BREEZ_LOADING_MESSAGES,
} from '@utils/wallet/constants';
import {
  DEVELOPMENT_CONFIG,
  getBreezSdkNetwork,
  logNetworkConfiguration,
  validateNetworkConsistency,
} from 'config/network.config';
import { deleteItemAsync, getItemAsync, setItemAsync } from 'expo-secure-store';
import type {
  BreezConnectionInfo,
  BreezPaymentLimits,
  BreezServiceStats,
  CreateInvoiceRequest,
  CreateInvoiceResponse,
} from 'types/breez-sdk.types';
import { BreezConnectionState as ConnectionState } from 'types/breez-sdk.types';
import type { PriceData, SupportedCurrency } from 'types/exchange-rate.types';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Essential utilities
// Regex declared at top-level per lint rules
const LIQUID_NETWORK_URI_PREFIX = 'liquidnetwork:';
const LIQUID_NETWORK_REGEX = /liquidnetwork:([^?]+)/;

const getApiKey = (): string | undefined => {
  // 1) Try standard env (may be available in native env or CI)
  const direct =
    (process.env.BREEZ_API_KEY as string | undefined) ||
    (process.env.EXPO_PUBLIC_BREEZ_API_KEY as string | undefined);
  if (direct && typeof direct === 'string' && direct.trim().length > 0) {
    return direct.trim();
  }

  // 2) Try @env (react-native-dotenv). Note: plugin path is configured to '.env'.
  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const env: any = require('@env');
    const fromEnv =
      (env?.BREEZ_API_KEY as string | undefined) ||
      (env?.EXPO_PUBLIC_BREEZ_API_KEY as string | undefined);
    if (fromEnv && typeof fromEnv === 'string' && fromEnv.trim().length > 0) {
      return fromEnv.trim();
    }
  } catch (_) {
    // ignore – @env might not be configured or no .env file exists
  }

  return;
};

// Simple operation execution with loading
const executeWithLoading = async <T>(
  operation: () => Promise<T>,
  setLoading: (loading: boolean, message?: string) => void,
  loadingMessage?: string
): Promise<{ success: boolean; data?: T; error?: string }> => {
  try {
    setLoading(true, loadingMessage);
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  } finally {
    setLoading(false);
  }
};

const WalletCreationStatus = {
  IDLE: 'IDLE',
  MNEMONIC_STORED: 'MNEMONIC_STORED',
  CONNECTING_BREEZ: 'CONNECTING_BREEZ',
  GENERATING_ADDRESS: 'GENERATING_ADDRESS',
  SYNCING_BACKEND: 'SYNCING_BACKEND',
  COMPLETE: 'COMPLETE',
  ERROR: 'ERROR',
} as const;

type WalletCreationStatus =
  (typeof WalletCreationStatus)[keyof typeof WalletCreationStatus];

// Store State Interface
interface BreezSdkStoreState {
  // Connection State
  connectionInfo: BreezConnectionInfo;
  isInitialized: boolean;
  isConnecting: boolean;

  // Wallet State
  walletInfo: WalletInfo | null;
  isConnected: boolean;
  lastSyncAt: number | null;

  // Payment State
  pendingPayments: Payment[];
  recentPayments: Payment[];
  paymentLimits: BreezPaymentLimits | null;

  // Service State
  serviceStats: BreezServiceStats | null;
  lastError: string | null;

  // UI State
  isLoading: boolean;
  loadingOperation: string | null;
  isSyncing: boolean;

  // Auto-connection state
  autoConnectionAttempts: number;
  lastAutoConnectionAttempt: number | null;
  isAutoConnecting: boolean;

  // Wallet creation state
  walletCreationStatus: WalletCreationStatus;
  walletCreationProgress: string | null;
  walletCreationError: string | null;
  generatedWalletAddress: string | null;

  // Exchange rate state
  exchangeRateData: PriceData | null;
  selectedCurrency: SupportedCurrency;
  exchangeRateError: string | null;
  exchangeRateLastUpdate: number | null;

  // Background creation state
  backgroundCreationStatus: WalletCreationStatus;
  backgroundCreationProgress: string | null;
  backgroundCreationError: string | null;
  backgroundGeneratedAddress: string | null;
  // Temporary callback to store address in backend after generation
  backgroundAddressStoreCallback:
  | ((address: string, type?: string, label?: string) => Promise<boolean>)
  | null;

  // Payment monitoring
  paymentMonitoringInterval: NodeJS.Timeout | undefined;
}

// Store Actions Interface
interface BreezSdkStoreActions {
  // Initialization
  initialize: (config?: {
    network?: string;
    enableLogging?: boolean;
  }) => Promise<boolean>;

  // Connection Management
  connect: (mnemonic?: string) => Promise<boolean>;
  disconnect: () => Promise<boolean>;
  reconnect: (mnemonic?: string) => Promise<boolean>;
  forceReconnect: (mnemonic?: string) => Promise<boolean>;
  // State helpers
  isSdkConnected: () => boolean;

  // Wallet Operations
  refreshWalletInfo: (options?: { skipSync?: boolean }) => Promise<boolean>;
  fastRefresh: () => Promise<boolean>;

  // Payment Operations
  createInvoice: (
    request: CreateInvoiceRequest
  ) => Promise<CreateInvoiceResponse | null>;
  refreshPaymentLimits: () => Promise<boolean>;

  // Payment History
  loadPayments: (filters?: {
    types?: string[];
    states?: string[];
    limit?: number;
    offset?: number;
  }) => Promise<Payment[]>;

  addPayment: (payment: Payment) => void;
  updatePayment: (payment: Payment) => void;
  clearPayments: () => void;

  // Wallet Creation
  createWallet: (mnemonic: string) => Promise<boolean>;
  generateAndStoreWalletAddress: () => Promise<string | null>;
  getStoredWalletAddress: () => Promise<string | null>;

  // State Management
  setLoading: (loading: boolean, operation?: string) => void;
  clearError: () => void;
  updateConnectionInfo: (info: Partial<BreezConnectionInfo>) => void;
  updateWalletInfo: (info: WalletInfo) => void;

  // Auto-connection Management
  startAutoConnection: () => Promise<void>;
  stopAutoConnection: () => void;
  retryAutoConnection: () => Promise<boolean>;

  // Exchange Rate Management
  updateExchangeRate: (data: PriceData) => void;
  setSelectedCurrency: (currency: SupportedCurrency) => void;
  convertSatoshisToCurrency: (
    satoshis: number,
    currency: SupportedCurrency
  ) => number;

  // Background Wallet Creation
  startBackgroundWalletCreation: (
    mnemonic: string,
    options?: {
      storeWalletAddress?: (
        address: string,
        type?: string,
        label?: string
      ) => Promise<boolean>;
    }
  ) => Promise<void>;

  getBackgroundCreationStatus: () => {
    status: WalletCreationStatus;
    progress: string | null;
    error: string | null;
    generatedAddress: string | null;
  };
  clearBackgroundCreationState: () => void;

  // Additional State Management
  resetToInitialState: () => void;

  // Wallet Deletion / Cleanup
  deleteWallet: (options?: { preserveAuth?: boolean }) => Promise<boolean>;

  // Validation and Recovery
  validateState: () => boolean;
  recoverState: (options?: { forceReset?: boolean }) => Promise<boolean>;
  forceStateReset: () => void;

  // Health Check
  isHealthy: () => boolean;
}

type BreezSdkStore = BreezSdkStoreState & BreezSdkStoreActions;

// Initial State
const initialState: BreezSdkStoreState = {
  connectionInfo: {
    state: ConnectionState.DISCONNECTED,
    retryCount: 0,
    isInitialized: false,
  },
  isInitialized: false,
  isConnecting: false,
  walletInfo: null,
  isConnected: false,
  lastSyncAt: null,
  pendingPayments: [],
  recentPayments: [],
  paymentLimits: null,
  serviceStats: null,
  lastError: null,
  isLoading: false,
  loadingOperation: null,
  isSyncing: false,
  autoConnectionAttempts: 0,
  lastAutoConnectionAttempt: null,
  isAutoConnecting: false,
  walletCreationStatus: WalletCreationStatus.IDLE,
  walletCreationProgress: null,
  walletCreationError: null,
  generatedWalletAddress: null,
  exchangeRateData: null,
  selectedCurrency: 'USD' as SupportedCurrency,
  exchangeRateError: null,
  exchangeRateLastUpdate: null,
  backgroundCreationStatus: WalletCreationStatus.IDLE,
  backgroundCreationProgress: null,
  backgroundCreationError: null,
  backgroundGeneratedAddress: null,
  // No-op default for optional callback
  backgroundAddressStoreCallback: null,
  paymentMonitoringInterval: undefined,
};

// Create Store
const useBreezSdkStore = create<BreezSdkStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Initialization
    initialize: async (_config) => {
      // Prefer proceeding even if not authenticated: initialization is local-only
      if (!breezAuthGuard.guardBreezOperation('initialize')) {
        breezStoreLogger.warn(
          'Initialize: proceeding without authenticated session (local SDK init)'
        );
        // continue
      }

      // Prevent concurrent initialization
      if (sdkInitializationInProgress) {
        breezStoreLogger.debug(
          '🔄 SDK initialization already in progress, waiting...'
        );
        return false;
      }

      const state = get();
      if (state.isInitialized) {
        breezStoreLogger.debug('✅ Breez SDK already initialized');
        return true;
      }

      const { setLoading, clearError } = get();
      clearError();

      try {
        sdkInitializationInProgress = true;
        breezStoreLogger.operation('🚀 Initializing Breez SDK');

        const result = await executeWithLoading(
          async () => {
            const apiKey = getApiKey();
            if (!apiKey) {
              throw new Error('BREEZ_API_KEY environment variable is required');
            }

            // Use centralized network configuration
            const networkConfig = getBreezSdkNetwork();

            // Debug the network configuration being passed to SDK
            breezStoreLogger.debug('🌐 Breez SDK Network Config:', {
              networkConfig,
              networkConfigType: typeof networkConfig,
              networkConfigValue: networkConfig,
            });

            const sdkConfig = await defaultConfig(networkConfig);
            sdkConfig.breezApiKey = apiKey;

            // Log network configuration for debugging
            if (DEVELOPMENT_CONFIG.ENABLE_NETWORK_LOGGING) {
              logNetworkConfiguration();
            }

            // Validate network consistency
            if (DEVELOPMENT_CONFIG.VALIDATE_ON_STARTUP) {
              const validation = validateNetworkConsistency();
              if (!validation.isValid) {
                breezStoreLogger.error(
                  '❌ Network configuration validation failed:',
                  validation.errors
                );
                if (DEVELOPMENT_CONFIG.STRICT_VALIDATION) {
                  throw new Error(
                    `Network validation failed: ${validation.errors.join(', ')}`
                  );
                }
              }
              if (validation.warnings.length > 0) {
                breezStoreLogger.warn(
                  '⚠️ Network configuration warnings:',
                  validation.warnings
                );
              }
            }

            // Store config for later use
            await setItemAsync(
              SecureStorageKeys.BREEZ_SERVICE_CONFIG,
              JSON.stringify(sdkConfig)
            );

            return {
              isInitialized: true,
              connectionInfo: {
                state: ConnectionState.DISCONNECTED,
                retryCount: 0,
                isInitialized: true,
              },
            };
          },
          setLoading,
          BREEZ_LOADING_MESSAGES.INITIALIZING
        );

        if (result.success && result.data) {
          set(result.data);
          breezStoreLogger.success('✅ Breez SDK initialized successfully');
          return true;
        }

        set({
          lastError: result.error || BREEZ_ERROR_MESSAGES.INITIALIZATION_FAILED,
        });
        breezStoreLogger.error(
          '❌ Breez SDK initialization failed:',
          result.error
        );
        return false;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Initialization failed';
        set({ lastError: errorMessage });
        breezStoreLogger.error('❌ Breez SDK initialization error:', error);
        return false;
      } finally {
        sdkInitializationInProgress = false;
      }
    },

    // Connection Management
    connect: async (mnemonic) => {
      // Prefer proceeding even if not authenticated: local SDK connection
      if (!breezAuthGuard.guardBreezOperation('connect')) {
        breezStoreLogger.warn(
          'Connect: proceeding without authenticated session (local SDK connect)'
        );
        // continue
      }

      const state = get();

      // Guard: if already connected or connecting, skip duplicate connect
      if (state.isConnected) {
        breezStoreLogger.debug('connect() skipped: already connected');
        return true;
      }
      if (state.isConnecting) {
        breezStoreLogger.debug('connect() skipped: connection in progress');
        return false;
      }

      const { setLoading, clearError } = get();
      setLoading(true, BREEZ_LOADING_MESSAGES.CONNECTING);
      clearError();

      try {
        set({
          isConnecting: true,
          connectionInfo: {
            ...state.connectionInfo,
            state: ConnectionState.CONNECTING,
          },
        });

        breezStoreLogger.operation('Attempting to connect to Breez SDK');

        // Get stored config
        const configStr = await getItemAsync(
          SecureStorageKeys.BREEZ_SERVICE_CONFIG
        );
        if (!configStr) {
          throw new Error('SDK not initialized');
        }

        const config = JSON.parse(configStr);

        // Get mnemonic
        const storedMnemonic =
          mnemonic || (await getItemAsync(SecureStorageKeys.MNEMONIC));
        if (!storedMnemonic) {
          throw new Error('No mnemonic available for connection');
        }

        // Direct SDK connection using mnemonic (SDK derives seed internally)
        breezStoreLogger.debug('🔑 Connecting using mnemonic');
        await connect({ config, mnemonic: storedMnemonic });

        // Get wallet info
        const infoResponse = await getInfo();
        breezStoreLogger.debug('📥 Connection wallet info:', {
          hasWalletInfo: !!infoResponse.walletInfo,
          balanceSat: infoResponse.walletInfo?.balanceSat,
        });

        set({
          isConnected: true,
          isConnecting: false,
          connectionInfo: {
            state: ConnectionState.CONNECTED,
            retryCount: 0,
            isInitialized: true,
          },
          walletInfo: infoResponse.walletInfo,
          lastSyncAt: Date.now(),
        });

        breezStoreLogger.success('✅ Successfully connected to Breez SDK');

        // Trigger automatic refresh after connection
        setTimeout(async () => {
          try {
            const {
              refreshWalletInfo,
              loadPayments,
              getStoredWalletAddress,
              generateAndStoreWalletAddress,
            } = get();

            await refreshWalletInfo();
            await loadPayments({ limit: 50 });

            // Ensure wallet address exists for display
            try {
              const existingAddress = await getStoredWalletAddress();
              if (existingAddress) {
                breezStoreLogger.debug(
                  'Stored wallet address found after connect',
                  {
                    addressPrefix: `${existingAddress.substring(0, 10)}...`,
                  }
                );
              } else {
                breezStoreLogger.warn(
                  'No stored wallet address found after connect; generating a new address'
                );
                await generateAndStoreWalletAddress();
              }
            } catch (addrErr) {
              breezStoreLogger.warn(
                'Failed to verify/generate wallet address after connect',
                addrErr
              );
            }

            breezStoreLogger.debug(
              '🔄 Post-connection wallet and payments refresh completed'
            );
          } catch (error) {
            breezStoreLogger.warn('⚠️ Post-connection refresh failed:', error);
          }
        }, 800);

        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Connection failed';

        set({
          isConnected: false,
          isConnecting: false,
          connectionInfo: {
            state: ConnectionState.DISCONNECTED,
            retryCount: get().connectionInfo.retryCount + 1,
            isInitialized: true,
          },
          lastError: errorMessage,
        });

        breezStoreLogger.error('Connection failed', error);
        return false;
      } finally {
        setLoading(false);
      }
    },

    disconnect: async () => {
      const { setLoading } = get();
      setLoading(true, BREEZ_LOADING_MESSAGES.DISCONNECTING);

      try {
        // Clean up Breez SDK event listener to avoid duplicates
        if (sdkListenerId) {
          try {
            await removeEventListener(sdkListenerId);
          } catch (e) {
            breezStoreLogger.warn(
              'Failed to remove SDK event listener on disconnect',
              e
            );
          } finally {
            sdkListenerId = null;
          }
        }

        await disconnect();

        // Clear any stored address to prevent stale display on wallet switch
        try {
          await deleteItemAsync(SecureStorageKeys.BREEZ_WALLET_ADDRESS);
        } catch (e) {
          breezStoreLogger.warn(
            'Failed to clear stored wallet address on disconnect',
            e
          );
        }

        set({
          isConnected: false,
          connectionInfo: {
            state: ConnectionState.DISCONNECTED,
            retryCount: 0,
            isInitialized: true,
          },
          walletInfo: null,
          pendingPayments: [],
          recentPayments: [],
          generatedWalletAddress: null,
        });

        breezStoreLogger.success('Successfully disconnected from Breez SDK');
        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Disconnection failed';
        set({ lastError: errorMessage });
        breezStoreLogger.error('Disconnection failed', error);
        return false;
      } finally {
        setLoading(false);
      }
    },

    reconnect: async (mnemonic) => {
      // Check authentication before reconnecting
      if (!breezAuthGuard.guardBreezOperation('reconnect')) {
        breezStoreLogger.warn(
          'Reconnect operation blocked: user not authenticated'
        );
        return false;
      }

      const { disconnect: disconnectAction, connect: connectAction } = get();
      await disconnectAction();
      return await connectAction(mnemonic);
    },

    forceReconnect: async (mnemonic) => {
      // Check authentication before force reconnecting
      if (!breezAuthGuard.guardBreezOperation('forceReconnect')) {
        breezStoreLogger.warn(
          'Force reconnect operation blocked: user not authenticated'
        );
        return false;
      }

      const { reconnect } = get();
      return await reconnect(mnemonic);
    },

    // Wallet Operations
    refreshWalletInfo: async (options) => {
      try {
        breezStoreLogger.debug('🔄 Starting wallet info refresh');

        const state = get();

        const { setLoading } = get();
        setLoading(true, 'Refreshing wallet info...');
        breezStoreLogger.debug('📊 Current state before refresh:', {
          isConnected: state.isConnected,
          isInitialized: state.isInitialized,
          hasWalletInfo: !!state.walletInfo,
          currentBalance: state.walletInfo?.balanceSat,
        });

        if (!state.isConnected) {
          breezStoreLogger.warn(
            '⚠️ Cannot refresh wallet info: not connected to SDK'
          );
          return false;
        }

        const shouldSync = !options?.skipSync;

        if (shouldSync) {
          breezStoreLogger.debug('🔄 Syncing wallet data...');
          await sync();
          breezStoreLogger.debug('🔄 Wallet data sync completed');
        } else {
          breezStoreLogger.debug('⏭️ Skipping sync (skipSync option set)');
        }

        const infoResponse = await getInfo();
        breezStoreLogger.debug('📥 Raw SDK response:', {
          hasWalletInfo: !!infoResponse.walletInfo,
          balanceSat: infoResponse.walletInfo?.balanceSat,
          pendingSendSat: infoResponse.walletInfo?.pendingSendSat,
          pendingReceiveSat: infoResponse.walletInfo?.pendingReceiveSat,
          fingerprint: infoResponse.walletInfo?.fingerprint,
        });

        if (!infoResponse.walletInfo) {
          breezStoreLogger.error('❌ SDK returned null walletInfo');
          return false;
        }

        const walletInfo = infoResponse.walletInfo;

        // Validate balance data
        if (typeof walletInfo.balanceSat !== 'number') {
          breezStoreLogger.error('❌ Invalid balance data:', {
            balanceSat: walletInfo.balanceSat,
            type: typeof walletInfo.balanceSat,
          });
          return false;
        }

        set({
          walletInfo,
          lastSyncAt: Date.now(),
        });

        breezStoreLogger.success('✅ Wallet info refreshed successfully:', {
          balanceSat: walletInfo.balanceSat,
          balanceBTC: (walletInfo.balanceSat / 100_000_000).toFixed(8),
          pendingSendSat: walletInfo.pendingSendSat,
          pendingReceiveSat: walletInfo.pendingReceiveSat,
        });

        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to refresh wallet info';
        set({ lastError: errorMessage });
        breezStoreLogger.error('❌ Failed to refresh wallet info', error);
        return false;
      } finally {
        const { setLoading } = get();
        setLoading(false);
      }
    },

    fastRefresh: async () => {
      const { refreshWalletInfo: doRefreshWalletInfo } = get();
      return await doRefreshWalletInfo({ skipSync: true });
    },

    // Payment Operations
    createInvoice: async (request) => {
      // Check authentication before creating invoice
      if (!breezAuthGuard.guardBreezOperation('createInvoice')) {
        breezStoreLogger.warn(
          'Create invoice operation blocked: user not authenticated'
        );
        return null;
      }

      const { setLoading, clearError } = get();
      setLoading(true, BREEZ_LOADING_MESSAGES.CREATING_INVOICE);
      clearError();

      try {
        const paymentMethod = request.paymentMethod || PaymentMethod.LIGHTNING;

        const prepareRequest: PrepareReceiveRequest = {
          paymentMethod,
        };

        const prepareResponse = await prepareReceivePayment(prepareRequest);

        const receiveRequest: ReceivePaymentRequest = {
          prepareResponse,
          description: request.description || '',
        };

        const receiveResponse = await receivePayment(receiveRequest);

        const result: CreateInvoiceResponse = {
          destination: receiveResponse.destination,
          paymentMethod,
          amountSat: request.amountSat,
          feesSat: prepareResponse.feesSat || 0,
        };

        breezStoreLogger.success('Invoice created successfully');
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to create invoice';
        set({ lastError: errorMessage });
        breezStoreLogger.error('Failed to create invoice', error);
        return null;
      } finally {
        setLoading(false);
      }
    },

    refreshPaymentLimits: async () => {
      try {
        const lightningLimits = await fetchLightningLimits();
        const onchainLimits = await fetchOnchainLimits();

        const paymentLimits: BreezPaymentLimits = {
          lightning: lightningLimits,
          onchain: onchainLimits,
          lastUpdatedAt: Date.now(),
        };

        set({ paymentLimits });
        return true;
      } catch (error) {
        breezStoreLogger.error('Failed to refresh payment limits', error);
        return false;
      }
    },

    // Payment History
    loadPayments: async (filters) => {
      try {
        // Call listPayments with proper parameters
        const payments = await listPayments({
          limit: filters?.limit,
          offset: filters?.offset,
        });

        // Ensure newest-first ordering for UI consistency
        const sorted = [...payments].sort((a, b) => {
          const ta = (a.timestamp ?? 0) as number;
          const tb = (b.timestamp ?? 0) as number;
          return tb - ta;
        });

        set({
          recentPayments: sorted,
          lastSyncAt: Date.now(),
        });

        return sorted;
      } catch (error) {
        breezStoreLogger.error('Failed to load payments', error);
        return [];
      }
    },

    addPayment: (payment) => {
      const { recentPayments } = get();
      const existingIndex = recentPayments.findIndex(
        (p) => p.txId === payment.txId
      );

      if (existingIndex >= 0) {
        // Update existing payment
        const updatedPayments = [...recentPayments];
        updatedPayments[existingIndex] = payment;
        set({ recentPayments: updatedPayments });
      } else {
        // Add new payment
        set({
          recentPayments: [payment, ...recentPayments].slice(0, 100), // Keep last 100
        });
      }
    },

    updatePayment: (payment) => {
      const { addPayment } = get();
      addPayment(payment);
    },

    clearPayments: () => {
      set({
        recentPayments: [],
        pendingPayments: [],
      });
    },

    // Wallet Creation
    createWallet: async (mnemonic) => {
      try {
        set({
          walletCreationStatus: WalletCreationStatus.MNEMONIC_STORED,
          walletCreationProgress: 'Storing mnemonic...',
        });

        // Store mnemonic securely
        await setItemAsync(SecureStorageKeys.MNEMONIC, mnemonic);

        set({
          walletCreationStatus: WalletCreationStatus.CONNECTING_BREEZ,
          walletCreationProgress: 'Connecting to Breez SDK...',
        });

        // Reconnect to Breez SDK with the new wallet
        const { reconnect } = get();
        const connected = await reconnect(mnemonic);

        if (!connected) {
          throw new Error('Failed to connect to Breez SDK');
        }

        set({
          walletCreationStatus: WalletCreationStatus.COMPLETE,
          walletCreationProgress: 'Wallet ready!',
        });

        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Wallet creation failed';
        set({
          walletCreationStatus: WalletCreationStatus.ERROR,
          walletCreationError: errorMessage,
        });
        breezStoreLogger.error('Wallet creation failed', error);
        return false;
      }
    },

    generateAndStoreWalletAddress: async () => {
      // Check authentication before generating wallet address
      if (
        !breezAuthGuard.guardBreezOperation('generateAndStoreWalletAddress')
      ) {
        breezStoreLogger.warn(
          'Generate wallet address operation blocked: user not authenticated'
        );
        return null;
      }

      const state = get();

      if (!state.isConnected) {
        breezStoreLogger.error('Cannot generate address: not connected');
        return null;
      }

      try {
        breezStoreLogger.operation('Generating and storing wallet address');

        // Use the createInvoice method to generate a Liquid address
        const { createInvoice } = get();
        const result = await createInvoice({
          amountSat: 0, // No amount generates a plain Liquid address
          description: 'Wallet Address',
          paymentMethod: PaymentMethod.LIQUID_ADDRESS,
        });

        if (result?.destination) {
          let address = result.destination;

          // If it's a BIP21 URI, extract just the address part

          if (address.startsWith(LIQUID_NETWORK_URI_PREFIX)) {
            const addressMatch = address.match(LIQUID_NETWORK_REGEX);
            if (addressMatch) {
              address = addressMatch[1];
            }
          }

          // Store the address
          await setItemAsync(SecureStorageKeys.BREEZ_WALLET_ADDRESS, address);
          set({ generatedWalletAddress: address });

          breezStoreLogger.success(
            'Wallet address generated and stored successfully',
            { address }
          );
          return address;
        }

        throw new Error('No address returned from invoice creation');
      } catch (error) {
        breezStoreLogger.error('Failed to generate wallet address', error);
        return null;
      }
    },

    getStoredWalletAddress: async () => {
      try {
        const address = await getItemAsync(
          SecureStorageKeys.BREEZ_WALLET_ADDRESS
        );
        return address;
      } catch (error) {
        breezStoreLogger.error('Failed to get stored wallet address', error);
        return null;
      }
    },

    // State Management
    setLoading: (loading, operation) => {
      set({
        isLoading: loading,
        loadingOperation: loading ? operation || null : null,
      });
    },

    clearError: () => {
      set({ lastError: null });
    },

    updateConnectionInfo: (info) => {
      const { connectionInfo } = get();
      set({
        connectionInfo: { ...connectionInfo, ...info },
      });
    },

    updateWalletInfo: (info) => {
      set({
        walletInfo: info,
        lastSyncAt: Date.now(),
      });
    },

    // Auto-connection Management
    startAutoConnection: async () => {
      // Check authentication before starting auto-connection
      if (!breezAuthGuard.guardBreezOperation('startAutoConnection')) {
        breezStoreLogger.warn(
          'Start auto-connection blocked: user not authenticated'
        );
        return;
      }

      const state = get();
      if (state.isAutoConnecting || state.isConnected) {
        return;
      }

      set({ isAutoConnecting: true });

      try {
        const storedMnemonic = await getItemAsync(SecureStorageKeys.MNEMONIC);
        if (storedMnemonic) {
          const { connect: connectAction } = get();
          await connectAction(storedMnemonic);
        }
      } catch (error) {
        breezStoreLogger.error('Auto-connection failed', error);
      } finally {
        set({ isAutoConnecting: false });
      }
    },

    stopAutoConnection: () => {
      set({ isAutoConnecting: false });
    },

    retryAutoConnection: async () => {
      // Check authentication before retrying auto-connection
      if (!breezAuthGuard.guardBreezOperation('retryAutoConnection')) {
        breezStoreLogger.warn(
          'Retry auto-connection blocked: user not authenticated'
        );
        return false;
      }

      const { startAutoConnection } = get();
      await startAutoConnection();
      return get().isConnected;
    },

    updateExchangeRate: (data) => {
      set({
        exchangeRateData: data,
        exchangeRateLastUpdate: Date.now(),
        exchangeRateError: null,
      });
    },

    setSelectedCurrency: (currency) => {
      set({ selectedCurrency: currency });
    },

    // Validation and Recovery (simplified)
    validateState: () => {
      // Simple state validation - just check if we're connected and have wallet info
      const state = get();
      return state.isConnected && state.walletInfo !== null;
    },

    recoverState: async (_options) => {
      // Simple recovery - try to reconnect
      const { reconnect } = get();
      return await reconnect();
    },

    forceStateReset: () => {
      // Reset to initial state
      set({
        ...initialState,
        // Keep authentication-related state
        isInitialized: get().isInitialized,
      });
    },

    // Currency Conversion
    convertSatoshisToCurrency: (satoshis, currency) => {
      const state = get();
      if (!state.exchangeRateData?.bitcoin[currency]) {
        return 0;
      }
      const btcAmount = satoshis / 100_000_000; // Convert satoshis to BTC
      return btcAmount * state.exchangeRateData.bitcoin[currency];
    },

    // Background Wallet Creation (fast, linear)
    startBackgroundWalletCreation: async (mnemonic, options) => {
      breezStoreLogger.operation('Starting background wallet creation');

      set({
        backgroundCreationStatus: WalletCreationStatus.CONNECTING_BREEZ,
        backgroundCreationProgress: 'Preparing wallet connection...',
        backgroundCreationError: null,
        backgroundGeneratedAddress: null,
        backgroundAddressStoreCallback: options?.storeWalletAddress || null,
      });

      const state = get();

      try {
        // 1) Ensure SDK is initialized
        if (!state.isInitialized) {
          const initialized = await state.initialize();
          if (!initialized) throw new Error('Failed to initialize Breez SDK');
        }

        const configStr = await getItemAsync(
          SecureStorageKeys.BREEZ_SERVICE_CONFIG
        );
        if (!configStr)
          throw new Error(
            'SDK initialization incomplete: config not available'
          );

        // 2) Disconnect any active session for a clean start
        set({
          backgroundCreationProgress: 'Disconnecting previous session...',
        });
        await state.disconnect();

        // 3) Connect to Breez SDK using the new mnemonic FIRST
        set({ backgroundCreationProgress: 'Connecting wallet...' });
        const connected = await state.connect(mnemonic);
        if (!connected) throw new Error('Failed to connect to Breez SDK');

        // 4) Now (re)attach SDK event listeners after a successful connection
        await forceEventListenerReset();

        // 5) Immediately generate and store wallet address (local operation)
        set({
          backgroundCreationStatus: WalletCreationStatus.GENERATING_ADDRESS,
          backgroundCreationProgress: 'Generating wallet address...',
        });

        const address = await state.generateAndStoreWalletAddress();
        if (!address) throw new Error('Failed to generate wallet address');
        set({ backgroundGeneratedAddress: address });

        const cb = state.backgroundAddressStoreCallback;
        if (cb) {
          try {
            await cb(address, 'liquid', 'Primary Wallet');
          } catch (cbError) {
            breezStoreLogger.warn(
              'Failed to store address via callback',
              cbError
            );
          } finally {
            set({ backgroundAddressStoreCallback: null });
          }
        }

        // 6) Mark creation complete; syncing continues in background
        set({
          backgroundCreationStatus: WalletCreationStatus.COMPLETE,
          backgroundCreationProgress: 'Wallet ready!',
        });

        // Kick off initial lightweight refresh in background; full updates happen on SYNCED
        state.refreshWalletInfo().catch((e) => {
          breezStoreLogger.warn('Background wallet info refresh failed', e);
        });
        state.loadPayments({ limit: 50 }).catch((e) => {
          breezStoreLogger.warn('Background payments load failed', e);
        });

        breezStoreLogger.success(
          'Background wallet creation completed quickly',
          {
            address,
          }
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        breezStoreLogger.error('Background wallet creation failed', error);
        set({
          backgroundCreationStatus: WalletCreationStatus.ERROR,
          backgroundCreationProgress: null,
          backgroundCreationError: errorMessage,
          backgroundAddressStoreCallback: null,
        });
        throw error;
      }
    },

    getBackgroundCreationStatus: () => {
      const state = get();
      return {
        status: state.backgroundCreationStatus,
        progress: state.backgroundCreationProgress,
        error: state.backgroundCreationError,
        generatedAddress: state.backgroundGeneratedAddress,
      };
    },

    clearBackgroundCreationState: () => {
      set({
        backgroundCreationStatus: WalletCreationStatus.IDLE,
        backgroundCreationProgress: null,
        backgroundCreationError: null,
        backgroundGeneratedAddress: null,
      });
    },

    resetToInitialState: () => {
      set({
        ...initialState,
        // Keep authentication-related state
        isInitialized: get().isInitialized,
      });
    },

    deleteWallet: async (_options) => {
      // Do not gate deletion behind auth guard; it's a cleanup operation
      breezStoreLogger.operation(
        '🧹 Deleting wallet and resetting Breez state'
      );

      try {
        // Stop any background timers first
        try {
          const interval = get().paymentMonitoringInterval;
          if (interval) {
            clearInterval(interval as unknown as number);
            set({ paymentMonitoringInterval: undefined });
          }
        } catch (timerError) {
          breezStoreLogger.warn(
            'Failed to clear payment monitoring interval',
            timerError
          );
        }

        // Best-effort disconnect from Breez SDK
        try {
          await disconnect();
        } catch (discError) {
          breezStoreLogger.warn(
            'Disconnect during deleteWallet failed (continuing)',
            discError
          );
        }

        // Clear SecureStore keys related to wallet/Breez
        const walletKeys = [
          SecureStorageKeys.MNEMONIC,
          SecureStorageKeys.BREEZ_WALLET_INFO,
          SecureStorageKeys.BREEZ_LAST_SYNC_TIME,
          SecureStorageKeys.BREEZ_PAYMENT_LIMITS,
          SecureStorageKeys.BREEZ_SERVICE_CONFIG,
          SecureStorageKeys.BREEZ_LAST_BACKUP_TIME,
          SecureStorageKeys.BREEZ_CONNECTION_INFO,
          SecureStorageKeys.BREEZ_WALLET_ADDRESS,
        ];

        await Promise.all(
          walletKeys.map((key) =>
            deleteItemAsync(key).catch((error) =>
              breezStoreLogger.warn(`Failed to clear ${key}`, error)
            )
          )
        );

        // Clear wallet-related caches
        try {
          await walletCacheService.clearAllCaches();
        } catch (cacheError) {
          breezStoreLogger.warn('Failed to clear wallet caches', cacheError);
        }

        // Reset all Breez store state to a true fresh-install state
        set({ ...initialState });

        // Also ensure in-memory flags are clean for a pristine state
        eventListenersSetup = false;
        sdkListenerId = null;
        autoInitializationInProgress = false;
        sdkInitializationInProgress = false;

        breezStoreLogger.success('✅ Wallet deleted and Breez state reset');
        return true;
      } catch (error) {
        const message =
          error instanceof Error ? error.message : 'Unknown error';
        breezStoreLogger.error('❌ deleteWallet failed', message);
        set({ lastError: message });
        return false;
      }
    },

    // State helpers
    isSdkConnected: () => {
      const s = get();
      return (
        !!s.isConnected && s.connectionInfo.state === ConnectionState.CONNECTED
      );
    },

    // Health Check
    isHealthy: () => {
      const state = get();
      return state.isInitialized && state.isConnected && !state.lastError;
    },
  }))
);

// Auto-initialization and authentication-gated setup
breezAuthGuard.initialize();

// Set up authentication-gated initialization
const initializeWhenAuthenticated = async () => {
  breezStoreLogger.debug(
    'Setting up authentication-gated Breez SDK initialization'
  );

  // Always set up the auth state listener first
  breezStoreLogger.debug(
    'Setting up auth state listener for authentication changes'
  );
  breezAuthGuard.addAuthStateListener(async (isAuthenticated) => {
    if (isAuthenticated) {
      breezStoreLogger.debug('User authenticated, setting up Breez SDK');

      // Run auto-initialization FIRST to ensure SDK is ready
      await autoInitializeAndConnect();

      // Set up event listeners AFTER SDK initialization/connect
      await setupEventListeners();
    } else {
      breezStoreLogger.debug('User unauthenticated, Breez SDK remains idle');
    }
  });

  // Check if already authenticated
  if (breezAuthGuard.isAuthenticated()) {
    breezStoreLogger.debug('User already authenticated, setting up Breez SDK');

    // Run auto-initialization FIRST to ensure SDK is ready
    await autoInitializeAndConnect();

    // Set up event listeners AFTER SDK initialization/connect
    await setupEventListeners();
    return;
  }

  // Wait for authentication if still pending
  if (breezAuthGuard.isAuthenticationPending()) {
    breezStoreLogger.debug('Authentication pending, waiting for resolution');

    const isAuthenticated = await breezAuthGuard.waitForAuthResolution(15_000);

    if (isAuthenticated) {
      breezStoreLogger.debug(
        'Authentication resolved: authenticated, setting up Breez SDK'
      );

      // Run auto-initialization FIRST to ensure SDK is ready
      await autoInitializeAndConnect();

      // Set up event listeners AFTER SDK initialization/connect
      await setupEventListeners();
    } else {
      breezStoreLogger.debug(
        'Authentication resolved: not authenticated, skipping Breez SDK initialization'
      );
    }
    return;
  }

  breezStoreLogger.debug(
    'User not authenticated, waiting for future authentication'
  );
};

// Setup event listeners only after authentication
let eventListenersSetup = false;
let sdkInitializationInProgress = false;
let autoInitializationInProgress = false;
let sdkListenerId: string | null = null;

const setupEventListeners = async () => {
  if (eventListenersSetup && sdkListenerId) {
    breezStoreLogger.debug('Event listeners already set up, skipping');
    return;
  }

  breezStoreLogger.debug('Setting up Breez SDK event listeners');

  // If there is an existing listener, remove it to prevent duplicates
  try {
    if (sdkListenerId) {
      await removeEventListener(sdkListenerId);
      breezStoreLogger.debug('Removed previous SDK event listener');
      sdkListenerId = null;
    }
  } catch (e) {
    breezStoreLogger.warn(
      'Failed to remove previous SDK event listener (continuing)',
      e
    );
  }

  // Listen for payment and sync events
  sdkListenerId = await addEventListener((event: SdkEvent) => {
    const store = useBreezSdkStore.getState();
    try {
      switch (event.type) {
        case SdkEventVariant.SYNCED: {
          breezStoreLogger.success('🔄 SDK sync completed');
          useBreezSdkStore.setState({
            isSyncing: false,
            isLoading: false,
            lastSyncAt: Date.now(),
          });
          setTimeout(() => {
            (async () => {
              try {
                await store.refreshWalletInfo({ skipSync: true });
                await store.loadPayments({ limit: 50 });
              } catch (e) {
                breezStoreLogger.warn('Post-sync refresh failed', e);
              }
            })();
          }, 300);
          break;
        }

        case SdkEventVariant.DATA_SYNCED: {
          breezStoreLogger.success('📊 Wallet data synchronized', event);
          useBreezSdkStore.setState({
            isSyncing: false,
            isLoading: false,
            lastSyncAt: Date.now(),
          });
          setTimeout(() => {
            (async () => {
              try {
                await store.refreshWalletInfo({ skipSync: true });
                await store.loadPayments({ limit: 50 });
              } catch (e) {
                breezStoreLogger.warn('Post-data-sync refresh failed', e);
              }
            })();
          }, 100);
          break;
        }

        case SdkEventVariant.PAYMENT_SUCCEEDED: {
          breezStoreLogger.success('🎉 Payment succeeded', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
              await store.loadPayments({ limit: 50 });
            } catch (e) {
              breezStoreLogger.warn('Post-payment-succeeded refresh failed', e);
            }
          })();
          useBreezSdkStore.setState({
            isLoading: false,
            lastSyncAt: Date.now(),
          });
          break;
        }

        case SdkEventVariant.PAYMENT_FAILED: {
          breezStoreLogger.error('❌ Payment failed', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
            } catch (e) {
              breezStoreLogger.warn('Post-payment-failed refresh failed', e);
            }
          })();
          useBreezSdkStore.setState({ isLoading: false });
          break;
        }

        case SdkEventVariant.PAYMENT_PENDING: {
          breezStoreLogger.debug('⏳ Payment pending', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
            } catch (e) {
              breezStoreLogger.warn('Post-payment-pending refresh failed', e);
            }
          })();
          break;
        }

        case SdkEventVariant.PAYMENT_WAITING_CONFIRMATION: {
          breezStoreLogger.debug('⏰ Payment waiting for confirmation', event);
          useBreezSdkStore.setState({ isLoading: false });
          break;
        }

        case SdkEventVariant.PAYMENT_REFUNDED: {
          breezStoreLogger.success('💰 Payment refunded', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
              await store.loadPayments({ limit: 50 });
            } catch (e) {
              breezStoreLogger.warn('Post-payment-refunded refresh failed', e);
            }
          })();
          useBreezSdkStore.setState({ lastSyncAt: Date.now() });
          break;
        }

        case SdkEventVariant.PAYMENT_REFUND_PENDING: {
          breezStoreLogger.debug('🔄 Payment refund pending', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
            } catch (e) {
              breezStoreLogger.warn('Post-refund-pending refresh failed', e);
            }
          })();
          break;
        }

        case SdkEventVariant.PAYMENT_WAITING_FEE_ACCEPTANCE: {
          breezStoreLogger.debug('💰 Payment waiting for fee acceptance', event);
          useBreezSdkStore.setState({ isLoading: false });
          break;
        }

        case SdkEventVariant.PAYMENT_REFUNDABLE: {
          breezStoreLogger.debug('🔄 Payment is refundable', event);
          (async () => {
            try {
              await store.refreshWalletInfo({ skipSync: true });
            } catch (e) {
              breezStoreLogger.warn('Post-refundable refresh failed', e);
            }
          })();
          break;
        }

        default: {
          breezStoreLogger.debug('📡 Unhandled SDK event', event);
          break;
        }
      }
    } catch (err) {
      const message =
        err && typeof err === 'object' && 'message' in (err as any)
          ? (err as any).message
          : 'Unknown error';
      breezStoreLogger.error('❌ Error handling SDK event', {
        eventType: event.type,
        error: message,
      });
    }
  });

  eventListenersSetup = true;
  breezStoreLogger.debug('Event listeners setup complete');
};

// Force event listener re-setup for new wallets
const forceEventListenerReset = async () => {
  breezStoreLogger.debug('Forcing event listener reset for new wallet');
  eventListenersSetup = false;
  await setupEventListeners();
};

// Auto-initialize and auto-reconnect if wallet exists
const autoInitializeAndConnect = async () => {
  // Prevent concurrent auto-initialization
  if (autoInitializationInProgress) {
    breezStoreLogger.debug(
      'Auto-initialization already in progress, skipping duplicate call'
    );
    return;
  }

  const state = useBreezSdkStore.getState();

  try {
    autoInitializationInProgress = true;

    // Proceed even if not authenticated: auto-init/connect uses local SDK
    if (!breezAuthGuard.guardBreezOperation('autoInitializeAndConnect')) {
      breezStoreLogger.debug(
        'Auto-initialize: proceeding without authenticated session (local-only)'
      );
      // continue
    }

    const hasStoredMnemonic = await getItemAsync(SecureStorageKeys.MNEMONIC);

    if (hasStoredMnemonic) {
      breezStoreLogger.debug(
        'Stored mnemonic found, attempting auto-initialization'
      );

      // Initialize if not already done
      if (state.isInitialized) {
        breezStoreLogger.debug('Auto-init: SDK already initialized');
      } else {
        breezStoreLogger.operation('Auto-init: calling initialize()');
        const initialized = await state.initialize();
        breezStoreLogger.debug('Auto-init: initialize() result', {
          initialized,
        });
        if (!initialized) {
          breezStoreLogger.error('Auto-initialization failed');
          return;
        }
      }

      // Auto-connect
      breezStoreLogger.operation('Auto-init: calling connect()');
      const connected = await state.connect(hasStoredMnemonic);
      breezStoreLogger.debug('Auto-init: connect() result', { connected });
      if (connected) {
        breezStoreLogger.success('Auto-connection successful');

        // Start auto-connection monitoring
        await state.startAutoConnection();
      } else {
        breezStoreLogger.warn('Auto-connection failed');
      }
    } else {
      breezStoreLogger.debug(
        'No stored mnemonic found, skipping auto-connection'
      );
    }
  } catch (error) {
    breezStoreLogger.error('Error during auto-initialization', error);
  } finally {
    autoInitializationInProgress = false;
  }
};

// Start authentication-gated initialization
initializeWhenAuthenticated();

export default useBreezSdkStore;
export { WalletCreationStatus };
export type { BreezSdkStore, BreezSdkStoreState, BreezSdkStoreActions };
