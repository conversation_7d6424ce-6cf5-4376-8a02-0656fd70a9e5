// biome-ignore-all lint/suspicious/noConsole: debug
import { fetchAPI, getQueryClient } from "@api";
import { SecureStorageKeys } from "@const/secure-storage-keys";
import {
  getProfileInfo,
  type ProfileInfoResponse,
} from "@endpoints/user.endpoints";
import {
  getVeriffSession,
  type VeriffSessionResponse,
} from "@endpoints/veriff.endpoints";
import useErrorStore from "@store/error.store";
import i18n from "@utils/i18next";
import VeriffSdk, { type VeriffSessionResult } from "@veriff/react-native-sdk";
import {
  deleteItemAsync as deleteItemAsyncSecureStore,
  getItem as getItemSecureStore,
  setItem as setItemSecureStore,
} from "expo-secure-store";
import { ErrorKey } from "types/error.types";
import { theme } from "unistyles";
import { create } from "zustand";

export const VerificationStatus = {
  NOT_STARTED: "NOT_STARTED",
  PENDING: "PENDING",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  TIMEOUT: "TIMEOUT",
  CANCELLED: "CANCELLED",
  LOADING: "LOADING",
} as const;

export type VerificationStatus =
  (typeof VerificationStatus)[keyof typeof VerificationStatus];

interface VeriffState {
  sessionUrl: string | undefined;
  sessionTimestamp: number | undefined;
  verificationStatus: VerificationStatus;
  isPolling: boolean;
  isLoadingInitialState: boolean;

  startVerification: () => Promise<string | undefined>;
  launchVeriffSdkFlow: () => Promise<VeriffSessionResult>;
  pollVerificationStatus: () => Promise<void>;
  clearSession: () => void;
  loadInitialState: () => Promise<void>;
  checkCurrentVerificationStatus: () => Promise<void>;
  isVeriffSdkAvailable: () => boolean;
}

const VERIFICATION_TIMEOUT_MS = 60_000 * 10; // 10 minutes
const POLLING_INTERVAL_MS = 5000; // 5 seconds
const MAX_POLLING_DURATION_MS = 300_000; // 5 minutes

const useVeriffStore = create<VeriffState>((set, get) => ({
  sessionUrl: undefined,
  sessionTimestamp: undefined,
  verificationStatus: VerificationStatus.LOADING,
  isPolling: false,
  isLoadingInitialState: false,

  loadInitialState: async () => {
    const { isLoadingInitialState } = get();

    if (isLoadingInitialState) {
      return;
    }

    set({ isLoadingInitialState: true });

    try {
      const sessionUrl = getItemSecureStore(
        SecureStorageKeys.VERIFF_SESSION_URL
      );
      const sessionTimestamp = getItemSecureStore(
        SecureStorageKeys.VERIFF_SESSION_TIMESTAMP
      );

      console.log("useVeriffStore: Loading initial state", {
        sessionUrl,
        sessionTimestamp,
      });

      if (sessionUrl && sessionTimestamp) {
        const timestamp = Number.parseInt(sessionTimestamp, 10);
        const now = Date.now();

        if (now - timestamp > VERIFICATION_TIMEOUT_MS) {
          get().clearSession();
          set({ verificationStatus: VerificationStatus.TIMEOUT });
        } else {
          set({
            sessionUrl,
            sessionTimestamp: timestamp,
            verificationStatus: VerificationStatus.PENDING,
          });
        }
      } else {
        await get().checkCurrentVerificationStatus();
      }
    } catch (e) {
      console.error("Failed to load verification state from storage:", e);
      try {
        await get().checkCurrentVerificationStatus();
      } catch (fallbackError) {
        console.error(
          "Failed to check current verification status:",
          fallbackError
        );
        set({ verificationStatus: VerificationStatus.NOT_STARTED });
      }
    } finally {
      set({ isLoadingInitialState: false });
    }
  },

  checkCurrentVerificationStatus: async () => {
    try {
      const profileInfo = await fetchAPI<ProfileInfoResponse>(getProfileInfo);

      console.log("useVeriffStore CHEKING: Profile info", profileInfo);
      if (profileInfo?.profile?.is_active) {
        const hasActiveDocument = profileInfo.profile.documents.some(
          (doc) => doc.is_active
        );

        if (hasActiveDocument) {
          set({ verificationStatus: VerificationStatus.COMPLETED });
          getQueryClient().invalidateQueries({
            queryKey: [getProfileInfo.key],
          });
          console.log("useVeriffStore: Verification already completed");
          return;
        }
      }

      set({ verificationStatus: VerificationStatus.NOT_STARTED });
    } catch (error) {
      console.error(
        "useVeriffStore: Failed to check current verification status",
        error
      );
      set({ verificationStatus: VerificationStatus.NOT_STARTED });
    }
  },

  startVerification: async () => {
    const { sessionUrl } = get();

    if (sessionUrl) {
      console.log("useVeriffStore: Using existing session URL");
      return sessionUrl;
    }

    try {
      console.log("useVeriffStore: Creating new verification session");

      const response = await fetchAPI<VeriffSessionResponse>(getVeriffSession);

      if (!response?.verification?.url) {
        throw new Error("No session URL received from backend");
      }

      const newSessionUrl = response.verification.url;
      const timestamp = Date.now();

      set({
        sessionUrl: newSessionUrl,
        sessionTimestamp: timestamp,
        verificationStatus: VerificationStatus.PENDING,
      });

      setItemSecureStore(SecureStorageKeys.VERIFF_SESSION_URL, newSessionUrl);
      setItemSecureStore(
        SecureStorageKeys.VERIFF_SESSION_TIMESTAMP,
        timestamp.toString()
      );

      console.log("useVeriffStore: Session created and stored");
      return newSessionUrl;
    } catch (error) {
      console.error("useVeriffStore: Failed to create session", error);
      set({ verificationStatus: VerificationStatus.FAILED });
      useErrorStore.getState().addError(ErrorKey.DocumentVerificationIdError, {
        key: ErrorKey.DocumentVerificationIdError,
        errorText:
          error instanceof Error
            ? error.message
            : "Failed to start verification",
        translate: false,
      });
      throw error;
    }
  },

  launchVeriffSdkFlow: async () => {
    const { sessionUrl } = get();

    if (!sessionUrl) {
      throw new Error(
        "No session URL available. Please start verification first."
      );
    }

    if (!get().isVeriffSdkAvailable()) {
      throw new Error(
        "Veriff SDK is not properly initialized. Please rebuild the app after installing @veriff/react-native-sdk."
      );
    }

    console.log(
      "useVeriffStore: Launching Veriff SDK flow with URL:",
      sessionUrl
    );

    try {
      const result: VeriffSessionResult = await VeriffSdk.launchVeriff({
        sessionUrl,
        locale: i18n.language,
        branding: {
          background: theme.colors.white,
          onBackground: theme.colors.dark,
          onBackgroundSecondary: theme.colors.grey[500],
          onBackgroundTertiary: theme.colors.grey[200],
          primary: theme.colors.primary[600],
          onPrimary: theme.colors.white,
          secondary: theme.colors.primary[800],
          onSecondary: theme.colors.white,
          outline: theme.colors.grey[100],
          cameraOverlay: theme.colors.primary[800],
          onCameraOverlay: theme.colors.white,
          error: theme.colors.red,
          success: theme.colors.green,
          buttonRadius: 24,
          logo: "https://reti.tech/wp-content/uploads/2024/05/<EMAIL>",
        },
      });

      console.log("useVeriffStore: SDK result:", result);

      if (result.status === VeriffSdk.statusDone) {
        get().pollVerificationStatus();
      } else if (result.status === VeriffSdk.statusCanceled) {
        get().clearSession();
        set({ verificationStatus: VerificationStatus.CANCELLED });
      } else if (result.status === VeriffSdk.statusError) {
        set({ verificationStatus: VerificationStatus.FAILED });
        useErrorStore
          .getState()
          .addError(ErrorKey.DocumentVerificationIdError, {
            key: ErrorKey.DocumentVerificationIdError,
            errorText: result.error || "Verification failed",
            translate: false,
          });
      }

      return result;
    } catch (error) {
      console.error("useVeriffStore: launchVeriffSdkFlow error", error);
      set({ verificationStatus: VerificationStatus.FAILED });
      throw error;
    }
  },

  pollVerificationStatus: async () => {
    const { sessionUrl, sessionTimestamp, isPolling } = get();

    if (!(sessionUrl && sessionTimestamp) || isPolling) {
      return;
    }

    const now = Date.now();
    const sessionStartTime = sessionTimestamp;

    if (now - sessionStartTime > VERIFICATION_TIMEOUT_MS) {
      console.log("useVeriffStore: Verification timeout reached", {
        now,
        sessionStartTime,
        VERIFICATION_TIMEOUT_MS,
      });
      get().clearSession();
      set({ verificationStatus: VerificationStatus.TIMEOUT });
      return;
    }

    if (now - sessionStartTime > MAX_POLLING_DURATION_MS) {
      get().clearSession();
      set({ verificationStatus: VerificationStatus.TIMEOUT });
      console.log("useVeriffStore: Maximum polling duration reached", {
        now,
        sessionStartTime,
        MAX_POLLING_DURATION_MS,
      });
      return;
    }

    set({ isPolling: true });

    try {
      const profileInfo = await fetchAPI<ProfileInfoResponse>(getProfileInfo);

      if (profileInfo?.profile?.is_active) {
        const hasActiveDocument = profileInfo.profile.documents.some(
          (doc) => doc.is_active
        );

        if (hasActiveDocument) {
          get().clearSession();
          set({ verificationStatus: VerificationStatus.COMPLETED });
          getQueryClient().invalidateQueries({
            queryKey: [getProfileInfo.key],
          });
          console.log("useVeriffStore: Verification completed successfully");
          return;
        }
      }

      setTimeout(() => {
        get().pollVerificationStatus();
      }, POLLING_INTERVAL_MS);
    } catch (error) {
      console.error(
        "useVeriffStore: Failed to poll verification status",
        error
      );
      const timeSinceStart = Date.now() - sessionStartTime;
      if (timeSinceStart < MAX_POLLING_DURATION_MS) {
        setTimeout(() => {
          get().pollVerificationStatus();
        }, POLLING_INTERVAL_MS);
      } else {
        console.log("useVeriffStore: Maximum polling duration reached", {
          timeSinceStart,
          MAX_POLLING_DURATION_MS,
          sessionStartTime,
          now: Date.now(),
        });
        get().clearSession();
        set({ verificationStatus: VerificationStatus.TIMEOUT });
      }
    } finally {
      set({ isPolling: false });
    }
  },

  clearSession: () => {
    set({
      sessionUrl: undefined,
      sessionTimestamp: undefined,
      verificationStatus: VerificationStatus.NOT_STARTED,
      isPolling: false,
    });

    deleteItemAsyncSecureStore(SecureStorageKeys.VERIFF_SESSION_URL);
    deleteItemAsyncSecureStore(SecureStorageKeys.VERIFF_SESSION_TIMESTAMP);

    console.log("useVeriffStore: Session cleared");
  },

  isVeriffSdkAvailable: () => {
    try {
      return !!(VeriffSdk && typeof VeriffSdk.launchVeriff === "function");
    } catch (error) {
      console.error("Error checking Veriff SDK availability:", error);
      return false;
    }
  },
}));

useVeriffStore.getState().loadInitialState();

export default useVeriffStore;
