import type { Agreement } from '@endpoints/terms.endpoints';
import { create } from 'zustand';

interface RegistrationState {
  allChecked: boolean;
  username: string;
  password: string;
  regulations_accepted?: boolean;
  agreements?: Agreement[];
  setUsername: (username: string) => void;
  setPassword: (password: string) => void;
  setLoginAndPassword: (username: string, password: string) => void;
  setTerms: (terms: boolean) => void;
  reset: () => void;
  setAgreements: (agreements: Agreement[]) => void;
  setAgreement: (name: string, checked: boolean) => void;
  setAllChecked: () => void;
}

const useRegistrationStore = create<RegistrationState>((set, get) => ({
  allChecked: false,
  username: '',
  password: '',
  regulations_accepted: false,
  agreements: undefined,

  setUsername: (username) => set({ username }),
  setPassword: (password) => set({ password }),
  setLoginAndPassword: (username: string, password: string) =>
    set({ username, password }),
  setTerms: (regulations_accepted) => set({ regulations_accepted }),
  setAgreements: (agreements: Agreement[]) => {
    set({ agreements });
  },
  setAgreement: (name: string, checked: boolean) => {
    const newAgreements = get().agreements?.map((agreement) => {
      if (agreement.name === name) {
        return { ...agreement, checked };
      }
      return agreement;
    });
    set(({
      agreements: newAgreements,
      allChecked: newAgreements?.every((agreement) => agreement.checked === true),
    }));
  },
  setAllChecked: () => {
    set((state) => ({
      agreements: state.agreements?.map((agreement) => ({
        ...agreement,
        checked: !state.allChecked,
      })),
      allChecked: !state.allChecked,
    }));
  },
  reset: () => set({ username: '', password: '', regulations_accepted: false }),
}));

export default useRegistrationStore;
