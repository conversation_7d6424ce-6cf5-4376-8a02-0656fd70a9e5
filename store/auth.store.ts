import { AuthState } from 'types/auth.types';
import { create } from 'zustand';

interface AuthStateStore {
  authenticated: AuthState;
  token: string | null;
  setAuthState: (authState: AuthState) => void;
  setToken: (token: string | null) => void;
}

const useAuthStore = create<AuthStateStore>((set) => ({
  authenticated: AuthState.Unknown,
  token: null,
  setAuthState: (authenticated) => set({ authenticated }),
  setToken: (token) => set({ token }),
}));

export default useAuthStore;
