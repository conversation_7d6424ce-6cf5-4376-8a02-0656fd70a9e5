import { create } from "zustand";

interface PasswordResetCooldownState {
  lastSentAtByEmail: Record<string, number>;
  cooldownMs: number;
  getRemainingMs: (email: string) => number;
  canSend: (email: string) => boolean;
  markSent: (email: string, timestamp?: number) => void;
  setCooldownMs: (ms: number) => void;
}

export const usePasswordResetCooldownStore = create<PasswordResetCooldownState>(
  (set, get) => ({
    lastSentAtByEmail: {},
    cooldownMs: 60_000,
    getRemainingMs: (email: string) => {
      const { lastSentAtByEmail, cooldownMs } = get();
      const last = lastSentAtByEmail[email] || 0;
      const remaining = last + cooldownMs - Date.now();
      return remaining > 0 ? remaining : 0;
    },
    canSend: (email: string) => get().getRemainingMs(email) === 0,
    markSent: (email: string, timestamp?: number) =>
      set((state) => ({
        lastSentAtByEmail: {
          ...state.lastSentAtByEmail,
          [email]: timestamp ?? Date.now(),
        },
      })),
    setCooldownMs: (ms: number) => set({ cooldownMs: ms }),
  })
);
