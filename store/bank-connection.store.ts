import type {
  Bank,
  BankAccount,
  BankInfoDict,
  ConfirmBankChoiceResponse,
} from '@endpoints/bank-connection.endpoints';
import { create } from 'zustand';

interface BankListStoreState {
  banks: Bank[];
  confirmedBank: ConfirmBankChoiceResponse | null;
  bankInfoDict: BankInfoDict;
  bankAccounts: BankAccount[];
}

interface BankListStoreActions {
  setBanks: (banks: Bank[]) => void;
  setConfirmedBank: (bank: ConfirmBankChoiceResponse) => void;
  setBankInfoDict: (dict: BankInfoDict) => void;
  setBankAccounts: (accounts: BankAccount[]) => void;
  clearBanks: () => void;
  clearConfirmedBank: () => void;
  clearBankAccounts: () => void;
}

export type BankListStoreProps = BankListStoreState & BankListStoreActions;

const initialState: BankListStoreState = {
  banks: [],
  confirmedBank: null,
  bankInfoDict: {},
  bankAccounts: [],
};

const useBankListStore = create<BankListStoreProps>((set) => ({
  ...initialState,
  setBanks: (banks) => set({ banks }),
  setConfirmedBank: (confirmedBank) => set({ confirmedBank }),
  setBankInfoDict: (bankInfoDict) => set({ bankInfoDict }),
  setBankAccounts: (bankAccounts) => set({ bankAccounts }),
  clearBanks: () => set({ banks: [] }),
  clearConfirmedBank: () => set({ confirmedBank: null }),
  clearBankAccounts: () => set({ bankAccounts: [] }),
}));

export default useBankListStore;
