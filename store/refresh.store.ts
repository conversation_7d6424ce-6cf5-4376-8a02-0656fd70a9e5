/**
 * Global refresh state management store
 * Manages refresh states across different pages with persistence during navigation
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface PageRefreshState {
  isRefreshing: boolean;
  refreshStartTime?: number;
  lastError?: string;
  lastRefreshTime?: number;
}

interface RefreshStoreState {
  pageStates: Record<string, PageRefreshState>;

  setRefreshing: (pageKey: string, isRefreshing: boolean) => void;
  setError: (pageKey: string, error: string) => void;
  clearError: (pageKey: string) => void;
  getPageState: (pageKey: string) => PageRefreshState;
  resetPageState: (pageKey: string) => void;
  clearAllStates: () => void;
}

const defaultPageState: PageRefreshState = {
  isRefreshing: false,
  refreshStartTime: undefined,
  lastError: undefined,
  lastRefreshTime: undefined,
};

/**
 * Global refresh state store
 * 
 * This store manages refresh states for all pages in the application.
 * Each page is identified by a unique key (typically the route name).
 * 
 * Features:
 * - Independent refresh states per page
 * - State persistence during navigation
 * - Error handling per page
 * - Timestamp tracking for refresh operations
 * 
 * @example
 * ```typescript
 * const { setRefreshing, getPageState } = useRefreshStore();
 * 
 * // Start refresh for wallet page
 * setRefreshing('wallet', true);
 * 
 * // Get current state
 * const walletState = getPageState('wallet');
 * console.log(walletState.isRefreshing); // true
 * ```
 */
export const useRefreshStore = create<RefreshStoreState>()(
  subscribeWithSelector((set, get) => ({
    pageStates: {},

    /**
     * Set refreshing state for a specific page
     * 
     * @param pageKey - Unique identifier for the page
     * @param isRefreshing - Whether the page is refreshing
     */
    setRefreshing: (pageKey: string, isRefreshing: boolean) => {
      const currentState = get().pageStates[pageKey] || defaultPageState;

      set((state) => ({
        pageStates: {
          ...state.pageStates,
          [pageKey]: {
            ...currentState,
            isRefreshing,
            refreshStartTime: isRefreshing ? Date.now() : currentState.refreshStartTime,
            lastRefreshTime: !isRefreshing && currentState.isRefreshing
              ? Date.now()
              : currentState.lastRefreshTime,
            lastError: isRefreshing ? undefined : currentState.lastError,
          },
        },
      }));
    },

    /**
     * Set error for a specific page
     * 
     * @param pageKey - Unique identifier for the page
     * @param error - Error message
     */
    setError: (pageKey: string, error: string) => {
      const currentState = get().pageStates[pageKey] || defaultPageState;

      set((state) => ({
        pageStates: {
          ...state.pageStates,
          [pageKey]: {
            ...currentState,
            isRefreshing: false,
            lastError: error,
          },
        },
      }));
    },

    /**
     * Clear error for a specific page
     * 
     * @param pageKey - Unique identifier for the page
     */
    clearError: (pageKey: string) => {
      const currentState = get().pageStates[pageKey];
      if (!currentState) { return; }

      set((state) => ({
        pageStates: {
          ...state.pageStates,
          [pageKey]: {
            ...currentState,
            lastError: undefined,
          },
        },
      }));
    },

    /**
     * Get refresh state for a specific page
     * 
     * @param pageKey - Unique identifier for the page
     * @returns Page refresh state
     */
    getPageState: (pageKey: string): PageRefreshState => {
      return get().pageStates[pageKey] || defaultPageState;
    },

    /**
     * Reset refresh state for a specific page
     * 
     * @param pageKey - Unique identifier for the page
     */
    resetPageState: (pageKey: string) => {
      set((state) => {
        const newPageStates = { ...state.pageStates };
        delete newPageStates[pageKey];
        return { pageStates: newPageStates };
      });
    },

    clearAllStates: () => {
      set({ pageStates: {} });
    },
  }))
);

/**
 * Hook to get refresh state for a specific page
 * 
 * @param pageKey - Unique identifier for the page
 * @returns Refresh state and actions for the page
 */
export const usePageRefresh = (pageKey: string) => {
  const store = useRefreshStore();

  return {
    ...store.getPageState(pageKey),
    setRefreshing: (isRefreshing: boolean) => store.setRefreshing(pageKey, isRefreshing),
    setError: (error: string) => store.setError(pageKey, error),
    clearError: () => store.clearError(pageKey),
    resetState: () => store.resetPageState(pageKey),
  };
};

export default useRefreshStore;
