/**
 * Authentication Types and Interfaces
 * 
 * This file defines TypeScript interfaces for the three-tier authentication system
 * used in the view-seed-phrase page and other security-sensitive areas.
 * 
 * Authentication Hierarchy:
 * 1. Biometric Authentication (Primary) - FaceID/TouchID
 * 2. PIN Authentication (Secondary) - 6-digit PIN
 * 3. Password Authentication (Fallback) - Account password
 */

/**
 * Enumeration of authentication methods in order of priority
 */
export enum AuthenticationMethod {
  BIOMETRIC = 'biometric',
  PIN = 'pin',
  PASSWORD = 'password',
}

/**
 * Authentication attempt result states
 */
export enum AuthenticationResult {
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  NOT_AVAILABLE = 'not_available',
  NOT_ENROLLED = 'not_enrolled',
  LOCKED_OUT = 'locked_out',
}

/**
 * Authentication state for the current session
 */
export interface AuthenticationState {
  /** Whether user is currently authenticated */
  isAuthenticated: boolean;
  /** Method used for current authentication */
  authenticatedMethod?: AuthenticationMethod;
  /** Timestamp of current authentication */
  authenticatedAt?: number;
  /** Whether authentication is in progress */
  isAuthenticating: boolean;
  /** Current authentication method being attempted */
  currentMethod?: AuthenticationMethod;
  /** Number of failed attempts for current session */
  failedAttempts: number;
  /** Whether user is locked out due to too many failed attempts */
  isLockedOut: boolean;
}

/**
 * Biometric authentication configuration and status
 */
export interface BiometricConfig {
  /** Whether biometric authentication is enabled by user */
  isEnabled: boolean;
  /** Whether device has biometric hardware */
  hasHardware: boolean;
  /** Whether user has enrolled biometric data */
  isEnrolled: boolean;
  /** Whether biometric authentication is available for use */
  isAvailable: boolean;
  /** Type of biometric authentication available */
  biometricType?: 'faceId' | 'touchId' | 'fingerprint' | 'iris';
}

/**
 * PIN authentication configuration and validation
 */
export interface PinConfig {
  /** Whether PIN authentication is enabled by user */
  isEnabled: boolean;
  /** Whether PIN has been set up */
  isConfigured: boolean;
  /** Minimum PIN length (always 6 for this implementation) */
  minLength: number;
  /** Maximum PIN length (always 6 for this implementation) */
  maxLength: number;
  /** Number of failed PIN attempts allowed before lockout */
  maxAttempts: number;
  /** Lockout duration in milliseconds */
  lockoutDuration: number;
}

/**
 * Password authentication configuration
 */
export interface PasswordConfig {
  /** Whether password authentication is available */
  isAvailable: boolean;
  /** Minimum password length for validation */
  minLength: number;
  /** Whether password requires special characters */
  requiresSpecialChars: boolean;
  /** Whether password requires numbers */
  requiresNumbers: boolean;
  /** Whether password requires uppercase letters */
  requiresUppercase: boolean;
}

/**
 * Complete authentication configuration
 */
export interface AuthenticationConfig {
  /** Biometric authentication settings */
  biometric: BiometricConfig;
  /** PIN authentication settings */
  pin: PinConfig;
  /** Password authentication settings */
  password: PasswordConfig;
  /** Maximum total authentication attempts before extended lockout */
  maxTotalAttempts: number;
  /** Extended lockout duration in milliseconds */
  extendedLockoutDuration: number;
}

/**
 * Authentication attempt record for logging and security
 */
export interface AuthenticationAttempt {
  /** Unique identifier for the attempt */
  id: string;
  /** Authentication method attempted */
  method: AuthenticationMethod;
  /** Result of the authentication attempt */
  result: AuthenticationResult;
  /** Timestamp of the attempt */
  timestamp: number;
  /** Error message if authentication failed */
  error?: string;
  /** Device information for security logging */
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
}

/**
 * PIN validation result
 */
export interface PinValidationResult {
  /** Whether the PIN is valid */
  isValid: boolean;
  /** Validation error messages */
  errors: string[];
  /** Whether PIN meets length requirements */
  hasValidLength: boolean;
  /** Whether PIN contains only digits */
  hasOnlyDigits: boolean;
  /** Whether PIN is not a simple pattern (e.g., 123456, 111111) */
  isNotSimplePattern: boolean;
}

/**
 * Authentication modal props for consistent modal behavior
 */
export interface AuthenticationModalProps {
  /** Whether the modal is visible */
  isVisible: boolean;
  /** Authentication method for this modal */
  method: AuthenticationMethod;
  /** Callback when authentication succeeds */
  onSuccess: (method: AuthenticationMethod) => void;
  /** Callback when authentication fails */
  onFailure: (method: AuthenticationMethod, error: string) => void;
  /** Callback when authentication is cancelled */
  onCancel: () => void;
  /** Whether authentication is in progress */
  isLoading?: boolean;
  /** Number of failed attempts for display */
  failedAttempts?: number;
  /** Maximum attempts allowed */
  maxAttempts?: number;
  /** Custom title for the modal */
  title?: string;
  /** Custom description for the modal */
  description?: string;
}

/**
 * PIN input component props
 */
export interface PinInputProps {
  /** Current PIN value */
  value: string;
  /** Callback when PIN changes */
  onChange: (pin: string) => void;
  /** Callback when PIN is complete (6 digits) */
  onComplete?: (pin: string) => void;
  /** Whether input is disabled */
  disabled?: boolean;
  /** Whether to show error state */
  hasError?: boolean;
  /** Error message to display */
  errorMessage?: string;
  /** Whether to auto-focus first input */
  autoFocus?: boolean;
  /** Whether to clear input on error */
  clearOnError?: boolean;
  /** Custom styling */
  style?: any;
}

/**
 * Authentication service interface for dependency injection
 */
export interface AuthenticationService {
  /** Initialize the authentication service */
  initialize(): Promise<void>;

  /** Get current authentication configuration */
  getConfig(): Promise<AuthenticationConfig>;

  /** Check if biometric authentication is available */
  isBiometricAvailable(): Promise<boolean>;

  /** Attempt biometric authentication */
  authenticateWithBiometric(): Promise<AuthenticationResult>;

  /** Check if PIN is configured */
  isPinConfigured(): Promise<boolean>;

  /** Set up a new PIN */
  setupPin(pin: string): Promise<boolean>;

  /** Verify PIN */
  verifyPin(pin: string): Promise<AuthenticationResult>;

  /** Verify password */
  verifyPassword(password: string): Promise<AuthenticationResult>;

  /** Enable/disable biometric authentication */
  setBiometricEnabled(enabled: boolean): Promise<void>;

  /** Enable/disable PIN authentication */
  setPinEnabled(enabled: boolean): Promise<void>;

  /** Get authentication state */
  getAuthenticationState(): AuthenticationState;

  /** Clear authentication state */
  clearAuthentication(): void;

  /** Record authentication attempt */
  recordAttempt(attempt: AuthenticationAttempt): Promise<void>;

  /** Check if user is locked out */
  isLockedOut(): Promise<boolean>;

  /** Get remaining lockout time in milliseconds */
  getRemainingLockoutTime(): Promise<number>;

  /** Reset authentication lockout state (Development only) */
  resetLockoutState(): Promise<void>;
}

/**
 * Default authentication configuration values
 */
export const DEFAULT_AUTH_CONFIG: Partial<AuthenticationConfig> = {
  pin: {
    isEnabled: false,
    isConfigured: false,
    minLength: 6,
    maxLength: 6,
    maxAttempts: 3,
    lockoutDuration: 5 * 60 * 1000, // 5 minutes
  },
  password: {
    isAvailable: true,
    minLength: 8,
    requiresSpecialChars: true,
    requiresNumbers: true,
    requiresUppercase: true,
  },
  maxTotalAttempts: 5,
  extendedLockoutDuration: 30 * 60 * 1000, // 30 minutes
};

/**
 * Common PIN patterns that should be rejected for security
 */
export const WEAK_PIN_PATTERNS = [
  '123456',
  '654321',
  '111111',
  '222222',
  '333333',
  '444444',
  '555555',
  '666666',
  '777777',
  '888888',
  '999999',
  '000000',
  '012345',
  '543210',
];
