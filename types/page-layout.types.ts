import type { ButtonProps } from "@ui";
import type { BlurViewProps } from "expo-blur";
import type { PropsWithChildren } from "react";
import type {
  GestureResponderEvent,
  StatusBarProps,
  ViewStyle,
} from "react-native";
import type { LinearGradientProps } from "react-native-linear-gradient";
import type { ReanimatedKeyframe } from "react-native-reanimated/src/layoutReanimation/animationBuilder/Keyframe";

export type PageLayoutProps = PropsWithChildren<{
  /**
   * Sets the primary title of the page, displayed prominently at the top.
   */
  title?: string;
  /**
   * Provides a subtitle or additional descriptive text displayed below the main title.
   */
  description?: string | React.ReactNode;
  /**
   * Adjusts the vertical spacing between the page title and the main content area.
   */
  headerTitleSpacing?: number;
  /**
   * Customizes the blur effect applied to the header, accepting props from `expo-blur`'s `BlurView`.
   */
  headerBlurOptions?: Partial<BlurViewProps>;
  /**
   * If `true`, the header will display a progress bar.
   *
   * @default false
   */
  headerWithProgress?: boolean;
  /**
   * The progress value for the progress bar in the header.
   *
   * @default 0
   */
  headerProgress?: number;
  /**
   * Applies a top margin to the main content container, creating space below the header.
   */
  contentMarginTop?: number;
  /**
   * Defines the vertical spacing between direct children elements within the main content area.
   */
  contentGap?: number;
  /**
   * The text label for the primary action button at the bottom of the page. If omitted, the button is not rendered.
   */
  buttonText?: string;
  /**
   * Allows passing custom props to the primary action `Button` component, enabling style and behavior overrides.
   *
   * @see ButtonProps
   */
  buttonProps?: Partial<ButtonProps>;
  /**
   * A custom React component to display on the top of the primary action button.
   */
  buttonTopLabel?: React.ReactNode;
  /**
   * A callback function that executes when the primary action button is pressed.
   */
  onButtonPress?: (e: GestureResponderEvent) => void;
  /**
   * A callback function that overrides the default back navigation behavior of the header's back button.
   */
  onBackPress?: () => void;
  /**
   * A custom href to navigate to when the back button is pressed.
   */
  backButtonHref?: string;
  /**
   * A custom React component to display on the left side of the header, replacing the default back button.
   */
  leftComponent?: React.ReactNode;
  /**
   * Applies custom styles to the container wrapping the `leftComponent`.
   */
  leftComponentContainerProps?: ViewStyle;
  /**
   * A custom React component to display on the right side of the header.
   */
  rightComponent?: React.ReactNode;
  /**
   * Applies custom styles to the container wrapping the `rightComponent`.
   */
  rightComponentContainerProps?: ViewStyle;
  /**
   * If `true`, a blur effect is not rendered behind the bottom action button, creating a floating appearance.
   *
   * @default false
   */
  noButtonBlur?: boolean;
  /**
   * Customizes the gradient effect for the blur effect of the button background, accepting props from `react-native-linear-gradient`.
   *
   * @default { colors: ['transparent', 'white', 'white'], locations: [0, 0.3, 1], start: { x: 0, y: 0 }, end: { x: 0, y: 1 }, style: StyleSheet.absoluteFill }
   */
  buttonBlurGradientOptions?: Partial<LinearGradientProps>;
  /**
   * If `true`, the gradient effect behind the bottom action button is disabled.
   *
   * @default false
   */
  noButtonGradient?: boolean;
  /**
   * Sets the gradient variant for the button background. Use 'light' for light backgrounds, 'dark' for dark backgrounds.
   *
   * @default 'light'
   */
  buttonGradientVariant?: "light" | "dark";
  /**
   * Customizes the blur effect for the button background, accepting props from `expo-blur`'s `BlurView`.
   *
   * @default { tint: 'extraLight', intensity: 15 }
   */
  buttonBlurOptions?: Partial<BlurViewProps>;
  /**
   * Customizes the gradient effect for the button background, accepting props from `react-native-linear-gradient`.
   * This overrides the buttonGradientVariant setting when provided.
   *
   * @default Based on buttonGradientVariant: light uses #F9FAFA tones, dark uses #131615 tones
   */
  buttonGradientOptions?: Partial<LinearGradientProps>;
  /**
   * Defines the position of each color stop in the gradient overlay for the button's blur effect.
   *
   * @default [0, 0.3, 1]
   */
  buttonGradientLocations?: LinearGradientProps["locations"];
  /**
   * Applies custom styles to the main scrollable content container.
   *
   * @default undefined
   */
  contentContainerStyle?: ViewStyle;
  /**
   * Sets the background color of the entire page layout. Accepts a key from the theme's color palette.
   *
   * @default 'grey.50'
   */
  bgColor?: string;
  /**
   * Specifies the entering animation for the page layout using a `ReanimatedKeyframe`.
   *
   * @default FadeIn
   */
  animEntering?: ReanimatedKeyframe;
  /**
   * Specifies the exiting animation for the page layout using a `ReanimatedKeyframe`.
   *
   * @default FadeOut
   */
  animExiting?: ReanimatedKeyframe;
  /**
   * If `true`, removes the default horizontal padding from the main content area.
   *
   * @default false
   */
  noPadding?: boolean;
  /**
   * If `true`, disables the entering and exiting animations for the page layout.
   *
   * @default false
   */
  noAnimation?: boolean;
  /**
   * If `true`, hides the default back button in the header.
   *
   * @default false
   */
  noBackButton?: boolean;
  /**
   * If `true`, hides the header.
   *
   * @default false
   */
  noHeader?: boolean;
  /**
   * If `true`, applies a decorative gradient overlay to the page background.
   *
   * @default false
   */
  withBackgroundGradient?: boolean;
  /**
   * Customizes the background gradient, accepting props from `react-native-linear-gradient`.
   *
   * @default { colors: ['primary.500', 'transparent'], locations: [0, 1] }
   */
  backgroundGradientOptions?: Partial<LinearGradientProps>;
  /**
   * Allows customization of the status bar's appearance, accepting standard `StatusBarProps`.
   *
   * @default { backgroundColor: 'transparent', barStyle: 'dark-content', translucent: true }
   */
  statusBarOptions?: Partial<StatusBarProps>;
  /**
   * If `true`, disables the default scroll container and renders content directly. Useful for carousels and other custom scroll implementations.
   *
   * @default false
   */
  noScroll?: boolean;
  /**
   * Custom background gradients that render absolutely positioned outside the main layout. Useful for animated gradients that need to touch screen edges.
   */
  customBackgroundGradients?: React.ReactNode;
  /**
   * If `true`, allows content to extend behind the header with blur effect on scroll. The header will be transparently overlaid on top of the content.
   *
   * @default false
   */
  headerOverlayContent?: boolean;
  /**
   * If `true`, hides the large title in the content area while keeping the header title on scroll functionality.
   *
   * @default false
   */
  noLargeTitle?: boolean;
  /**
   * If `true`, the small title is always visible.
   *
   * @default false
   */
  smallTitleAlwaysVisible?: boolean;
  /**
   * Scroll Y value to start the header blur animation.
   *
   * @default 50
   */
  headerAnimationStartY?: number;
  /**
   * Scroll Y value when the header blur animation is fully complete.
   *
   * @default 80
   */
  headerAnimationEndY?: number;
  /**
   * If `true`, disables top overscroll (bounce) behavior.
   *
   * @default false
   */
  disableTopOverscroll?: boolean;
  /**
   * Scroll Y value when the header title should start appearing (threshold).
   *
   * @default undefined (uses headerAnimationStartY)
   */
  headerTitleThresholdY?: number;
  /**
   * Color of the title text in the header. Accepts a color string.
   *
   * @default 'grey.80'
   */
  titleColor?: string;
  /**
   * If `true`, enables pull-to-refresh functionality for the page.
   * When enabled, users can pull down to trigger a refresh action.
   *
   * @default false
   */
  pullToRefresh?: boolean;
  /**
   * Callback function executed when pull-to-refresh is triggered.
   * Should return a Promise that resolves when the refresh operation is complete.
   * Only used when `pullToRefresh` is `true`.
   *
   * @param pageKey - Unique identifier for the current page (typically route name)
   * @returns Promise that resolves when refresh is complete
   */
  onRefresh?: (pageKey: string) => Promise<void>;
  /**
   * Additional vertical offset for the pull-to-refresh indicator in pixels.
   * Useful when the refresh indicator overlaps with header content.
   * This value is added to the default offset calculation.
   *
   * @default 0
   */
  refreshIndicatorOffset?: number;
  /**
   * A custom React component to display as the header logo.
   * When provided, this will be shown in the header instead of the default title.
   */
  headerLogo?: React.ReactNode;
}>;
