/**
 * @fileoverview Exchange Rate Types
 * 
 * Type definitions for Bitcoin price fetching and currency conversion.
 * Supports multiple API providers with fallback mechanisms.
 */

// Supported currencies for conversion
export const SupportedCurrencies = {
  USD: 'USD',
  PLN: 'PLN',
  EUR: 'EUR',
  GBP: 'GBP',
} as const;

export type SupportedCurrency = typeof SupportedCurrencies[keyof typeof SupportedCurrencies];

// Bitcoin price data from various sources
export interface BitcoinPrice {
  /** Price in USD */
  usd: number;
  /** Timestamp when the price was fetched */
  timestamp: number;
  /** Source of the price data */
  source: 'binance' | 'coingecko' | 'fallback';
}

// Exchange rates for currency conversion
export interface ExchangeRates {
  /** Base currency (always USD) */
  base: 'USD';
  /** Exchange rates relative to USD */
  rates: Record<SupportedCurrency, number>;
  /** Timestamp when rates were fetched */
  timestamp: number;
  /** Source of the exchange rate data */
  source: 'exchangerate-api' | 'fallback';
}

// Combined price data with conversions
export interface PriceData {
  /** Bitcoin price in various currencies */
  bitcoin: Record<SupportedCurrency, number>;
  /** When this data was last updated */
  lastUpdated: number;
  /** Whether the data is from cache or fresh */
  isFromCache: boolean;
  /** Primary data sources used */
  sources: {
    bitcoinPrice: BitcoinPrice['source'];
    exchangeRates: ExchangeRates['source'];
  };
}

// API Response Types

// Binance API response
export interface BinanceTickerResponse {
  symbol: string;
  price: string;
}

// ExchangeRate-API response
export interface ExchangeRateApiResponse {
  base: string;
  date: string;
  time_last_updated: number;
  rates: Record<string, number>;
}

// Service configuration
export interface ExchangeRateConfig {
  /** Cache duration in milliseconds (default: 5 minutes) */
  cacheDuration?: number;
  /** Request timeout in milliseconds (default: 10 seconds) */
  timeout?: number;
  /** Whether to enable logging */
  enableLogging?: boolean;
  /** Fallback exchange rates if APIs fail */
  fallbackRates?: Partial<Record<SupportedCurrency, number>>;
}

// Service response wrapper
export interface ExchangeRateResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  source?: string;
}

// Cache entry structure
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Service events for real-time updates
export const ExchangeRateEvent = {
  PRICE_UPDATED: 'PRICE_UPDATED',
  RATES_UPDATED: 'RATES_UPDATED',
  ERROR_OCCURRED: 'ERROR_OCCURRED',
  CACHE_CLEARED: 'CACHE_CLEARED',
} as const;

export type ExchangeRateEvent = typeof ExchangeRateEvent[keyof typeof ExchangeRateEvent];

// Event payloads
export interface ExchangeRateEventPayload {
  [ExchangeRateEvent.PRICE_UPDATED]: {
    priceData: PriceData;
  };
  [ExchangeRateEvent.RATES_UPDATED]: {
    exchangeRates: ExchangeRates;
  };
  [ExchangeRateEvent.ERROR_OCCURRED]: {
    error: string;
    operation: string;
  };
  [ExchangeRateEvent.CACHE_CLEARED]: Record<string, never>;
}

// Event listener type
export type ExchangeRateEventListener<T extends ExchangeRateEvent> = (
  event: T,
  payload: ExchangeRateEventPayload[T]
) => void;

// Service interface
export interface IExchangeRateService {
  // Configuration
  initialize(config?: ExchangeRateConfig): boolean;

  // Price fetching
  getBitcoinPrice(): Promise<ExchangeRateResponse<BitcoinPrice>>;
  getExchangeRates(): Promise<ExchangeRateResponse<ExchangeRates>>;
  getPriceData(): Promise<ExchangeRateResponse<PriceData>>;

  // Currency conversion utilities
  convertSatoshisToCurrency(satoshis: number, currency: SupportedCurrency): Promise<number>;
  convertBtcToCurrency(btc: number, currency: SupportedCurrency): Promise<number>;

  // Cache management
  clearCache(): void;
  getCacheStatus(): {
    bitcoinPrice: CacheEntry<BitcoinPrice> | null;
    exchangeRates: CacheEntry<ExchangeRates> | null;
  };

  // Event management
  addEventListener<T extends ExchangeRateEvent>(
    event: T,
    listener: ExchangeRateEventListener<T>
  ): string;
  removeEventListener(listenerId: string): void;

  // Service status
  isHealthy(): boolean;
  getLastError(): string | null;
  getServiceStats(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    cacheHits: number;
    lastUpdateTime: number | null;
  };
}

// Default configuration values
export const DEFAULT_EXCHANGE_RATE_CONFIG: Required<ExchangeRateConfig> = {
  cacheDuration: 5 * 60 * 1000, // 5 minutes
  timeout: 10 * 1000, // 10 seconds
  enableLogging: false,
  fallbackRates: {
    USD: 1,
    PLN: 4.0, // Approximate USD/PLN rate
    EUR: 0.85, // Approximate USD/EUR rate
    GBP: 0.75, // Approximate USD/GBP rate
  },
};

export const FALLBACK_BITCOIN_PRICE = 100_000;
