import type {
  Config,
  ConnectRequest,
  FiatCurrency,
  GetInfoResponse,
  InputType,
  LightningPaymentLimitsResponse,
  LiquidNetwork,
  OnchainPaymentLimitsResponse,
  PayAmount,
  Payment,
  PaymentMethod,
  PaymentState,
  PaymentType,
  Rate,
  ReceiveAmount,
  SdkEvent,
  WalletInfo,
} from '@breeztech/react-native-breez-sdk-liquid';

// Connection States
export const BreezConnectionState = {
  DISCONNECTED: 'DISCONNECTED',
  CONNECTING: 'CONNECTING',
  CONNECTED: 'CONNECTED',
  RECONNECTING: 'RECONNECTING',
  FAILED: 'FAILED',
} as const;

export type BreezConnectionState =
  (typeof BreezConnectionState)[keyof typeof BreezConnectionState];

// Service Configuration
export interface BreezSdkConfig {
  network: LiquidNetwork;
  apiKey?: string; // Optional - will use BREEZ_API_KEY environment variable if not provided
  workingDir?: string;
  retryAttempts?: number;
  retryDelayMs?: number;
  connectionTimeoutMs?: number;
  enableLogging?: boolean;
}

// Connection Management
export interface BreezConnectionInfo {
  state: BreezConnectionState;
  lastConnectedAt?: number;
  lastError?: string;
  retryCount: number;
  isInitialized: boolean;
}

// Wallet State
export interface BreezWalletState {
  walletInfo?: WalletInfo;
  isConnected: boolean;
  lastSyncAt?: number;
  pendingPayments: Payment[];
  recentPayments: Payment[];
}

// Payment Limits
export interface BreezPaymentLimits {
  lightning: LightningPaymentLimitsResponse;
  onchain: OnchainPaymentLimitsResponse;
  lastUpdatedAt: number;
}

// Service Events
export const BreezServiceEvent = {
  CONNECTION_STATE_CHANGED: 'CONNECTION_STATE_CHANGED',
  WALLET_INFO_UPDATED: 'WALLET_INFO_UPDATED',
  PAYMENT_RECEIVED: 'PAYMENT_RECEIVED',
  PAYMENT_SENT: 'PAYMENT_SENT',
  PAYMENT_PENDING: 'PAYMENT_PENDING',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  SYNC_STARTED: 'SYNC_STARTED',
  SYNC_COMPLETED: 'SYNC_COMPLETED',
  ERROR_OCCURRED: 'ERROR_OCCURRED',
} as const;

export type BreezServiceEvent =
  (typeof BreezServiceEvent)[keyof typeof BreezServiceEvent];

// Event Payloads
export interface BreezServiceEventPayload {
  [BreezServiceEvent.CONNECTION_STATE_CHANGED]: {
    previousState: BreezConnectionState;
    currentState: BreezConnectionState;
    error?: string;
  };
  [BreezServiceEvent.WALLET_INFO_UPDATED]: {
    walletInfo: WalletInfo;
  };
  [BreezServiceEvent.PAYMENT_RECEIVED]: {
    payment: Payment;
  };
  [BreezServiceEvent.PAYMENT_SENT]: {
    payment: Payment;
  };
  [BreezServiceEvent.PAYMENT_PENDING]: {
    payment: Payment;
  };
  [BreezServiceEvent.PAYMENT_FAILED]: {
    payment: Payment;
    error: string;
  };
  [BreezServiceEvent.SYNC_STARTED]: Record<string, never>;
  [BreezServiceEvent.SYNC_COMPLETED]: {
    didPullNewRecords: boolean;
  };
  [BreezServiceEvent.ERROR_OCCURRED]: {
    error: string;
    context?: string;
  };
}

// Service Methods Response Types
export interface BreezServiceResponse<T = void> {
  success: boolean;
  data?: T;
  error?: string;
}

// Payment Operations
export interface CreateInvoiceRequest {
  amountSat: number;
  description?: string;
  paymentMethod?: PaymentMethod;
}

export interface CreateInvoiceResponse {
  destination: string;
  paymentMethod: PaymentMethod;
  amountSat: number;
  feesSat: number;
}

export interface PaymentResult {
  payment: Payment;
  feesSat: number;
}



// Service Statistics
export interface BreezServiceStats {
  totalPaymentsSent: number;
  totalPaymentsReceived: number;
  totalVolumeSat: number;
  totalFeesPaidSat: number;
  averagePaymentSizeSat: number;
  connectionUptime: number;
  lastBackupAt?: number;
}

// Error Context
export interface BreezErrorContext {
  operation: string;
  timestamp: number;
  connectionState: BreezConnectionState;
  retryCount?: number;
  additionalInfo?: Record<string, unknown>;
}

// Event Listener
export type BreezEventListener<T extends BreezServiceEvent> = (
  event: T,
  payload: BreezServiceEventPayload[T]
) => void;

// Service Interface
export interface IBreezSdkService {
  // Connection Management
  initialize(config: BreezSdkConfig): Promise<BreezServiceResponse>;
  connect(mnemonic?: string): Promise<BreezServiceResponse>;
  disconnect(): Promise<BreezServiceResponse>;
  reconnect(): Promise<BreezServiceResponse>;

  // State Management
  getConnectionInfo(): BreezConnectionInfo;
  getWalletInfo(): Promise<BreezServiceResponse<WalletInfo>>;
  isInitialized(): boolean;

  // Payment Operations
  createInvoice(
    request: CreateInvoiceRequest
  ): Promise<BreezServiceResponse<CreateInvoiceResponse>>;
  getPaymentLimits(): Promise<BreezServiceResponse<BreezPaymentLimits>>;

  // Payment History
  listPayments(filters?: {
    types?: PaymentType[];
    states?: PaymentState[];
    limit?: number;
    offset?: number;
  }): Promise<BreezServiceResponse<Payment[]>>;



  // Utilities
  getServiceStats(): BreezServiceStats;

  // Event Management
  addEventListener<T extends BreezServiceEvent>(
    event: T,
    listener: BreezEventListener<T>
  ): string;
  removeEventListener(listenerId: string): void;

  // Health Check
  isHealthy(): boolean;
  getLastError(): string | null;
}

// Re-export commonly used types from the SDK
export type {
  Config,
  ConnectRequest,
  GetInfoResponse,
  WalletInfo,
  Payment,
  PaymentState,
  PaymentType,
  PaymentMethod,
  LiquidNetwork,
  SdkEvent,
  ReceiveAmount,
  PayAmount,
  InputType,
  Rate,
  FiatCurrency,
};
