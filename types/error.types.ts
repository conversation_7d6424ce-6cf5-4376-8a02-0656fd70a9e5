export const ErrorKey = {
  AuthError: "AuthError",
  RegistrationError: "RegistrationError",
  TokenError: "TokenError",
  DocumentVerificationIdError: "DocumentVerificationIdError",
  PasswordResetError: "PasswordResetError",
  // Breez SDK Liquid Network Errors
  BreezConnectionError: "BreezConnectionError",
  BreezInitializationError: "BreezInitializationError",
  BreezPaymentError: "BreezPaymentError",
  BreezSyncError: "BreezSyncError",
  BreezBackupError: "BreezBackupError",
  BreezRestoreError: "BreezRestoreError",
  BreezRefundError: "BreezRefundError",
  BreezConfigurationError: "BreezConfigurationError",
  BreezNetworkError: "BreezNetworkError",
  BreezValidationError: "BreezValidationError",
} as const;

export type AppError = {
  key: ErrorKey;
  errorText?: string;
  status?: number;
  translate: boolean;
};

export type ErrorKey = (typeof ErrorKey)[keyof typeof ErrorKey];
