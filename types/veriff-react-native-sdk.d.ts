declare module '@veriff/react-native-sdk' {
  export interface VeriffBranding {
    logo?: string | { uri: string };
    background?: string;
    onBackground?: string;
    onBackgroundSecondary?: string;
    onBackgroundTertiary?: string;
    primary?: string;
    onPrimary?: string;
    secondary?: string;
    onSecondary?: string;
    outline?: string;
    cameraOverlay?: string;
    onCameraOverlay?: string;
    error?: string;
    success?: string;
    buttonRadius?: number;
    iOSFont?: {
      regular?: string;
      medium?: string;
      bold?: string;
    };
    androidFont?: {
      regular?: string;
      medium?: string;
      bold?: string;
    };
  }

  export interface VeriffLaunchOptions {
    sessionUrl: string;
    locale?: string;
    branding?: VeriffBranding;
    customIntroScreen?: boolean;
  }

  export interface VeriffSessionResult {
    status: 'STATUS_DONE' | 'STATUS_CANCELED' | 'STATUS_ERROR';
    error?: string;
  }

  export interface VeriffError {
    code: string;
    message: string;
  }

  const VeriffSdk: {
    launchVeriff: (
      options: VeriffLaunchOptions
    ) => Promise<VeriffSessionResult>;
    statusDone: 'STATUS_DONE';
    statusCanceled: 'STATUS_CANCELED';
    statusError: 'STATUS_ERROR';
  };

  export default VeriffSdk;
}
