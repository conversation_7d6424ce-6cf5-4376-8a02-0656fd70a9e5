// Dashboard cache data types
export interface CachedBalanceData {
  balanceSat: number;
  balanceBtc: number;
  balancePln: number;
  bitcoinPricePln: number;
  timestamp: number;
}

export interface CachedTransactionData {
  id: number;
  type: 'deposit' | 'withdrawal';
  date: string;
  amount: number;
  btc: number;
}

export interface CachedBitcoinPriceData {
  pricePln: number;
  changePercent: number;
  changeTimeframe: string;
  timestamp: number;
}

// Wallet cache data types
export interface CachedWalletInfo {
  address: string;
  balance: number;
  isConnected: boolean;
  lastSync: number;
}

export interface CachedSeedPhrase {
  encryptedSeed: string;
  checksum: string;
  timestamp: number;
}

// User preferences cache
export interface CachedUserPreferences {
  language: string;
  currency: string;
  notifications: boolean;
  biometrics: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// Exchange rate cache
export interface CachedExchangeRates {
  rates: Record<string, number>;
  baseCurrency: string;
  lastUpdated: number;
}

// Transaction history cache
export interface CachedTransactionHistory {
  transactions: Array<{
    id: string;
    type: string;
    amount: number;
    currency: string;
    date: string;
    status: string;
    description?: string;
  }>;
  totalCount: number;
  lastFetch: number;
}

// Cache key constants for type safety
export const CACHE_KEYS = {
  // Shared wallet data (used by dashboard, wallet, and other pages)
  BALANCE: 'wallet_balance',
  TRANSACTIONS: 'wallet_transactions',
  BITCOIN_PRICE: 'bitcoin_price',

  // Wallet
  WALLET_INFO: 'wallet_info',
  WALLET_SEED_PHRASE: 'wallet_seed_phrase',
  WALLET_ADDRESSES: 'wallet_addresses',

  // User
  USER_PREFERENCES: 'user_preferences',
  USER_PROFILE: 'user_profile',

  // Exchange rates
  EXCHANGE_RATES: 'exchange_rates',
  BITCOIN_PRICE_HISTORY: 'bitcoin_price_history',

  // Transactions
  TRANSACTION_HISTORY: 'transaction_history',
  PENDING_TRANSACTIONS: 'pending_transactions',

  // Settings
  APP_SETTINGS: 'app_settings',
  SECURITY_SETTINGS: 'security_settings',
} as const;

// Cache TTL constants (in milliseconds)
export const CACHE_TTL = {
  // Short-lived data (5 minutes)
  SHORT: 5 * 60 * 1000,

  // Medium-lived data (1 hour)
  MEDIUM: 60 * 60 * 1000,

  // Long-lived data (24 hours)
  LONG: 24 * 60 * 60 * 1000,

  // Very long-lived data (7 days)
  VERY_LONG: 7 * 24 * 60 * 60 * 1000,

  // Specific TTLs
  BALANCE: 5 * 60 * 1000, // 5 minutes
  TRANSACTIONS: 10 * 60 * 1000, // 10 minutes
  BITCOIN_PRICE: 2 * 60 * 1000, // 2 minutes
  EXCHANGE_RATES: 15 * 60 * 1000, // 15 minutes
  USER_PREFERENCES: 7 * 24 * 60 * 60 * 1000, // 7 days
  WALLET_INFO: 30 * 60 * 1000, // 30 minutes
} as const;

// Type-safe cache key mapping
export type CacheKeyMap = {
  [CACHE_KEYS.BALANCE]: CachedBalanceData;
  [CACHE_KEYS.TRANSACTIONS]: CachedTransactionData[];
  [CACHE_KEYS.BITCOIN_PRICE]: CachedBitcoinPriceData;
  [CACHE_KEYS.WALLET_INFO]: CachedWalletInfo;
  [CACHE_KEYS.WALLET_SEED_PHRASE]: CachedSeedPhrase;
  [CACHE_KEYS.USER_PREFERENCES]: CachedUserPreferences;
  [CACHE_KEYS.EXCHANGE_RATES]: CachedExchangeRates;
  [CACHE_KEYS.TRANSACTION_HISTORY]: CachedTransactionHistory;
};

// Utility type for cache operations
export type CacheKey = keyof CacheKeyMap;
export type CacheValue<K extends CacheKey> = CacheKeyMap[K];
