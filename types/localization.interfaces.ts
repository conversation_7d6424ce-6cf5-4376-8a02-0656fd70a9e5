interface IInputTranslation {
  label: string;
}

export interface IFormProps {
  title: string;
  subtitle: string;
  bottomText: {
    text: string;
    linkText: string;
  };
}

export interface ILocalization {
  signIn: {
    formProps: IFormProps;
    inputs: {
      username: IInputTranslation;
      password: IInputTranslation;
    };
    buttons: {
      signIn: IInputTranslation;
    };
    forgotPassword: string;
  };
  signUp: {
    formProps: IFormProps;
    inputs: {
      username: IInputTranslation;
      password: IInputTranslation;
      confirmPassword: IInputTranslation;
      termsAndConditions: IInputTranslation;
    };
    buttons: {
      signUp: IInputTranslation;
    };
    validationMessages: {
      emailValidationMessage: string;
      passwordValidationMessage: string;
      confirmPasswordValidationMessage: string;
    };
  };
  requestInfo: {
    success: string;
  };
  buttonsLabels: {
    dismiss: string;
  };
}

export interface ILocalizationContext {
  language: string;
  translation: ILocalization;
}
