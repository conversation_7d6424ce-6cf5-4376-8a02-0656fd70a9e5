import useProgressStore from '@store/progress.store';
import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';

const useSetProgressPosition = (currentStep: number, totalSteps: number) => {
  const setProgress = useProgressStore((state) => state.setProgress);

  useFocusEffect(
    useCallback(() => {
      const progress = currentStep / totalSteps;
      setProgress(progress);
    }, [currentStep, totalSteps, setProgress])
  );
};

export default useSetProgressPosition;
