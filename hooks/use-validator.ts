import { useCallback, useState } from 'react';
import { ZodError, type ZodType } from 'zod';
import { useTranslations } from './use-translations';

function buildIssues(
  issuesArr: ZodError['issues'],
  t: (msg: string) => string
) {
  const issues: Record<string, string> = {};
  for (const issue of issuesArr) {
    const key = issue.path[0] ? String(issue.path[0]) : 'form';
    if (!issues[key]) {
      issues[key] = t(issue.message);
    }
  }
  return issues;
}

export function useValidator<T = unknown>(
  validator: ZodType<T>
): [
  string | undefined,
  (value?: T) => void,
  (value?: T) => boolean,
  Record<string, string> | undefined,
] {
  const t = useTranslations();
  const [validationError, setValidationError] = useState<string | undefined>(
    undefined
  );
  const [allIssues, setAllIssues] = useState<
    Record<string, string> | undefined
  >(undefined);

  const validate = useCallback(
    (value?: T) => {
      try {
        validator.parse(value);
        setValidationError(undefined);
        setAllIssues(undefined);
      } catch (e: unknown) {
        if (e instanceof ZodError) {
          setValidationError(t(e.issues?.[0]?.message));
          setAllIssues(buildIssues(e.issues, t));
        }
      }
    },
    [t, validator]
  );

  const isValid = useCallback(
    (value?: T) => {
      try {
        validator.parse(value);
        return true;
      } catch {
        return false;
      }
    },
    [validator]
  );

  return [validationError, validate, isValid, allIssues];
}
