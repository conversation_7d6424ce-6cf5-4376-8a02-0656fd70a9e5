import useBreezSdkStore, { WalletCreationStatus } from '@store/breez-sdk.store';
import { createLogger } from '@utils/logger';
import { useEffect, useState } from 'react';

const backgroundStatusLogger = createLogger(
  'useBackgroundWalletCreationStatus'
);

export interface BackgroundCreationState {
  // Status
  status: WalletCreationStatus;
  isInProgress: boolean;
  isComplete: boolean;
  hasError: boolean;

  // Progress
  progress: string | null;
  error: string | null;
  generatedAddress: string | null;

  // Actions
  clearStatus: () => void;
  retry: () => void;
}

export const useBackgroundWalletCreationStatus =
  (): BackgroundCreationState => {
    const { getBackgroundCreationStatus, clearBackgroundCreationState } =
      useBreezSdkStore();

    const [statusState, setStatusState] = useState(() =>
      getBackgroundCreationStatus()
    );
    const [lastLoggedStatus, setLastLoggedStatus] = useState<string | null>(null);

    useEffect(() => {
      const interval = setInterval(() => {
        const currentStatus = getBackgroundCreationStatus();
        setStatusState((prev) => {
          // Performance: Only update if status actually changed
          if (
            prev.status === currentStatus.status &&
            prev.progress === currentStatus.progress &&
            prev.error === currentStatus.error &&
            prev.generatedAddress === currentStatus.generatedAddress
          ) {
            return prev;
          }
          return currentStatus;
        });
      }, 2000);

      return () => clearInterval(interval);
    }, [getBackgroundCreationStatus]);

    useEffect(() => {
      if (statusState.status !== WalletCreationStatus.IDLE) {
        const statusKey = `${statusState.status}-${statusState.progress}`;

        if (lastLoggedStatus !== statusKey) {
          backgroundStatusLogger.debug('Background creation status update', {
            status: statusState.status,
            progress: statusState.progress,
            hasError: !!statusState.error,
            hasAddress: !!statusState.generatedAddress,
          });
          setLastLoggedStatus(statusKey);
        }
      } else {
        setLastLoggedStatus(null);
      }
    }, [statusState, lastLoggedStatus]);

    const isInProgress = [
      WalletCreationStatus.MNEMONIC_STORED,
      WalletCreationStatus.CONNECTING_BREEZ,
      WalletCreationStatus.GENERATING_ADDRESS,
      WalletCreationStatus.SYNCING_BACKEND,
    ].includes(statusState.status as any);

    const isComplete = statusState.status === WalletCreationStatus.COMPLETE;
    const hasError = statusState.status === WalletCreationStatus.ERROR;

    const clearStatus = () => {
      backgroundStatusLogger.debug('Clearing background creation status');
      clearBackgroundCreationState();
      setStatusState({
        status: WalletCreationStatus.IDLE,
        progress: null,
        error: null,
        generatedAddress: null,
      });
      setLastLoggedStatus(null);
    };

    const retry = () => {
      backgroundStatusLogger.operation('Retrying background wallet creation');
      clearStatus();
    };

    return {
      status: statusState.status,
      isInProgress,
      isComplete,
      hasError,
      progress: statusState.progress,
      error: statusState.error,
      generatedAddress: statusState.generatedAddress,
      clearStatus,
      retry,
    };
  };

export default useBackgroundWalletCreationStatus;
