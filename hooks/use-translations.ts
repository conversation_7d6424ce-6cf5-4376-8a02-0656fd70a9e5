import type { TFunction } from 'i18next';
import { useCallback } from 'react';
import { type UseTranslationOptions, useTranslation } from 'react-i18next';

export const useTranslations = (
  ns?: string,
  options?: UseTranslationOptions<string>
) => {
  const { t } = useTranslation(ns, options);
  return useCallback(
    (translation: string, callbackOptions?: Record<string, unknown>) =>
      t(translation, callbackOptions),
    [t]
  ) as TFunction;
};
