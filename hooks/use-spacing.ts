import { useMemo } from 'react';
import type { DimensionValue, ViewStyle } from 'react-native';
import { useUnistyles } from 'react-native-unistyles';

export interface SpacingProps {
  m?: number;
  mt?: number;
  mb?: number;
  ml?: number;
  mr?: number;
  mx?: number;
  my?: number;
  p?: number;
  pt?: number;
  pb?: number;
  pl?: number;
  pr?: number;
  px?: number;
  py?: number;
  gap?: number;
  pos?: 'absolute' | 'relative';
  top?: DimensionValue;
  left?: DimensionValue;
  right?: DimensionValue;
  bottom?: DimensionValue;
  w?: DimensionValue;
  h?: DimensionValue;
  flex?: number;
}

const spacingProperties: Record<string, keyof ViewStyle> = {
  m: 'margin',
  mt: 'marginTop',
  mb: 'marginBottom',
  ml: 'marginLeft',
  mr: 'marginRight',
  mx: 'marginInline',
  my: 'marginBlock',
  p: 'padding',
  pt: 'paddingTop',
  pb: 'paddingBottom',
  pl: 'paddingLeft',
  pr: 'paddingRight',
  px: 'paddingInline',
  py: 'paddingBlock',
  gap: 'gap',
};

const layoutProperties: Record<string, keyof ViewStyle> = {
  pos: 'position',
  top: 'top',
  left: 'left',
  right: 'right',
  bottom: 'bottom',
  w: 'width',
  h: 'height',
  flex: 'flex',
};

export const allPropKeys = [
  ...Object.keys(spacingProperties),
  ...Object.keys(layoutProperties),
] as (keyof SpacingProps)[];

export const useSpacing = (props: SpacingProps): ViewStyle => {
  const { theme } = useUnistyles();

  const dependencies = allPropKeys.map((key) => props[key]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: because of the use of allPropKeys
  return useMemo(() => {
    const styles: ViewStyle = {};
    for (const key of allPropKeys) {
      const value = props[key as keyof SpacingProps];

      if (value === undefined) {
        continue;
      }

      if (key in spacingProperties) {
        if (typeof value === 'number') {
          const styleKey = spacingProperties[key];
          (styles as Record<string, unknown>)[styleKey] = theme.gap(value);
        }
      } else if (key in layoutProperties) {
        const styleKey = layoutProperties[key];
        (styles as Record<string, unknown>)[styleKey] = value;
      }
    }
    return styles;
  }, [theme, ...dependencies]);
};
