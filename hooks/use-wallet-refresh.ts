import walletCacheService from '@services/wallet-cache.service';
import useBreezSdkStore from '@store/breez-sdk.store';
import { createLogger } from '@utils/logger';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';

import { useExchangeRate } from 'hooks/use-exchange-rate';
import { useCallback } from 'react';

/**
 * Reusable hook for wallet data refresh functionality
 *
 * Provides unified refresh logic that updates shared cache data
 * for both dashboard and wallet pages. Ensures data consistency
 * across the application.
 *
 * @example
 * ```typescript
 * const { handleRefresh, isRefreshing } = useWalletRefresh();
 *
 * // Use in PageLayout
 * <PageLayout onRefresh={handleRefresh} pullToRefresh>
 *   {content}
 * </PageLayout>
 * ```
 */
export function useWalletRefresh() {
  const { state: walletState, actions: walletActions } = useSimpleWallet();
  const {
    isConnected: isBreezConnected,
    walletInfo,
    refreshWalletInfo,
    loadPayments,
    recentPayments,
  } = useBreezSdkStore();
  const { priceData, convertSatoshis, refreshPrices } = useExchangeRate();

  const updateSharedCache = useCallback(async () => {
    try {
      const hasWallet = walletState.hasWallet && walletState.wallet;

      if (!hasWallet) {
        return;
      }

      if (convertSatoshis) {
        walletCacheService.initialize(convertSatoshis);
      }

      await walletCacheService.updateAllCaches({
        walletInfo: isBreezConnected && walletInfo ? walletInfo : undefined,
        priceData: priceData || undefined,
        payments:
          isBreezConnected && recentPayments ? recentPayments : undefined,
      });
    } catch (_error) {
      // Silently handle errors
    }
  }, [
    walletState,
    isBreezConnected,
    walletInfo,
    priceData,
    recentPayments,
    convertSatoshis,
  ]);

  const handleRefresh = useCallback(async () => {
    const refreshLogger = createLogger('WalletRefresh');

    try {
      refreshLogger.debug('🔄 Starting wallet refresh');

      const hasWallet = walletState.hasWallet && walletState.wallet;

      if (!hasWallet) {
        refreshLogger.warn('⚠️ No wallet found, skipping refresh');
        return;
      }

      // Step 1: Invalidate caches
      refreshLogger.debug('📝 Invalidating caches');
      await walletCacheService.invalidateRelatedCaches('all');

      // Step 2: Refresh simple wallet data
      refreshLogger.debug('🔄 Refreshing simple wallet data');
      await walletActions.refresh();

      if (isBreezConnected) {
        refreshLogger.debug('🔄 Refreshing Breez SDK data');
        const refreshPromises = [
          refreshWalletInfo().then((success) => {
            if (success) {
              refreshLogger.success('✅ Wallet info refreshed');
            } else {
              refreshLogger.warn('⚠️ Wallet info refresh failed');
            }
            return success;
          }),
          loadPayments({ limit: 50 }).then((payments) => {
            if (payments && payments.length >= 0) {
              refreshLogger.success(`✅ Loaded ${payments.length} payments`);
            } else {
              refreshLogger.warn('⚠️ Payment loading failed');
            }
            return payments;
          }),
        ];

        const [walletInfoSuccess] = await Promise.all(refreshPromises);
        if (!walletInfoSuccess) {
          refreshLogger.warn('⚠️ Wallet info refresh failed, but continuing');
        }
      } else {
        refreshLogger.debug('📡 Breez SDK not connected, skipping SDK refresh');
      }

      // Step 4: Refresh exchange rates
      refreshLogger.debug('💱 Refreshing exchange rates');
      await refreshPrices();

      // Step 5: Update shared cache
      refreshLogger.debug('💾 Updating shared cache');
      await updateSharedCache();

      refreshLogger.success('✅ Wallet refresh completed successfully');
    } catch (error) {
      refreshLogger.error('❌ Wallet refresh failed:', error);
      // Re-throw error so PageLayout can show user feedback
      throw error;
    }
  }, [
    walletState,
    walletActions,
    isBreezConnected,
    refreshWalletInfo,
    loadPayments,
    refreshPrices,
    updateSharedCache,
  ]);

  const updateCache = useCallback(async () => {
    await updateSharedCache();
  }, [updateSharedCache]);

  const refreshSpecific = useCallback(
    async (dataType: 'balance' | 'transactions' | 'price') => {
      try {
        const hasWallet = walletState.hasWallet && walletState.wallet;
        if (!hasWallet) return;

        await walletCacheService.invalidateRelatedCaches(dataType);

        switch (dataType) {
          case 'balance':
            if (isBreezConnected) {
              const walletInfoRefreshed = await refreshWalletInfo();
              if (walletInfoRefreshed && walletInfo && priceData) {
                await walletCacheService.refreshCache('balance', {
                  walletInfo,
                  priceData,
                });
              }
            }
            break;
          case 'transactions':
            if (isBreezConnected) {
              const freshPayments = await loadPayments({ limit: 10 });
              if (freshPayments) {
                await walletCacheService.refreshCache('transactions', {
                  payments: freshPayments,
                });
              }
            }
            break;
          case 'price':
            await refreshPrices();
            if (priceData) {
              await walletCacheService.refreshCache('price', { priceData });
            }
            break;
          default:
            break;
        }
      } catch (_error) {
        // Silently handle errors
      }
    },
    [
      walletState,
      isBreezConnected,
      walletInfo,
      refreshWalletInfo,
      loadPayments,
      refreshPrices,
      priceData,
    ]
  );

  return {
    handleRefresh,
    updateCache,
    refreshSpecific,
  };
}
