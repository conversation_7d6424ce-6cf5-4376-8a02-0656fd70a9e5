/** biome-ignore-all lint/correctness/useExhaustiveDependencies: . */

import walletCacheService from '@services/wallet-cache.service';
import useBreezSdkStore from '@store/breez-sdk.store';
import { createLogger } from '@utils/logger';
import { useEffect, useRef } from 'react';
import { AppState, type AppStateStatus } from 'react-native';

const backgroundSyncLogger = createLogger('BackgroundSync');

interface BackgroundSyncOptions {
  syncInterval?: number;
  syncOnAppActive?: boolean;
  enablePeriodicSync?: boolean;
}

export function useBackgroundSync(options: BackgroundSyncOptions = {}) {
  const {
    syncInterval = 60_000,
    syncOnAppActive = true,
    enablePeriodicSync = true,
  } = options;

  const {
    isConnected: isBreezConnected,
    walletInfo,
    fastRefresh,
    loadPayments,
  } = useBreezSdkStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSyncRef = useRef<number>(0);

  const performBackgroundSync = async () => {
    try {
      const now = Date.now();
      const timeSinceLastSync = now - lastSyncRef.current;

      if (!isBreezConnected || timeSinceLastSync < 10_000) {
        return;
      }

      backgroundSyncLogger.debug('Starting background sync');

      const [walletInfoRefreshed, freshPayments] = await Promise.all([
        fastRefresh(),
        loadPayments({ limit: 10 }),
      ]);

      if (walletInfoRefreshed || freshPayments) {
        await walletCacheService.updateAllCaches({
          walletInfo: walletInfo || undefined,
          payments: freshPayments || undefined,
        });

        backgroundSyncLogger.success(
          'Background sync completed and cache updated'
        );
      }

      lastSyncRef.current = now;
    } catch (error) {
      backgroundSyncLogger.error('Background sync failed', error);
    }
  };

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (syncOnAppActive && nextAppState === 'active') {
      backgroundSyncLogger.debug('App became active, triggering sync');
      performBackgroundSync();
    }
  };

  useEffect(() => {
    if (!syncOnAppActive) return;

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [syncOnAppActive]);

  useEffect(() => {
    if (!enablePeriodicSync) return;

    intervalRef.current = setInterval(() => {
      if (AppState.currentState === 'active') {
        performBackgroundSync();
      }
    }, syncInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enablePeriodicSync, syncInterval]);

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    triggerSync: performBackgroundSync,
    getLastSyncTime: () => lastSyncRef.current,
    canSync: () => {
      const timeSinceLastSync = Date.now() - lastSyncRef.current;
      return isBreezConnected && timeSinceLastSync >= 10_000;
    },
  };
}

export default useBackgroundSync;
