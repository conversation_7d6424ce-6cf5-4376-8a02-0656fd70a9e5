import exchangeRateService from '@services/exchange-rate.service';
import { useCallback, useEffect, useState } from 'react';
import type { PriceData, SupportedCurrency } from 'types/exchange-rate.types';

interface UseExchangeRateState {
  priceData: PriceData | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  lastUpdated: number | null;
}

interface UseExchangeRateReturn extends UseExchangeRateState {
  convertSatoshis: (satoshis: number, currency: SupportedCurrency) => number;
  convertBtc: (btc: number, currency: SupportedCurrency) => number;
  refreshPrices: () => Promise<void>;
  clearCache: () => void;
  getStats: () => ReturnType<typeof exchangeRateService.getServiceStats>;
}

/**
 * Hook for accessing Bitcoin exchange rates and currency conversion
 *
 * Features:
 * - Real-time price updates
 * - Automatic caching (5-minute default)
 * - Error handling with fallbacks
 * - Multiple currency support
 * - Synchronous conversion functions
 *
 * @param autoRefresh - Whether to automatically refresh prices (default: true)
 * @param refreshInterval - Auto-refresh interval in milliseconds (default: 5 minutes)
 * @returns Exchange rate state and utility functions
 */
export const useExchangeRate = (
  autoRefresh = true,
  refreshInterval = 5 * 60 * 1000
): UseExchangeRateReturn => {
  const [state, setState] = useState<UseExchangeRateState>({
    priceData: null,
    isLoading: false,
    error: null,
    isInitialized: false,
    lastUpdated: null,
  });

  const initializeService = useCallback(async () => {
    try {
      const success = await exchangeRateService.initialize({
        cacheDuration: 5 * 60 * 1000,
        timeout: 10 * 1000,
        // biome-ignore lint/correctness/noUndeclaredVariables: dev flag
        enableLogging: __DEV__,
      });

      setState((prev) => ({
        ...prev,
        isInitialized: success,
        error: success ? null : 'Failed to initialize exchange rate service',
      }));

      return success;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Initialization failed';
      setState((prev) => ({
        ...prev,
        isInitialized: false,
        error: errorMessage,
      }));
      return false;
    }
  }, []);

  const fetchPriceData = useCallback(async () => {
    if (!state.isInitialized) {
      return;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await exchangeRateService.getPriceData();

      if (result.success && result.data) {
        setState((prev) => ({
          ...prev,
          priceData: result.data!,
          isLoading: false,
          error: null,
          lastUpdated: result.data!.lastUpdated,
        }));
      } else {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to fetch price data',
        }));
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch prices';
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [state.isInitialized]);

  const convertSatoshis = useCallback(
    (satoshis: number, currency: SupportedCurrency): number => {
      if (!state.priceData) {
        // Fallback calculation when no data is available
        const fallbackRates = {
          USD: 100_000, // $100k fallback
          PLN: 400_000, // ~$100k * 4 PLN/USD
          EUR: 85_000, // ~$100k * 0.85 EUR/USD
          GBP: 75_000, // ~$100k * 0.75 GBP/USD
        };
        return (satoshis / 100_000_000) * fallbackRates[currency];
      }

      return (satoshis / 100_000_000) * state.priceData.bitcoin[currency];
    },
    [state.priceData]
  );

  const convertBtc = useCallback(
    (btc: number, currency: SupportedCurrency): number => {
      if (!state.priceData) {
        // Fallback calculation when no data is available
        const fallbackRates = {
          USD: 100_000, // $100k fallback
          PLN: 400_000, // ~$100k * 4 PLN/USD
          EUR: 85_000, // ~$100k * 0.85 EUR/USD
          GBP: 75_000, // ~$100k * 0.75 GBP/USD
        };
        return btc * fallbackRates[currency];
      }

      return btc * state.priceData.bitcoin[currency];
    },
    [state.priceData]
  );

  const refreshPrices = useCallback(async () => {
    await fetchPriceData();
  }, [fetchPriceData]);

  const clearCache = useCallback(() => {
    exchangeRateService.clearCache();
    fetchPriceData();
  }, [fetchPriceData]);

  const getStats = useCallback(() => {
    return exchangeRateService.getServiceStats();
  }, []);

  useEffect(() => {
    initializeService().then((success) => {
      if (success) {
        fetchPriceData();
      }
    });
  }, [initializeService, fetchPriceData]);

  useEffect(() => {
    if (!(autoRefresh && state.isInitialized)) {
      return;
    }

    const interval = setInterval(() => {
      fetchPriceData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, state.isInitialized, fetchPriceData]);

  useEffect(() => {
    const priceUpdateListener = exchangeRateService.addEventListener(
      'PRICE_UPDATED',
      (_, payload) => {
        setState((prev) => ({
          ...prev,
          priceData: payload.priceData,
          lastUpdated: payload.priceData.lastUpdated,
          error: null,
        }));
      }
    );

    const errorListener = exchangeRateService.addEventListener(
      'ERROR_OCCURRED',
      (_, payload) => {
        setState((prev) => ({
          ...prev,
          error: payload.error,
        }));
      }
    );

    return () => {
      exchangeRateService.removeEventListener(priceUpdateListener);
      exchangeRateService.removeEventListener(errorListener);
    };
  }, []);

  return {
    ...state,
    convertSatoshis,
    convertBtc,
    refreshPrices,
    clearCache,
    getStats,
  };
};

export default useExchangeRate;
