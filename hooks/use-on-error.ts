import useErrorStore from '@store/error.store';
import { useEffect } from 'react';
import type { AppError, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'types/error.types';

const useOnError = (
  key: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  callback: (error: AppError | undefined) => void
) => {
  const error = useErrorStore((state) => state.errors.get(key));

  useEffect(() => {
    callback(error);
  }, [error, callback]);
};

export default useOnError;
