import { getQueryData } from "@api";
import {
  getSavingPlanSettings,
  type SavingPlanProps,
} from "@endpoints/saving-plan.endpoints";
import useAuthStore from "@store/auth.store";
import useSavingPlanStore from "@store/saving-plan.store";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "expo-router";
import { useEffect } from "react";
import { AuthState } from "types/auth.types";
import { mapSavingResponseToSavingStore } from "../utils/map-saving-response-to-saving-store";
import { loadToken, saveToken } from "../utils/token";

const useOnAuthorizationChange = (isLoaded: boolean) => {
  const router = useRouter();
  const { setAuthState, setToken, authenticated, token } = useAuthStore();

  const savingStore = useSavingPlanStore();

  const { data: savingPlan } = useQuery<SavingPlanProps>(
    getQueryData(getSavingPlanSettings, {
      queryOptions: {
        enabled: authenticated === AuthState.Authenticated,
      },
      fetcherOptions: {
        ignoreError: 404,
      },
    })
  );

  useEffect(() => {
    if (!isLoaded) {
      return;
    }

    const initializeAuth = async () => {
      setAuthState(AuthState.Pending);
      const loadedToken = await loadToken();
      setToken(loadedToken);

      if (loadedToken?.length) {
        setAuthState(AuthState.Authenticated);
      } else {
        setAuthState(AuthState.Unauthenticated);
      }
    };

    initializeAuth();
  }, [isLoaded, setAuthState, setToken]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we update savingStore in this effect which causes infinite re-render
  useEffect(() => {
    if (!isLoaded) {
      return;
    }
    if (authenticated === AuthState.Unauthenticated) {
      router.replace("/init-screen");
    }

    if (token) {
      saveToken(token);
    }
    // TODO handle this better (redirecting depending on saving plan, because when user creates saving plan, he is redirected to /(authenticated))
    if (authenticated === AuthState.Authenticated) {
      router.replace("/(authenticated)");
      if (savingPlan) {
        mapSavingResponseToSavingStore(savingPlan, savingStore);
      }
    }
  }, [authenticated, router, isLoaded, token]);
};

export default useOnAuthorizationChange;
