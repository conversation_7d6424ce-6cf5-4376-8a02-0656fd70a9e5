import { useCallback, useState } from 'react';

const useToggleState = (
  initialState: boolean
): [boolean, (value: boolean) => void, () => void] => {
  const [state, setState] = useState(initialState);

  const setValue = useCallback((value = true) => {
    setState(value);
  }, []);

  const toggleValue = useCallback(() => {
    setState((value) => !value);
  }, []);

  return [state, setValue, toggleValue];
};

export default useToggleState;
