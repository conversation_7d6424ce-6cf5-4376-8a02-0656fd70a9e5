import cacheService from '@services/cache.service';
import { useCallback, useEffect, useState } from 'react';

interface UseCacheOptions {
  ttl?: number; // Time to live in milliseconds
  autoLoad?: boolean;
  onCacheHit?: () => void;
  onCacheMiss?: () => void;
}

interface UseCacheReturn<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  updateCache: (value: T, ttl?: number) => Promise<void>;
  clearCache: () => Promise<void>;
  refreshCache: () => Promise<void>;
  hasCache: () => Promise<boolean>;
}

/**
 * Universal cache hook for React components
 *
 * Provides a simple interface for caching data with automatic loading,
 * type safety, and error handling.
 *
 * @param key - Unique cache key
 * @param defaultValue - Default value when cache is empty
 * @param options - Cache configuration options
 *
 * @example
 * ```typescript
 * // Basic usage
 * const { data: balance, updateCache } = useCache<BalanceData>('wallet_balance');
 *
 * // With default value and TTL
 * const { data: transactions, updateCache } = useCache<Transaction[]>(
 *   'recent_transactions',
 *   [],
 *   { ttl: 30 * 60 * 1000 } // 30 minutes
 * );
 *
 * // Update cache when fresh data arrives
 * useEffect(() => {
 *   if (freshData) {
 *     updateCache(freshData);
 *   }
 * }, [freshData, updateCache]);
 * ```
 */
export function useCache<T>(
  key: string,
  defaultValue?: T,
  options: UseCacheOptions = {}
): UseCacheReturn<T> {
  const { ttl, autoLoad = true, onCacheHit, onCacheMiss } = options;

  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(autoLoad);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    cacheService.initialize();
  }, []);

  useEffect(() => {
    if (!autoLoad) return;

    const loadCache = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const cachedData = await cacheService.get<T>(key, defaultValue);

        if (cachedData !== null) {
          setData(cachedData);
          onCacheHit?.();
        } else {
          setData(defaultValue ?? null);
          onCacheMiss?.();
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown cache error';
        setError(errorMessage);
        setData(defaultValue ?? null);
      } finally {
        setIsLoading(false);
      }
    };

    loadCache();
  }, [key, defaultValue, autoLoad, onCacheHit, onCacheMiss]);

  const updateCache = useCallback(
    async (value: T, customTtl?: number): Promise<void> => {
      try {
        setError(null);
        await cacheService.set(key, value, customTtl ?? ttl);
        setData(value);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to update cache';
        setError(errorMessage);
      }
    },
    [key, ttl]
  );

  const clearCache = useCallback(async (): Promise<void> => {
    try {
      setError(null);
      await cacheService.delete(key);
      setData(defaultValue ?? null);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to clear cache';
      setError(errorMessage);
    }
  }, [key, defaultValue]);

  const refreshCache = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const cachedData = await cacheService.get<T>(key, defaultValue);
      setData(cachedData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to refresh cache';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [key, defaultValue]);

  const hasCache = useCallback(async (): Promise<boolean> => {
    try {
      return await cacheService.has(key);
    } catch (_err) {
      return false;
    }
  }, [key]);

  return {
    data,
    isLoading,
    error,
    updateCache,
    clearCache,
    refreshCache,
    hasCache,
  };
}

