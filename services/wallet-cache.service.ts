import type {
  Payment,
  WalletInfo,
} from '@breeztech/react-native-breez-sdk-liquid';
import cacheService from '@services/cache.service';
import { createLogger } from '@utils/logger';
import {
  CACHE_KEYS,
  CACHE_TTL,
  type CachedBalanceData,
  type CachedBitcoinPriceData,
  type CachedTransactionData,
} from 'types/cache.types';
import type { PriceData } from 'types/exchange-rate.types';

class WalletCacheService {
  private convertSatoshis:
    | ((satoshis: number, currency: any) => number)
    | null = null;
  private logger = createLogger('WalletCacheService');

  initialize(
    convertSatoshisFunction: (satoshis: number, currency: any) => number
  ) {
    this.convertSatoshis = convertSatoshisFunction;
  }

  async updateBalanceCache(
    walletInfo: WalletInfo,
    priceData: PriceData
  ): Promise<void> {
    try {
      if (!this.convertSatoshis) {
        return;
      }

      const balanceData: CachedBalanceData = {
        balanceSat: walletInfo.balanceSat || 0,
        balanceBtc: (walletInfo.balanceSat || 0) / 100_000_000,
        balancePln: this.convertSatoshis!(walletInfo.balanceSat || 0, 'PLN'),
        bitcoinPricePln: priceData.bitcoin.PLN,
        timestamp: Date.now(),
      };

      await cacheService.set(
        CACHE_KEYS.BALANCE,
        balanceData,
        CACHE_TTL.BALANCE
      );
    } catch (error) {
      this.logger.error('Failed to update balance cache', error);
    }
  }

  async updateBitcoinPriceCache(priceData: PriceData): Promise<void> {
    try {
      const bitcoinPriceData: CachedBitcoinPriceData = {
        pricePln: priceData.bitcoin.PLN,
        changePercent: 0, // TODO: Calculate actual change when historical data is available
        changeTimeframe: '24h',
        timestamp: Date.now(),
      };

      await cacheService.set(
        CACHE_KEYS.BITCOIN_PRICE,
        bitcoinPriceData,
        CACHE_TTL.BITCOIN_PRICE
      );
    } catch (error) {
      this.logger.error('Failed to update Bitcoin price cache', error);
    }
  }

  async updateTransactionsCache(payments: Payment[]): Promise<void> {
    try {
      if (!this.convertSatoshis) {
        return;
      }

      if (!payments || payments.length === 0) {
        return;
      }

      const transactionData: CachedTransactionData[] = payments
        .slice(0, 3)
        .map((payment, index) => ({
          id: index + 1,
          type: payment.paymentType === 'receive' ? 'deposit' : 'withdrawal',
          date: new Date(
            (payment.timestamp || Date.now() / 1000) * 1000
          ).toISOString(),
          amount: this.convertSatoshis!(payment.amountSat, 'PLN'),
          btc: payment.amountSat / 100_000_000,
        }));

      await cacheService.set(
        CACHE_KEYS.TRANSACTIONS,
        transactionData,
        CACHE_TTL.TRANSACTIONS
      );
    } catch (error) {
      this.logger.error('Failed to update transactions cache', error);
    }
  }

  async updateAllCaches(data: {
    walletInfo?: WalletInfo;
    priceData?: PriceData;
    payments?: Payment[];
  }): Promise<void> {
    try {
      const promises: Promise<void>[] = [];

      if (data.walletInfo && data.priceData) {
        promises.push(this.updateBalanceCache(data.walletInfo, data.priceData));
      }

      if (data.priceData) {
        promises.push(this.updateBitcoinPriceCache(data.priceData));
      }

      if (data.payments) {
        promises.push(this.updateTransactionsCache(data.payments));
      }

      await Promise.all(promises);
    } catch (error) {
      this.logger.error('Failed to update wallet caches', error);
    }
  }

  async clearAllCaches(): Promise<void> {
    try {
      await Promise.all([
        cacheService.delete(CACHE_KEYS.BALANCE),
        cacheService.delete(CACHE_KEYS.BITCOIN_PRICE),
        cacheService.delete(CACHE_KEYS.TRANSACTIONS),
      ]);
      this.logger.success('All wallet caches cleared');
    } catch (error) {
      this.logger.error('Failed to clear wallet caches', error);
    }
  }

  async invalidateRelatedCaches(changedData: 'balance' | 'transactions' | 'price' | 'all'): Promise<void> {
    try {
      const invalidationMap = {
        balance: [CACHE_KEYS.BALANCE],
        transactions: [CACHE_KEYS.TRANSACTIONS],
        price: [CACHE_KEYS.BITCOIN_PRICE, CACHE_KEYS.BALANCE],
        all: [CACHE_KEYS.BALANCE, CACHE_KEYS.TRANSACTIONS, CACHE_KEYS.BITCOIN_PRICE],
      };

      const keysToInvalidate = invalidationMap[changedData];

      await Promise.all(
        keysToInvalidate.map(key => cacheService.delete(key))
      );

      this.logger.debug(`Invalidated caches for: ${changedData}`, {
        invalidatedKeys: keysToInvalidate,
      });
    } catch (error) {
      this.logger.error(`Failed to invalidate caches for: ${changedData}`, error);
      throw error;
    }
  }

  async refreshCache(cacheType: 'balance' | 'transactions' | 'price', freshData: any): Promise<void> {
    try {
      switch (cacheType) {
        case 'balance':
          if (freshData.walletInfo && freshData.priceData) {
            await this.updateBalanceCache(freshData.walletInfo, freshData.priceData);
          }
          break;
        case 'transactions':
          if (freshData.payments) {
            await this.updateTransactionsCache(freshData.payments);
          }
          break;
        case 'price':
          if (freshData.priceData) {
            await this.updateBitcoinPriceCache(freshData.priceData);
          }
          break;
        default:
          this.logger.warn(`Unknown cache type: ${cacheType}`);
          break;
      }

      this.logger.debug(`Refreshed ${cacheType} cache with fresh data`);
    } catch (error) {
      this.logger.error(`Failed to refresh ${cacheType} cache`, error);
      throw error;
    }
  }

  async getCacheStats(): Promise<{
    hasBalance: boolean;
    hasBitcoinPrice: boolean;
    hasTransactions: boolean;
  }> {
    try {
      const [hasBalance, hasBitcoinPrice, hasTransactions] = await Promise.all([
        cacheService.has(CACHE_KEYS.BALANCE),
        cacheService.has(CACHE_KEYS.BITCOIN_PRICE),
        cacheService.has(CACHE_KEYS.TRANSACTIONS),
      ]);

      return {
        hasBalance,
        hasBitcoinPrice,
        hasTransactions,
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats', error);
      return {
        hasBalance: false,
        hasBitcoinPrice: false,
        hasTransactions: false,
      };
    }
  }
}

const walletCacheService = new WalletCacheService();
export default walletCacheService;
