import { createLogger } from '@utils/logger';
import {
  deleteItemAsync,
  getItemAsync,
  type SecureStoreOptions,
  setItemAsync,
} from 'expo-secure-store';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface CacheConfig {
  ttl?: number;
  prefix?: string;
}

const DEFAULT_CONFIG: Required<CacheConfig> = {
  ttl: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  prefix: 'cache_',
};

const BACKGROUND_SAFE_OPTIONS: SecureStoreOptions = {
  requireAuthentication: false, // Allow access without user authentication
  authenticationPrompt: 'Authenticate to access cache data',
  keychainService: 'reti-cache-service',
};

class CacheService {
  private config: Required<CacheConfig>;
  private memoryCache = new Map<string, CacheEntry<any>>();
  private isInitialized = false;
  private logger = createLogger('CacheService');

  constructor(config: CacheConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.cleanupExpiredEntries();
      this.isInitialized = true;
    } catch (error) {
      this.logger.error('Failed to initialize universal cache service', error);
    }
  }

  async get<T>(key: string, defaultValue?: T): Promise<T | null> {
    try {
      const fullKey = this.getFullKey(key);

      const memoryEntry = this.memoryCache.get(fullKey);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        return memoryEntry.data;
      }

      const stored = await getItemAsync(fullKey, BACKGROUND_SAFE_OPTIONS);
      if (!stored) {
        return defaultValue ?? null;
      }

      const entry: CacheEntry<T> = JSON.parse(stored);

      if (this.isExpired(entry)) {
        await this.delete(key);
        return defaultValue ?? null;
      }

      this.memoryCache.set(fullKey, entry);
      return entry.data;
    } catch (error) {
      this.logger.error(`Failed to get cache entry: ${key}`, error);
      return defaultValue ?? null;
    }
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    try {
      const fullKey = this.getFullKey(key);
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl: ttl ?? this.config.ttl,
      };

      this.memoryCache.set(fullKey, entry);

      try {
        await setItemAsync(fullKey, JSON.stringify(entry), BACKGROUND_SAFE_OPTIONS);
      } catch (storageError) {
        this.logger.warn(`Failed to persist cache entry to secure storage: ${key}`, storageError);

        if (storageError instanceof Error &&
          (storageError.message.includes('User interaction') ||
            storageError.message.includes('authentication'))) {
          this.logger.debug('Cache storage failed due to background/authentication restrictions');
        }
      }
    } catch (error) {
      this.logger.error(`Failed to set cache entry: ${key}`, error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const fullKey = this.getFullKey(key);

      this.memoryCache.delete(fullKey);
      await deleteItemAsync(fullKey, BACKGROUND_SAFE_OPTIONS);
    } catch (error) {
      this.logger.error(`Failed to delete cache entry: ${key}`, error);
    }
  }

  async clear(keyPattern?: string): Promise<void> {
    try {
      if (keyPattern) {
        const pattern = this.getFullKey(keyPattern);
        const keysToDelete: string[] = [];

        for (const key of this.memoryCache.keys()) {
          if (key.includes(pattern)) {
            keysToDelete.push(key);
          }
        }

        await Promise.all(
          keysToDelete.map(async (key) => {
            this.memoryCache.delete(key);
            await deleteItemAsync(key, BACKGROUND_SAFE_OPTIONS);
          })
        );
      } else {
        this.memoryCache.clear();
      }
    } catch (error) {
      this.logger.error('Failed to clear cache', error);
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key);

      const memoryEntry = this.memoryCache.get(fullKey);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        return true;
      }

      const stored = await getItemAsync(fullKey, BACKGROUND_SAFE_OPTIONS);
      if (!stored) return false;

      const entry: CacheEntry<any> = JSON.parse(stored);
      return !this.isExpired(entry);
    } catch (error) {
      this.logger.error(`Failed to check cache existence: ${key}`, error);
      return false;
    }
  }

  getStats(): {
    memoryEntries: number;
    totalSize: number;
  } {
    return {
      memoryEntries: this.memoryCache.size,
      totalSize: Array.from(this.memoryCache.values()).reduce(
        (total, entry) => total + JSON.stringify(entry).length,
        0
      ),
    };
  }

  private getFullKey(key: string): string {
    return `${this.config.prefix}${key}`;
  }

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private async cleanupExpiredEntries(): Promise<void> {
    try {
      const expiredKeys: string[] = [];

      for (const [key, entry] of this.memoryCache.entries()) {
        if (this.isExpired(entry)) {
          expiredKeys.push(key);
        }
      }

      await Promise.all(
        expiredKeys.map(async (key) => {
          this.memoryCache.delete(key);
          await deleteItemAsync(key, BACKGROUND_SAFE_OPTIONS);
        })
      );
    } catch (error) {
      this.logger.error('Failed to cleanup expired cache entries', error);
    }
  }
}

const cacheService = new CacheService();
export default cacheService;
