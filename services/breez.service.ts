// biome-ignore-all lint: .

import {
  addEventListener,
  type Config,
  type ConnectRequest,
  connect,
  defaultConfig,
  disconnect,
  fetchLightningLimits,
  fetchOnchainLimits,
  getInfo,
  LiquidNetwork,
  listPayments,
  type Payment,
  PaymentMethod,
  type PrepareReceiveRequest,
  prepareReceivePayment,
  ReceiveAmountVariant,
  type ReceivePaymentRequest,
  receivePayment,
  removeEventListener,
  type SdkEvent,
  SdkEventVariant,
  type WalletInfo,
} from '@breeztech/react-native-breez-sdk-liquid';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import useErrorStore from '@store/error.store';
import { breezServiceLogger } from '@utils/logger';
import {
  BREEZ_ERROR_MESSAGES,
  BREEZ_OPERATIONS,
  DEFAULT_NETWORK,
  DIAGNOSTIC_MESSAGES,
  PAYMENT_STATUS,
  PAYMENT_TYPES,
  WALLET_NETWORKS,
} from '@utils/wallet/constants';
import {
  getItem as getItemSecureStore,
  setItem as setItemSecureStore,
  WHEN_UNLOCKED,
} from 'expo-secure-store';
import type {
  BreezConnectionInfo,
  BreezConnectionState,
  BreezErrorContext,
  BreezEventListener,
  BreezPaymentLimits,
  BreezSdkConfig,
  BreezServiceEvent,
  BreezServiceEventPayload,
  BreezServiceResponse,
  BreezServiceStats,
  BreezWalletState,
  CreateInvoiceRequest,
  CreateInvoiceResponse,
  IBreezSdkService,
  PaymentState,
  PaymentType,
} from 'types/breez-sdk.types';
import {
  BreezConnectionState as ConnectionState,
  BreezServiceEvent as ServiceEvent,
} from 'types/breez-sdk.types';
import { ErrorKey } from 'types/error.types';
import { extractErrorMessage, updateConnectionState } from '../utils/breez/common-operations';
import { connectionHealthMonitor } from '../utils/breez/health-monitor';
import { progressiveRetryManager } from '../utils/breez/retry-manager';

const getApiKey = (): string | undefined => {
  const apiKey = process.env.BREEZ_API_KEY;
  if (apiKey && typeof apiKey === 'string' && apiKey.trim().length > 0) {
    return apiKey.trim();
  }
};

const API_KEY = getApiKey();

export const LIQUID_NETWORK_REGEX = /liquidnetwork:([^?]+)/;

/**
 * Singleton service for managing Breez SDK Liquid Network integration.
 *
 * This service provides a comprehensive interface for interacting with the Breez SDK
 * for Liquid Network operations including:
 * - Wallet initialization and connection management
 * - Payment processing (send/receive)
 * - Invoice creation and management
 * - Network synchronization and health monitoring
 * - Secure storage of wallet data
 * - Event handling and state management
 * - Error handling with automatic retry mechanisms
 *
 * @dependencies
 * - @breeztech/react-native-breez-sdk-liquid - Core Breez SDK
 * - @const/secure-storage-keys - Secure storage key constants
 * - @store/error.store - Error state management
 * - @utils/logger - Centralized logging
 * - ./health-monitor - Connection health monitoring
 * - ./retry-manager - Progressive retry management
 * - ./state-manager - State consistency management
 *
 * @example
 * ```typescript
 * // Initialize the service
 * const breezService = BreezSdkService.getInstance();
 * await breezService.initialize({
 *   network: LiquidNetwork.TESTNET,
 *   enableLogging: true
 * });
 *
 * // Connect with a mnemonic
 * const result = await breezService.connect(mnemonic);
 * if (result.success) {
 *   console.log('Connected successfully');
 * }
 *
 * // Create an invoice
 * const invoice = await breezService.createInvoice({
 *   amountSat: 100000,
 *   description: 'Test payment'
 * });
 * ```
 */
class BreezSdkService implements IBreezSdkService {
  private static instance: BreezSdkService | null = null;
  private config: BreezSdkConfig | null = null;
  private connectionInfo: BreezConnectionInfo;
  private walletState: BreezWalletState;
  private paymentLimits: BreezPaymentLimits | null = null;
  private eventListeners: Map<
    string,
    { event: BreezServiceEvent; listener: BreezEventListener<any> }
  > = new Map();
  private sdkEventListenerId: string | null = null;
  private retryTimeouts: Set<NodeJS.Timeout> = new Set();
  private lastError: string | null = null;
  private serviceStats: BreezServiceStats;

  private constructor() {
    this.connectionInfo = {
      state: ConnectionState.DISCONNECTED,
      retryCount: 0,
      isInitialized: false,
    };

    this.walletState = {
      isConnected: false,
      pendingPayments: [],
      recentPayments: [],
    };

    this.serviceStats = {
      totalPaymentsSent: 0,
      totalPaymentsReceived: 0,
      totalVolumeSat: 0,
      totalFeesPaidSat: 0,
      averagePaymentSizeSat: 0,
      connectionUptime: 0,
    };

    this.loadPersistedStateSync();
  }
  sync(): Promise<BreezServiceResponse> {
    throw new Error('Method not implemented.');
  }

  static getInstance(): BreezSdkService {
    if (!BreezSdkService.instance) {
      BreezSdkService.instance = new BreezSdkService();
    }
    return BreezSdkService.instance;
  }

  /**
   * Load persisted state synchronously (for constructor)
   */
  private loadPersistedStateSync(): void {
    try {
      const connectionInfoStr = getItemSecureStore(
        SecureStorageKeys.BREEZ_CONNECTION_INFO
      );
      if (connectionInfoStr) {
        const persistedInfo = JSON.parse(connectionInfoStr);
        this.connectionInfo = { ...this.connectionInfo, ...persistedInfo };
      }

      const walletInfoStr = getItemSecureStore(
        SecureStorageKeys.BREEZ_WALLET_INFO
      );
      if (walletInfoStr) {
        this.walletState.walletInfo = JSON.parse(walletInfoStr);
      }

      const paymentLimitsStr = getItemSecureStore(
        SecureStorageKeys.BREEZ_PAYMENT_LIMITS
      );
      if (paymentLimitsStr) {
        this.paymentLimits = JSON.parse(paymentLimitsStr);
      }

      const configStr = getItemSecureStore(
        SecureStorageKeys.BREEZ_SERVICE_CONFIG
      );
      if (configStr) {
        this.config = JSON.parse(configStr);
        breezServiceLogger.debug('Loaded persisted service config', {
          hasApiKey: !!this.config?.apiKey,
          network: this.config?.network,
        });
      } else {
        breezServiceLogger.debug('No persisted service config found');
      }
    } catch (error) {
      this.logError('Failed to load persisted state', error);
      // Reset to clean state on error
      this.connectionInfo.isInitialized = false;
      this.config = null;
    }
  }

  private async persistConnectionInfo(): Promise<void> {
    try {
      await setItemSecureStore(
        SecureStorageKeys.BREEZ_CONNECTION_INFO,
        JSON.stringify(this.connectionInfo),
        { keychainAccessible: WHEN_UNLOCKED }
      );
    } catch (error) {
      this.logError('Failed to persist connection info', error);
    }
  }

  private async persistWalletInfo(walletInfo: WalletInfo): Promise<void> {
    try {
      await setItemSecureStore(
        SecureStorageKeys.BREEZ_WALLET_INFO,
        JSON.stringify(walletInfo),
        { keychainAccessible: WHEN_UNLOCKED }
      );
    } catch (error) {
      this.logError('Failed to persist wallet info', error);
    }
  }

  private async persistPaymentLimits(
    limits: BreezPaymentLimits
  ): Promise<void> {
    try {
      await setItemSecureStore(
        SecureStorageKeys.BREEZ_PAYMENT_LIMITS,
        JSON.stringify(limits),
        { keychainAccessible: WHEN_UNLOCKED }
      );
    } catch (error) {
      this.logError('Failed to persist payment limits', error);
    }
  }

  private async persistConfig(config: BreezSdkConfig): Promise<void> {
    try {
      await setItemSecureStore(
        SecureStorageKeys.BREEZ_SERVICE_CONFIG,
        JSON.stringify(config),
        { keychainAccessible: WHEN_UNLOCKED }
      );
    } catch (error) {
      this.logError('Failed to persist config', error);
    }
  }

  private logError(message: string, error: unknown, context?: string): void {
    const errorMessage = extractErrorMessage(error, message);
    this.lastError = `${message}: ${errorMessage}`;

    breezServiceLogger.error(message, error);

    const errorContext: BreezErrorContext = {
      operation: context || 'unknown',
      timestamp: Date.now(),
      connectionState: this.connectionInfo.state,
      retryCount: this.connectionInfo.retryCount,
      additionalInfo: { originalError: errorMessage },
    };

    this.emitEvent(ServiceEvent.ERROR_OCCURRED, {
      error: this.lastError,
      context: JSON.stringify(errorContext),
    });
  }

  private updateConnectionState(
    newState: BreezConnectionState,
    error?: string
  ): void {
    const previousState = this.connectionInfo.state;

    this.connectionInfo = updateConnectionState(
      this.connectionInfo,
      newState,
      error,
      'service connection state update'
    );

    if (error) {
      this.lastError = error;
    }

    if (newState === ConnectionState.CONNECTED) {
      this.connectionInfo.lastConnectedAt = Date.now();
    }

    this.persistConnectionInfo();

    this.emitEvent(ServiceEvent.CONNECTION_STATE_CHANGED, {
      previousState,
      currentState: newState,
      error,
    });
  }

  private emitEvent<T extends BreezServiceEvent>(
    event: T,
    payload: BreezServiceEventPayload[T]
  ): void {
    for (const [_, { event: listenerEvent, listener }] of this.eventListeners) {
      if (listenerEvent === event) {
        try {
          listener(event, payload);
        } catch (error) {
          this.logError(`Error in event listener for ${event}`, error);
        }
      }
    }
  }

  private async setupSdkEventListener(): Promise<void> {
    try {
      if (this.sdkEventListenerId) {
        await removeEventListener(this.sdkEventListenerId);
      }

      this.sdkEventListenerId = await addEventListener((sdkEvent: SdkEvent) => {
        this.handleSdkEvent(sdkEvent);
      });
    } catch (error) {
      this.logError('Failed to setup SDK event listener', error);
    }
  }

  private handleSdkEvent(sdkEvent: SdkEvent): void {
    breezServiceLogger.success('🔔 SDK Event received', {
      type: sdkEvent.type,
      timestamp: new Date().toISOString(),
    });

    switch (sdkEvent.type) {
      case SdkEventVariant.PAYMENT_SUCCEEDED:
        breezServiceLogger.success('💰 Payment succeeded event', {
          txId: sdkEvent.details?.txId,
          amountSat: sdkEvent.details?.amountSat,
          paymentType: sdkEvent.details?.paymentType,
        });
        this.handlePaymentSucceeded(sdkEvent.details);
        break;
      case SdkEventVariant.PAYMENT_FAILED:
        breezServiceLogger.warn('❌ Payment failed event', {
          txId: sdkEvent.details?.txId,
        });
        this.handlePaymentFailed(sdkEvent.details);
        break;
      case SdkEventVariant.PAYMENT_PENDING:
        breezServiceLogger.debug('⏳ Payment pending event', {
          txId: sdkEvent.details?.txId,
        });
        this.handlePaymentPending(sdkEvent.details);
        break;
      case SdkEventVariant.SYNCED:
        breezServiceLogger.success('🔄 Sync completed event');
        this.handleSyncCompleted();
        break;
      case SdkEventVariant.DATA_SYNCED:
        breezServiceLogger.success('📊 Data synced event', {
          didPullNewRecords: sdkEvent.didPullNewRecords,
        });
        this.handleDataSynced(sdkEvent.didPullNewRecords);
        break;
      default:
        breezServiceLogger.debug('❓ Unhandled SDK event', {
          type: sdkEvent.type,
        });
    }
  }

  private handlePaymentSucceeded(payment: Payment): void {
    this.updatePaymentInState(payment);
    this.updateServiceStats(payment);

    if (payment.paymentType === 'receive') {
      this.emitEvent(ServiceEvent.PAYMENT_RECEIVED, { payment });
    } else {
      this.emitEvent(ServiceEvent.PAYMENT_SENT, { payment });
    }
  }

  private handlePaymentFailed(payment: Payment): void {
    this.updatePaymentInState(payment);
    this.emitEvent(ServiceEvent.PAYMENT_FAILED, {
      payment,
      error: 'Payment failed',
    });
  }

  private handlePaymentPending(payment: Payment): void {
    this.updatePaymentInState(payment);
    // Emit event to notify store about pending payment update
    this.emitEvent(ServiceEvent.PAYMENT_PENDING, { payment });
  }

  private handleSyncCompleted(): void {
    this.walletState.lastSyncAt = Date.now();
    this.emitEvent(ServiceEvent.SYNC_COMPLETED, { didPullNewRecords: false });
  }

  private handleDataSynced(didPullNewRecords: boolean): void {
    this.walletState.lastSyncAt = Date.now();
    this.emitEvent(ServiceEvent.SYNC_COMPLETED, { didPullNewRecords });
  }

  private updatePaymentInState(payment: Payment): void {
    // Update pending payments
    this.walletState.pendingPayments = this.walletState.pendingPayments.filter(
      (p) => p.txId !== payment.txId
    );

    if (payment.status === 'pending') {
      this.walletState.pendingPayments.push(payment);
    }

    // Update recent payments
    const existingIndex = this.walletState.recentPayments.findIndex(
      (p) => p.txId === payment.txId
    );

    if (existingIndex >= 0) {
      this.walletState.recentPayments[existingIndex] = payment;
    } else {
      this.walletState.recentPayments.unshift(payment);
      // Keep only the 50 most recent payments
      this.walletState.recentPayments = this.walletState.recentPayments.slice(
        0,
        50
      );
    }
  }

  private updateServiceStats(payment: Payment): void {
    if (payment.paymentType === 'send') {
      this.serviceStats.totalPaymentsSent++;
    } else {
      this.serviceStats.totalPaymentsReceived++;
    }

    this.serviceStats.totalVolumeSat += payment.amountSat;
    this.serviceStats.totalFeesPaidSat += payment.feesSat;

    const totalPayments =
      this.serviceStats.totalPaymentsSent +
      this.serviceStats.totalPaymentsReceived;
    this.serviceStats.averagePaymentSizeSat =
      totalPayments > 0
        ? Math.round(this.serviceStats.totalVolumeSat / totalPayments)
        : 0;
  }

  // API Implementation

  /**
   * Initialize the Breez SDK service with configuration
   *
   * This method must be called before any other operations. It sets up the service
   * configuration, validates the API key, and prepares the service for connection.
   *
   * @param config - Configuration object for the Breez SDK
   * @param config.network - Liquid Network to connect to (MAINNET or TESTNET)
   * @param config.apiKey - API key for Breez SDK (optional, uses BREEZ_API_KEY environment variable if not provided)
   * @param config.retryAttempts - Number of retry attempts for failed operations (default: 3)
   * @param config.retryDelayMs - Delay between retry attempts in milliseconds (default: 2000)
   * @param config.connectionTimeoutMs - Connection timeout in milliseconds (default: 30000)
   * @param config.enableLogging - Enable debug logging (default: __DEV__)
   * @param config.workingDir - Working directory for SDK data storage (optional)
   *
   * @returns Promise resolving to service response with success status
   *
   * @throws {Error} When BREEZ_API_KEY environment variable is not set
   *
   * @example
   * ```typescript
   * // Using environment variable (recommended)
   * const result = await breezService.initialize({
   *   network: LiquidNetwork.TESTNET,
   *   enableLogging: true
   * });
   *
   * // Or providing API key directly
   * const result = await breezService.initialize({
   *   network: LiquidNetwork.TESTNET,
   *   apiKey: 'your-api-key-here',
   *   enableLogging: true
   * });
   *
   * if (result.success) {
   *   console.log('Service initialized successfully');
   * } else {
   *   console.error('Initialization failed:', result.error);
   * }
   * ```
   */
  async initialize(config: BreezSdkConfig): Promise<BreezServiceResponse> {
    return await progressiveRetryManager
      .executeInitializationRetry(async () => {
        // Check if already initialized AND properly configured
        if (this.connectionInfo.isInitialized) {
          breezServiceLogger.debug(
            'Service marked as initialized, validating configuration'
          );

          // Force reload persisted state to ensure we have latest config
          this.loadPersistedStateSync();

          // Validate that we have a proper configuration
          const healthCheck = await this.validateServiceHealth();
          if (healthCheck.isHealthy) {
            breezServiceLogger.debug(
              'Already initialized with valid configuration, skipping initialization'
            );
            return { success: true };
          }
          breezServiceLogger.warn(
            'Service marked as initialized but configuration is invalid, re-initializing',
            {
              issues: healthCheck.issues,
            }
          );
          // Reset the initialization flag and continue with initialization
          this.connectionInfo.isInitialized = false;
          this.config = null;
        }

        // Use provided API key or fall back to environment variable
        const apiKey = config.apiKey ?? API_KEY;

        breezServiceLogger.debug('API key validation', {
          hasProvidedApiKey: !!config.apiKey,
          hasEnvironmentApiKey: !!API_KEY,
          finalApiKey: !!apiKey,
          environmentValue: process.env.BREEZ_API_KEY ? 'present' : 'missing',
          apiKeyLength: apiKey ? apiKey.length : 0,
        });

        if (!apiKey) {
          throw new Error(BREEZ_ERROR_MESSAGES.API_KEY_REQUIRED);
        }

        // Validate API key format (basic validation)
        if (typeof apiKey !== 'string' || apiKey.trim().length === 0) {
          throw new Error(BREEZ_ERROR_MESSAGES.API_KEY_INVALID);
        }

        breezServiceLogger.operation('Initializing Breez SDK');

        return await this.performInitialization(config, apiKey);
      }, 'breez_sdk_initialize')
      .then((result) =>
        result.success
          ? { success: true }
          : { success: false, error: result.error }
      );
  }

  private async performInitialization(
    config: BreezSdkConfig,
    apiKey: string
  ): Promise<BreezServiceResponse> {
    try {
      const defaultNetwork =
        DEFAULT_NETWORK === WALLET_NETWORKS.TESTNET
          ? LiquidNetwork.TESTNET
          : LiquidNetwork.MAINNET;

      this.config = {
        ...config,
        retryAttempts: config.retryAttempts ?? 3,
        retryDelayMs: config.retryDelayMs ?? 2000,
        connectionTimeoutMs: config.connectionTimeoutMs ?? 30_000,
        enableLogging: config.enableLogging ?? __DEV__,
        network: config.network ?? defaultNetwork,
        apiKey,
      };

      // Validate the SDK can create a config with these parameters
      // This ensures the SDK is actually ready and the parameters are valid
      breezServiceLogger.debug('Validating SDK configuration');
      const testSdkConfig = await defaultConfig(
        this.config.network,
        this.config.apiKey
      );

      if (!testSdkConfig) {
        throw new Error(BREEZ_ERROR_MESSAGES.SDK_CONFIG_FAILED);
      }

      breezServiceLogger.debug('SDK configuration validated successfully');

      // Persist configuration
      await this.persistConfig(this.config);

      // Only mark as initialized after successful SDK validation
      this.connectionInfo.isInitialized = true;
      await this.persistConnectionInfo();

      breezServiceLogger.success(
        'Initialization completed successfully with SDK validation'
      );

      return { success: true };
    } catch (error) {
      // Reset initialization state on failure
      this.connectionInfo.isInitialized = false;
      this.config = null;

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown initialization error';
      breezServiceLogger.error('Initialization failed', {
        error: errorMessage,
      });

      return {
        success: false,
        error: `Initialization failed: ${errorMessage}`,
      };
    }
  }

  /**
   * Connect to the Breez SDK with a mnemonic phrase
   *
   * Establishes a connection to the Liquid Network using the provided mnemonic.
   * If no mnemonic is provided, attempts to use one from secure storage.
   * This method creates or restores a wallet and sets up event listeners.
   *
   * @param mnemonic - Optional 12-word mnemonic phrase. If not provided, uses stored mnemonic
   *
   * @returns Promise resolving to service response with success status
   *
   * @throws {Error} When service is not initialized or mnemonic is invalid
   *
   * @example
   * ```typescript
   * // Connect with a new mnemonic
   * const mnemonic = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
   * const result = await breezService.connect(mnemonic);
   *
   * // Connect with stored mnemonic
   * const result = await breezService.connect();
   *
   * if (result.success) {
   *   console.log('Connected to Breez SDK');
   * } else {
   *   console.error('Connection failed:', result.error);
   * }
   * ```
   *
   * @sideEffects
   * - Stores mnemonic in secure storage
   * - Sets up SDK event listeners
   * - Updates connection state
   * - Fetches initial wallet info and payment limits
   */
  async connect(mnemonic?: string): Promise<BreezServiceResponse> {
    // Enhanced validation before connection attempt
    const healthCheck = await this.validateServiceHealth();
    if (!healthCheck.isHealthy) {
      const errorMessage = `Service not properly initialized: ${healthCheck.issues.join(', ')}`;
      breezServiceLogger.error(
        'Connection failed - service health check failed',
        {
          issues: healthCheck.issues,
        }
      );
      return {
        success: false,
        error: errorMessage,
      };
    }

    // Check if already connected
    if (this.walletState.isConnected) {
      breezServiceLogger.debug('Already connected, skipping connection');
      return { success: true };
    }

    // Check if connection is in progress - wait for it to complete
    if (this.connectionInfo.state === ConnectionState.CONNECTING) {
      breezServiceLogger.debug(
        'Connection already in progress, waiting for completion'
      );
      return await this.waitForExistingConnection();
    }

    // Use progressive retry for connection
    return await progressiveRetryManager
      .executeConnectionRetry(async () => {
        return await this.performConnection(mnemonic);
      }, 'breez_sdk_connect')
      .then((result) =>
        result.success
          ? { success: true }
          : { success: false, error: result.error }
      );
  }

  private async waitForExistingConnection(): Promise<BreezServiceResponse> {
    const maxWaitTime = 30_000;
    const startTime = Date.now();

    await new Promise<void>((resolve) => {
      const checkConnection = () => {
        if (
          this.connectionInfo.state !== ConnectionState.CONNECTING ||
          Date.now() - startTime > maxWaitTime
        ) {
          resolve();
        } else {
          setTimeout(checkConnection, 500);
        }
      };
      checkConnection();
    });

    // Check final state
    if (this.walletState.isConnected) {
      breezServiceLogger.success('Existing connection completed successfully');
      return { success: true };
    }
    breezServiceLogger.warn('Existing connection failed or timed out');
    return {
      success: false,
      error: 'Existing connection failed or timed out',
    };
  }

  private async performConnection(
    mnemonic?: string
  ): Promise<BreezServiceResponse> {
    breezServiceLogger.operation('Starting connection to Breez SDK');

    this.updateConnectionState(ConnectionState.CONNECTING);

    try {
      // Get or generate mnemonic
      let walletMnemonic = mnemonic;
      if (!walletMnemonic) {
        walletMnemonic =
          getItemSecureStore(SecureStorageKeys.MNEMONIC) || undefined;
      }

      if (!walletMnemonic) {
        return {
          success: false,
          error: BREEZ_ERROR_MESSAGES.NO_MNEMONIC,
        };
      }

      // Create SDK config
      const sdkConfig: Config = await defaultConfig(
        this.config?.network || LiquidNetwork.TESTNET,
        this.config?.apiKey
      );

      if (this.config?.workingDir) {
        sdkConfig.workingDir = this.config.workingDir;
      }

      // Connect to SDK
      const connectRequest: ConnectRequest = {
        config: sdkConfig,
        mnemonic: walletMnemonic,
      };

      await connect(connectRequest);

      // Store mnemonic securely (using primary MNEMONIC key)
      await setItemSecureStore(SecureStorageKeys.MNEMONIC, walletMnemonic, {
        keychainAccessible: WHEN_UNLOCKED,
      });

      // Setup event listener
      await this.setupSdkEventListener();

      // Update state
      this.walletState.isConnected = true;
      this.updateConnectionState(ConnectionState.CONNECTED);

      // Clear any previous errors after successful connection
      this.lastError = null;

      // Give the SDK a moment to fully establish connection before fetching data
      // This helps prevent "Too many retry" errors during initial sync
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Fetch initial wallet info
      await this.refreshWalletInfo();

      // Fetch payment limits
      await this.refreshPaymentLimits();

      // Start health monitoring
      connectionHealthMonitor.startMonitoring(this, null);

      return { success: true };
    } catch (error) {
      this.updateConnectionState(
        ConnectionState.FAILED,
        error instanceof Error
          ? error.message
          : BREEZ_ERROR_MESSAGES.CONNECTION_FAILED
      );
      this.logError(
        'Failed to connect to Breez SDK',
        error,
        BREEZ_OPERATIONS.CONNECT
      );

      useErrorStore.getState().addError(ErrorKey.BreezConnectionError, {
        key: ErrorKey.BreezConnectionError,
        errorText:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.CONNECTION_FAILED,
        translate: false,
      });

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.CONNECTION_FAILED,
      };
    }
  }

  async disconnect(): Promise<BreezServiceResponse> {
    try {
      this.updateConnectionState(ConnectionState.DISCONNECTED);

      // Clear retry timeouts
      for (const timeout of this.retryTimeouts) {
        clearTimeout(timeout);
      }
      this.retryTimeouts.clear();

      // Remove SDK event listener
      if (this.sdkEventListenerId) {
        await removeEventListener(this.sdkEventListenerId);
        this.sdkEventListenerId = null;
      }

      // Disconnect from SDK
      await disconnect();

      // Update state
      this.walletState.isConnected = false;
      this.walletState.walletInfo = undefined;

      return { success: true };
    } catch (error) {
      this.logError(
        'Failed to disconnect from Breez SDK',
        error,
        BREEZ_OPERATIONS.DISCONNECT
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.DISCONNECT_FAILED,
      };
    }
  }

  async reconnect(): Promise<BreezServiceResponse> {
    this.updateConnectionState(ConnectionState.RECONNECTING);

    try {
      await this.disconnect();
      const mnemonic = getItemSecureStore(SecureStorageKeys.MNEMONIC);
      return await this.connect(mnemonic || undefined);
    } catch (error) {
      this.updateConnectionState(
        ConnectionState.FAILED,
        error instanceof Error
          ? error.message
          : BREEZ_ERROR_MESSAGES.RECONNECTION_FAILED
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.RECONNECTION_FAILED,
      };
    }
  }

  private async refreshWalletInfo(): Promise<void> {
    try {
      const info = await getInfo();
      this.walletState.walletInfo = info.walletInfo;
      await this.persistWalletInfo(info.walletInfo);

      this.emitEvent(ServiceEvent.WALLET_INFO_UPDATED, {
        walletInfo: info.walletInfo,
      });
    } catch (error) {
      this.logError(
        'Failed to refresh wallet info',
        error,
        BREEZ_OPERATIONS.REFRESH_WALLET_INFO
      );
    }
  }

  private async refreshPaymentLimits(): Promise<void> {
    try {
      const [lightning, onchain] = await Promise.all([
        fetchLightningLimits(),
        fetchOnchainLimits(),
      ]);

      this.paymentLimits = {
        lightning,
        onchain,
        lastUpdatedAt: Date.now(),
      };

      await this.persistPaymentLimits(this.paymentLimits);
    } catch (error) {
      this.logError(
        'Failed to refresh payment limits',
        error,
        BREEZ_OPERATIONS.GET_PAYMENT_LIMITS
      );
    }
  }

  getConnectionInfo(): BreezConnectionInfo {
    return { ...this.connectionInfo };
  }

  /**
   * Get current wallet state
   */
  getWalletState(): BreezWalletState {
    return { ...this.walletState };
  }

  /**
   * Check if the service is properly initialized
   * Enhanced validation to ensure actual SDK readiness
   */
  isInitialized(): boolean {
    const basicCheck =
      this.connectionInfo.isInitialized && this.config !== null;

    if (!basicCheck) {
      return false;
    }

    // Additional validation: ensure config has required fields
    const configValid = this.config?.apiKey && this.config?.network;

    if (!configValid) {
      breezServiceLogger.warn(
        'Service marked as initialized but config is invalid',
        {
          hasApiKey: !!this.config?.apiKey,
          hasNetwork: !!this.config?.network,
        }
      );
      return false;
    }

    return true;
  }

  /**
   * Comprehensive service health check
   * Validates both initialization and actual SDK readiness
   */
  async validateServiceHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];

    // Check basic initialization
    if (!this.connectionInfo.isInitialized) {
      issues.push(DIAGNOSTIC_MESSAGES.SERVICE_NOT_INITIALIZED);
    }

    if (!this.config) {
      issues.push('Service config missing');
    }

    if (!this.config?.apiKey) {
      issues.push('API key missing');
    }

    if (!this.config?.network) {
      issues.push('Network config missing');
    }

    // Check if we can create a basic SDK config (validates SDK readiness)
    if (this.config?.apiKey && this.config?.network) {
      try {
        await defaultConfig(this.config.network, this.config.apiKey);
      } catch (error) {
        issues.push(
          `SDK config creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    return {
      isHealthy: issues.length === 0,
      issues,
    };
  }

  /**
   * Reset the service state (for testing purposes)
   * @internal
   */
  resetForTesting(): void {
    if (__DEV__) {
      this.connectionInfo.isInitialized = false;
      this.config = null;
      this.walletState.isConnected = false;
      this.updateConnectionState(ConnectionState.DISCONNECTED);
      connectionHealthMonitor.stopMonitoring();
      breezServiceLogger.debug('Service state reset for testing');
    }
  }

  /**
   * Force fresh initialization (ignores current state)
   * Useful for debugging and recovery
   */
  async forceInitialize(
    config?: BreezSdkConfig
  ): Promise<BreezServiceResponse> {
    breezServiceLogger.warn('Forcing fresh initialization');

    // Reset state first
    this.connectionInfo.isInitialized = false;
    this.config = null;

    // Use provided config or create default
    const initConfig = config || {
      network: DEFAULT_NETWORK as any,
      enableLogging: __DEV__,
    };

    return await this.initialize(initConfig);
  }

  /**
   * Force reset service state when inconsistencies are detected
   * This is a recovery mechanism for production use
   */
  async forceStateReset(): Promise<void> {
    breezServiceLogger.warn(
      'Forcing service state reset due to inconsistencies'
    );

    try {
      // Stop any ongoing operations
      connectionHealthMonitor.stopMonitoring();

      // Clear all timeouts
      this.retryTimeouts.forEach((timeout) => clearTimeout(timeout));
      this.retryTimeouts.clear();

      // Reset internal state
      this.connectionInfo = {
        state: ConnectionState.DISCONNECTED,
        retryCount: 0,
        isInitialized: false,
      };

      this.walletState = {
        isConnected: false,
        pendingPayments: [],
        recentPayments: [],
      };

      this.config = null;
      this.lastError = null;

      // Clear persisted state
      try {
        await this.clearPersistedState();
      } catch (error) {
        breezServiceLogger.warn(
          'Failed to clear persisted state during reset',
          error
        );
      }

      breezServiceLogger.success('Service state reset completed');
    } catch (error) {
      breezServiceLogger.error('Error during force state reset', error);
      throw error;
    }
  }

  /**
   * Detect and recover from state inconsistencies
   */
  async detectAndRecoverInconsistencies(): Promise<{
    recovered: boolean;
    actions: string[];
  }> {
    const actions: string[] = [];

    try {
      const healthCheck = await this.validateServiceHealth();

      if (!healthCheck.isHealthy) {
        actions.push('Service health check failed');

        // Check if it's a recoverable issue
        const hasBasicConfig = this.connectionInfo.isInitialized && this.config;

        if (
          hasBasicConfig &&
          healthCheck.issues.some((issue) =>
            issue.includes('SDK config creation failed')
          )
        ) {
          actions.push('Attempting to recover SDK configuration');

          try {
            // Try to recreate SDK config
            if (this.config?.apiKey && this.config?.network) {
              await defaultConfig(this.config.network, this.config.apiKey);
              actions.push('SDK configuration recovered successfully');
              return { recovered: true, actions };
            }
          } catch (error) {
            actions.push(
              `SDK configuration recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
          }
        }

        // If we can't recover, force reset
        actions.push('Performing force state reset');
        await this.forceStateReset();
        return { recovered: true, actions };
      }

      return { recovered: true, actions: ['No inconsistencies detected'] };
    } catch (error) {
      actions.push(
        `Recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      return { recovered: false, actions };
    }
  }

  /**
   * Clear all persisted state
   */
  private async clearPersistedState(): Promise<void> {
    const keysToRemove = [
      SecureStorageKeys.BREEZ_SERVICE_CONFIG,
    ];

    for (const key of keysToRemove) {
      try {
        await setItemSecureStore(key, '', {
          keychainAccessible: WHEN_UNLOCKED,
        });
      } catch (error) {
        breezServiceLogger.warn(`Failed to clear persisted key ${key}`, error);
      }
    }
  }

  /**
   * Force reset service state (for recovery purposes)
   * @internal
   */
  async forceResetState(): Promise<void> {
    breezServiceLogger.warn('Performing force reset of service state');

    try {
      // Stop health monitoring
      connectionHealthMonitor.stopMonitoring();

      // Disconnect if connected
      if (this.walletState.isConnected) {
        await this.disconnect();
      }

      // Reset state
      this.connectionInfo = {
        state: ConnectionState.DISCONNECTED,
        retryCount: 0,
        isInitialized: false,
      };

      this.walletState = {
        isConnected: false,
        pendingPayments: [],
        recentPayments: [],
      };

      this.config = null;
      this.lastError = null;

      breezServiceLogger.success('Service state force reset completed');
    } catch (error) {
      breezServiceLogger.error('Force reset failed', error);
    }
  }

  async getWalletInfo(): Promise<BreezServiceResponse<WalletInfo>> {
    try {
      if (!this.walletState.isConnected) {
        return { success: false, error: BREEZ_ERROR_MESSAGES.NOT_CONNECTED };
      }

      await this.refreshWalletInfo();

      if (!this.walletState.walletInfo) {
        return {
          success: false,
          error: BREEZ_ERROR_MESSAGES.WALLET_INFO_UNAVAILABLE,
        };
      }

      return { success: true, data: this.walletState.walletInfo };
    } catch (error) {
      this.logError(
        'Failed to get wallet info',
        error,
        BREEZ_OPERATIONS.GET_WALLET_INFO
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.WALLET_INFO_FAILED,
      };
    }
  }

  /**
   * Pre-fetch essential wallet data after connection
   * This replaces the problematic sync() method with individual fetches
   */
  async preFetchWalletData(): Promise<BreezServiceResponse> {
    try {
      if (!this.walletState.isConnected) {
        return { success: false, error: BREEZ_ERROR_MESSAGES.NOT_CONNECTED };
      }

      breezServiceLogger.debug('Starting wallet data pre-fetch');

      // Fetch wallet info (balance, etc.)
      try {
        await this.refreshWalletInfo();
        breezServiceLogger.debug('Wallet info pre-fetch successful');
      } catch (error) {
        breezServiceLogger.warn('Failed to pre-fetch wallet info', error);
      }

      // Fetch payment limits
      try {
        await this.refreshPaymentLimits();
        breezServiceLogger.debug('Payment limits pre-fetch successful');
      } catch (error) {
        breezServiceLogger.warn('Failed to pre-fetch payment limits', error);
      }

      // Fetch recent payments to ensure we have latest status
      try {
        await this.refreshRecentPayments();
        breezServiceLogger.debug('Recent payments pre-fetch successful');
      } catch (error) {
        breezServiceLogger.warn('Failed to pre-fetch recent payments', error);
      }

      breezServiceLogger.debug('Wallet data pre-fetch completed');
      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Pre-fetch failed';
      breezServiceLogger.error('Wallet data pre-fetch failed', error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Refresh recent payments to catch status updates
   */
  private async refreshRecentPayments(): Promise<void> {
    try {
      const result = await this.listPayments({ limit: 50 });
      if (result.success && result.data) {
        // Update internal state with fresh payment data
        this.walletState.recentPayments = result.data;

        // Check for any pending payments that might have changed status
        const pendingPayments = result.data.filter(
          (p) => p.status === PAYMENT_STATUS.PENDING
        );
        this.walletState.pendingPayments = pendingPayments;

        // Emit events for any payments that have changed status
        this.checkForPaymentStatusChanges(result.data);

        breezServiceLogger.debug('Recent payments refreshed', {
          totalPayments: result.data.length,
          pendingPayments: pendingPayments.length,
        });
      }
    } catch (error) {
      breezServiceLogger.warn('Failed to refresh recent payments', error);
    }
  }

  /**
   * Check for payment status changes and emit appropriate events
   */
  private checkForPaymentStatusChanges(freshPayments: Payment[]): void {
    const previousPayments = new Map(
      this.walletState.recentPayments.map((p) => [p.txId, p])
    );

    for (const payment of freshPayments) {
      const previousPayment = previousPayments.get(payment.txId);

      if (previousPayment && previousPayment.status !== payment.status) {
        breezServiceLogger.debug('Payment status changed', {
          txId: payment.txId,
          previousStatus: previousPayment.status,
          newStatus: payment.status,
        });

        // Emit appropriate event based on new status
        switch (payment.status) {
          case PAYMENT_STATUS.COMPLETE:
            if (payment.paymentType === PAYMENT_TYPES.RECEIVE) {
              this.emitEvent(ServiceEvent.PAYMENT_RECEIVED, { payment });
            } else {
              this.emitEvent(ServiceEvent.PAYMENT_SENT, { payment });
            }
            break;
          case PAYMENT_STATUS.FAILED:
            this.emitEvent(ServiceEvent.PAYMENT_FAILED, {
              payment,
              error: 'Payment failed',
            });
            break;
          case PAYMENT_STATUS.PENDING:
            this.emitEvent(ServiceEvent.PAYMENT_PENDING, { payment });
            break;
        }
      }
    }
  }
  /**
   * Create a payment invoice for receiving funds
   *
   * Generates a payment request (invoice) that can be used to receive payments
   * on the Liquid Network. The invoice includes the amount, description, and
   * payment method details.
   *
   * @param request - Invoice creation request
   * @param request.amountSat - Amount to receive in satoshis
   * @param request.description - Optional description for the payment
   * @param request.paymentMethod - Payment method (default: BOLT11_INVOICE)
   *
   * @returns Promise resolving to service response with invoice details
   *
   * @throws {Error} When not connected to Breez SDK or invoice creation fails
   *
   * @example
   * ```typescript
   * const result = await breezService.createInvoice({
   *   amountSat: 100000, // 0.001 BTC
   *   description: 'Coffee payment',
   *   paymentMethod: PaymentMethod.BOLT11_INVOICE
   * });
   *
   * if (result.success && result.data) {
   *   console.log('Invoice created:', result.data.destination);
   *   console.log('Fees:', result.data.feesSat, 'sats');
   * }
   * ```
   */
  async createInvoice(
    request: CreateInvoiceRequest
  ): Promise<BreezServiceResponse<CreateInvoiceResponse>> {
    try {
      if (!this.walletState.isConnected) {
        return { success: false, error: BREEZ_ERROR_MESSAGES.NOT_CONNECTED };
      }

      const prepareRequest: PrepareReceiveRequest = {
        paymentMethod: request.paymentMethod || PaymentMethod.BOLT11_INVOICE,
        amount:
          request.amountSat > 0
            ? {
              type: ReceiveAmountVariant.BITCOIN,
              payerAmountSat: request.amountSat,
            }
            : undefined, // No amount generates a plain address for Liquid
      };

      const prepareResponse = await prepareReceivePayment(prepareRequest);

      const receiveRequest: ReceivePaymentRequest = {
        prepareResponse,
        description: request.description,
      };

      const receiveResponse = await receivePayment(receiveRequest);

      const result: CreateInvoiceResponse = {
        destination: receiveResponse.destination,
        paymentMethod: prepareResponse.paymentMethod,
        amountSat: request.amountSat,
        feesSat: prepareResponse.feesSat,
      };

      return { success: true, data: result };
    } catch (error) {
      this.logError(
        'Failed to create invoice',
        error,
        BREEZ_OPERATIONS.CREATE_INVOICE
      );
      useErrorStore.getState().addError(ErrorKey.BreezPaymentError, {
        key: ErrorKey.BreezPaymentError,
        errorText:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.INVOICE_CREATION_FAILED,
        translate: false,
      });
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.INVOICE_CREATION_FAILED,
      };
    }
  }

  async getPaymentLimits(): Promise<BreezServiceResponse<BreezPaymentLimits>> {
    try {
      if (!this.walletState.isConnected) {
        return { success: false, error: BREEZ_ERROR_MESSAGES.NOT_CONNECTED };
      }

      // Refresh limits if they're older than 5 minutes
      const shouldRefresh =
        !this.paymentLimits ||
        Date.now() - this.paymentLimits.lastUpdatedAt > 5 * 60 * 1000;

      if (shouldRefresh) {
        await this.refreshPaymentLimits();
      }

      if (!this.paymentLimits) {
        return {
          success: false,
          error: BREEZ_ERROR_MESSAGES.PAYMENT_LIMITS_UNAVAILABLE,
        };
      }

      return { success: true, data: this.paymentLimits };
    } catch (error) {
      this.logError(
        'Failed to get payment limits',
        error,
        BREEZ_OPERATIONS.GET_PAYMENT_LIMITS
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.PAYMENT_LIMITS_FAILED,
      };
    }
  }

  async listPayments(filters?: {
    types?: string[];
    states?: string[];
    limit?: number;
    offset?: number;
  }): Promise<BreezServiceResponse<Payment[]>> {
    try {
      if (!this.walletState.isConnected) {
        return { success: false, error: BREEZ_ERROR_MESSAGES.NOT_CONNECTED };
      }

      const request = {
        filters: filters?.types as PaymentType[],
        states: filters?.states as PaymentState[],
        limit: filters?.limit,
        offset: filters?.offset,
      };

      const payments = await listPayments(request);
      return { success: true, data: payments };
    } catch (error) {
      this.logError(
        'Failed to list payments',
        error,
        BREEZ_OPERATIONS.LIST_PAYMENTS
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : BREEZ_ERROR_MESSAGES.PAYMENTS_LOAD_FAILED,
      };
    }
  }

  getServiceStats(): BreezServiceStats {
    return { ...this.serviceStats };
  }

  addEventListener<T extends BreezServiceEvent>(
    event: T,
    listener: BreezEventListener<T>
  ): string {
    const listenerId = `${event}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    this.eventListeners.set(listenerId, { event, listener });
    return listenerId;
  }

  removeEventListener(listenerId: string): void {
    this.eventListeners.delete(listenerId);
  }

  isHealthy(): boolean {
    // Service is healthy if connected, even if there are sync errors
    // Sync errors don't affect core wallet functionality
    const isConnected =
      this.connectionInfo.state === ConnectionState.CONNECTED &&
      this.walletState.isConnected;

    const hasNonSyncError =
      this.lastError &&
      !this.lastError.includes('Sync timeout') &&
      !this.lastError.includes('sync failed');

    return isConnected && !hasNonSyncError;
  }

  getLastError(): string | null {
    return this.lastError;
  }
}

export const breezSdkService = BreezSdkService.getInstance();
export default breezSdkService;
