import { createLogger } from '@utils/logger';
import type {
  BinanceTickerResponse,
  BitcoinPrice,
  CacheEntry,
  ExchangeRateApiResponse,
  ExchangeRateConfig,
  ExchangeRateEvent,
  ExchangeRateEventListener,
  ExchangeRateEventPayload,
  ExchangeRateResponse,
  ExchangeRates,
  IExchangeRateService,
  PriceData,
  SupportedCurrency,
} from 'types/exchange-rate.types';
import {
  DEFAULT_EXCHANGE_RATE_CONFIG,
  FALLBACK_BITCOIN_PRICE,
  SupportedCurrencies,
} from 'types/exchange-rate.types';

const exchangeRateLogger = createLogger('ExchangeRateService');

class ExchangeRateService implements IExchangeRateService {
  private static instance: ExchangeRateService;

  private config: Required<ExchangeRateConfig> = DEFAULT_EXCHANGE_RATE_CONFIG;
  private isInitialized = false;

  private bitcoinPriceCache: CacheEntry<BitcoinPrice> | null = null;
  private exchangeRatesCache: CacheEntry<ExchangeRates> | null = null;

  private eventListeners: Map<
    string,
    {
      event: ExchangeRateEvent;
      listener: ExchangeRateEventListener<any>;
    }
  > = new Map();
  private listenerIdCounter = 0;

  private lastError: string | null = null;
  private serviceStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
    lastUpdateTime: null as number | null,
  };

  private readonly API_ENDPOINTS = {
    binance: 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
    exchangeRateApi: 'https://api.exchangerate-api.com/v4/latest/USD',
  };

  private constructor() { }

  static getInstance(): ExchangeRateService {
    if (!ExchangeRateService.instance) {
      ExchangeRateService.instance = new ExchangeRateService();
    }
    return ExchangeRateService.instance;
  }

  initialize(config?: ExchangeRateConfig): boolean {
    try {
      this.config = { ...DEFAULT_EXCHANGE_RATE_CONFIG, ...config };
      this.isInitialized = true;

      if (this.config.enableLogging) {
        exchangeRateLogger.operation('Exchange rate service initialized', {
          cacheDuration: this.config.cacheDuration,
          timeout: this.config.timeout,
        });
      }

      return true;
    } catch (error) {
      this.lastError =
        error instanceof Error ? error.message : 'Initialization failed';
      exchangeRateLogger.error(
        'Failed to initialize exchange rate service',
        error
      );
      return false;
    }
  }

  async getBitcoinPrice(): Promise<ExchangeRateResponse<BitcoinPrice>> {
    try {
      // Check internal cache first
      if (this.bitcoinPriceCache && this.isCacheValid(this.bitcoinPriceCache)) {
        this.serviceStats.cacheHits++;
        return {
          success: true,
          data: this.bitcoinPriceCache.data,
          source: 'cache',
        };
      }

      const cacheStatus = this.getCacheStatus();
      if (cacheStatus.bitcoinPrice) {
        this.serviceStats.cacheHits++;
        return {
          success: true,
          data: cacheStatus.bitcoinPrice.data,
          source: 'app-cache',
        };
      }

      this.serviceStats.totalRequests++;

      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.timeout
      );

      const response = await fetch(this.API_ENDPOINTS.binance, {
        signal: controller.signal,
        headers: {
          Accept: 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Binance API error: ${response.status} ${response.statusText}`
        );
      }

      const data: BinanceTickerResponse = await response.json();

      const bitcoinPrice: BitcoinPrice = {
        usd: Number.parseFloat(data.price),
        timestamp: Date.now(),
        source: 'binance',
      };

      this.bitcoinPriceCache = {
        data: bitcoinPrice,
        timestamp: Date.now(),
        expiresAt: Date.now() + this.config.cacheDuration,
      };

      this.serviceStats.successfulRequests++;
      this.serviceStats.lastUpdateTime = Date.now();

      if (this.config.enableLogging) {
        exchangeRateLogger.debug('Bitcoin price fetched from Binance', {
          price: bitcoinPrice.usd,
          source: bitcoinPrice.source,
        });
      }

      return {
        success: true,
        data: bitcoinPrice,
        source: 'binance',
      };
    } catch (error) {
      this.serviceStats.failedRequests++;
      this.lastError =
        error instanceof Error
          ? error.message
          : 'Failed to fetch Bitcoin price';

      exchangeRateLogger.warn(
        'Failed to fetch Bitcoin price from Binance, using fallback',
        error
      );

      const fallbackPrice: BitcoinPrice = {
        usd: FALLBACK_BITCOIN_PRICE,
        timestamp: Date.now(),
        source: 'fallback',
      };

      return {
        success: true,
        data: fallbackPrice,
        source: 'fallback',
      };
    }
  }

  async getExchangeRates(): Promise<ExchangeRateResponse<ExchangeRates>> {
    try {
      // Check internal cache first
      if (
        this.exchangeRatesCache &&
        this.isCacheValid(this.exchangeRatesCache)
      ) {
        this.serviceStats.cacheHits++;
        return {
          success: true,
          data: this.exchangeRatesCache.data,
          source: 'cache',
        };
      }

      this.serviceStats.totalRequests++;

      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.timeout
      );

      const response = await fetch(this.API_ENDPOINTS.exchangeRateApi, {
        signal: controller.signal,
        headers: {
          Accept: 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `ExchangeRate-API error: ${response.status} ${response.statusText}`
        );
      }

      const data: ExchangeRateApiResponse = await response.json();

      const exchangeRates: ExchangeRates = {
        base: 'USD',
        rates: {
          USD: 1,
          PLN: data.rates.PLN || this.config.fallbackRates.PLN,
          EUR: data.rates.EUR || this.config.fallbackRates.EUR,
          GBP: data.rates.GBP || this.config.fallbackRates.GBP,
        },
        timestamp: Date.now(),
        source: 'exchangerate-api',
      };

      this.exchangeRatesCache = {
        data: exchangeRates,
        timestamp: Date.now(),
        expiresAt: Date.now() + this.config.cacheDuration,
      };

      this.serviceStats.successfulRequests++;
      this.serviceStats.lastUpdateTime = Date.now();

      if (this.config.enableLogging) {
        exchangeRateLogger.debug('Exchange rates fetched', {
          rates: exchangeRates.rates,
          source: exchangeRates.source,
        });
      }

      return {
        success: true,
        data: exchangeRates,
        source: 'exchangerate-api',
      };
    } catch (error) {
      this.serviceStats.failedRequests++;
      this.lastError =
        error instanceof Error
          ? error.message
          : 'Failed to fetch exchange rates';

      exchangeRateLogger.warn(
        'Failed to fetch exchange rates, using fallback',
        error
      );

      const fallbackRates: ExchangeRates = {
        base: 'USD',
        rates: this.config.fallbackRates as Record<SupportedCurrency, number>,
        timestamp: Date.now(),
        source: 'fallback',
      };

      return {
        success: true,
        data: fallbackRates,
        source: 'fallback',
      };
    }
  }

  async getPriceData(): Promise<ExchangeRateResponse<PriceData>> {
    try {
      const [bitcoinPriceResult, exchangeRatesResult] = await Promise.all([
        this.getBitcoinPrice(),
        this.getExchangeRates(),
      ]);

      if (!(bitcoinPriceResult.success && exchangeRatesResult.success)) {
        throw new Error('Failed to fetch required data');
      }

      const bitcoinPrice = bitcoinPriceResult.data!;
      const exchangeRates = exchangeRatesResult.data!;

      const bitcoin: Record<SupportedCurrency, number> = {} as any;

      for (const currency of Object.values(SupportedCurrencies)) {
        bitcoin[currency] = bitcoinPrice.usd * exchangeRates.rates[currency];
      }

      const priceData: PriceData = {
        bitcoin,
        lastUpdated: Math.min(bitcoinPrice.timestamp, exchangeRates.timestamp),
        isFromCache:
          bitcoinPriceResult.source === 'cache' &&
          exchangeRatesResult.source === 'cache',
        sources: {
          bitcoinPrice: bitcoinPrice.source,
          exchangeRates: exchangeRates.source,
        },
      };

      this.emitEvent('PRICE_UPDATED', { priceData });

      return {
        success: true,
        data: priceData,
      };
    } catch (error) {
      this.lastError =
        error instanceof Error ? error.message : 'Failed to get price data';
      exchangeRateLogger.error('Failed to get price data', error);

      return {
        success: false,
        error: this.lastError,
      };
    }
  }

  async convertSatoshisToCurrency(
    satoshis: number,
    currency: SupportedCurrency
  ): Promise<number> {
    const priceDataResult = await this.getPriceData();

    if (!(priceDataResult.success && priceDataResult.data)) {
      const fallbackBtcPrice =
        FALLBACK_BITCOIN_PRICE * (this.config.fallbackRates[currency] || 1);
      return (satoshis / 100_000_000) * fallbackBtcPrice;
    }

    return (satoshis / 100_000_000) * priceDataResult.data.bitcoin[currency];
  }

  async convertBtcToCurrency(
    btc: number,
    currency: SupportedCurrency
  ): Promise<number> {
    const priceDataResult = await this.getPriceData();

    if (!(priceDataResult.success && priceDataResult.data)) {
      const fallbackBtcPrice =
        FALLBACK_BITCOIN_PRICE * (this.config.fallbackRates[currency] || 1);
      return btc * fallbackBtcPrice;
    }

    return btc * priceDataResult.data.bitcoin[currency];
  }

  clearCache(): void {
    this.bitcoinPriceCache = null;
    this.exchangeRatesCache = null;
    this.emitEvent('CACHE_CLEARED', {});

    if (this.config.enableLogging) {
      exchangeRateLogger.debug('Cache cleared');
    }
  }

  getCacheStatus() {
    return {
      bitcoinPrice: this.bitcoinPriceCache,
      exchangeRates: this.exchangeRatesCache,
    };
  }

  addEventListener<T extends ExchangeRateEvent>(
    event: T,
    listener: ExchangeRateEventListener<T>
  ): string {
    const id = `listener_${++this.listenerIdCounter}`;
    this.eventListeners.set(id, { event, listener });
    return id;
  }

  removeEventListener(listenerId: string): void {
    this.eventListeners.delete(listenerId);
  }

  isHealthy(): boolean {
    return this.isInitialized && this.lastError === null;
  }

  getLastError(): string | null {
    return this.lastError;
  }

  getServiceStats() {
    return { ...this.serviceStats };
  }

  private isCacheValid<T>(cacheEntry: CacheEntry<T>): boolean {
    return Date.now() < cacheEntry.expiresAt;
  }

  private emitEvent<T extends ExchangeRateEvent>(
    event: T,
    payload: ExchangeRateEventPayload[T]
  ): void {
    for (const [, { event: listenerEvent, listener }] of this.eventListeners) {
      if (listenerEvent === event) {
        try {
          listener(event, payload);
        } catch (error) {
          exchangeRateLogger.warn('Event listener error', error);
        }
      }
    }
  }
}

const exchangeRateService = ExchangeRateService.getInstance();
export default exchangeRateService;
