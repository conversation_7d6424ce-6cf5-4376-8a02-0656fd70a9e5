/**
 * Logout Service
 *
 * Comprehensive logout functionality that handles:
 * - Authentication token cleanup
 * - User session state reset
 * - Cached data cleanup
 * - Authentication service state reset
 * - Navigation to appropriate entry screen
 *
 * This service ensures complete app state reset on logout.
 */

import { SecureStorageKeys } from '@const/secure-storage-keys';
import type { useTranslations } from '@hooks';
import useAuthStore from '@store/auth.store';
import useBreezSdkStore from '@store/breez-sdk.store';
import useErrorStore from '@store/error.store';
import useNotificationStore from '@store/notification.store';
import useTokenStore from '@store/token.store';
import { createLogger } from '@utils/logger';
import { clearRefreshToken, clearToken } from '@utils/token';
import { router } from 'expo-router';
import { deleteItemAsync } from 'expo-secure-store';
import { AuthState } from 'types/auth.types';
import { authenticationService } from './authentication.service';

const logoutLogger = createLogger('LogoutService');

/**
 * Logout service interface
 */
export interface LogoutService {
  logout(): Promise<void>;
  clearAuthTokens(): Promise<void>;
  clearUserSession(): Promise<void>;
  clearCachedData(): Promise<void>;
  resetStores(): void;
  clearAuthenticationService(): void;
}

/**
 * Implementation of the logout service
 */
class LogoutServiceImpl implements LogoutService {
  async logout(): Promise<void> {
    try {
      logoutLogger.info('Starting complete logout process');

      // Ensure wallet is deleted prior to clearing auth to avoid auto-reconnect races
      try {
        const breez = useBreezSdkStore.getState();
        await breez.deleteWallet?.();
      } catch (e) {
        logoutLogger.warn('Wallet deletion during logout failed (continuing)', e);
      }

      await this.clearAuthTokens();
      await this.clearUserSession();
      await this.clearCachedData();
      this.resetStores();
      await this.clearAuthenticationService();

      logoutLogger.info('Logout process completed successfully');

      router.replace('/init-screen');
    } catch (error) {
      logoutLogger.error('Error during logout process', error);

      router.replace('/init-screen');
      throw error;
    }
  }

  /**
   * Clear authentication tokens from secure storage
   */
  async clearAuthTokens(): Promise<void> {
    try {
      logoutLogger.info('Clearing authentication tokens');

      await Promise.all([clearToken(), clearRefreshToken()]);

      logoutLogger.info('Authentication tokens cleared successfully');
    } catch (error) {
      logoutLogger.error('Failed to clear authentication tokens', error);
      throw error;
    }
  }

  /**
   * Clear user session data from secure storage
   */
  async clearUserSession(): Promise<void> {
    try {
      logoutLogger.info('Clearing user session data');

      const sessionKeys = [
        SecureStorageKeys.IS_ACCOUNT_CREATED,
        SecureStorageKeys.IS_SAVING_PLAN_CREATED,
        SecureStorageKeys.TERMS_VERSION,
        SecureStorageKeys.VERIFF_SESSION_URL,
        SecureStorageKeys.VERIFF_SESSION_TIMESTAMP,
      ];

      await Promise.all(
        sessionKeys.map((key) =>
          deleteItemAsync(key).catch((error) =>
            logoutLogger.warn(`Failed to clear ${key}`, error)
          )
        )
      );

      logoutLogger.info('User session data cleared successfully');
    } catch (error) {
      logoutLogger.error('Failed to clear user session data', error);
      throw error;
    }
  }

  /**
   * Clear cached application data
   * Note: Wallet data (mnemonic) is intentionally preserved for security
   */
  async clearCachedData(): Promise<void> {
    try {
      logoutLogger.info('Clearing cached application data');

      const cacheKeys = [
        SecureStorageKeys.BREEZ_LAST_SYNC_TIME,
        SecureStorageKeys.BREEZ_PAYMENT_LIMITS,
        SecureStorageKeys.BREEZ_LAST_BACKUP_TIME,

        SecureStorageKeys.AUTH_ATTEMPT_COUNT,
        SecureStorageKeys.LAST_AUTH_TIMESTAMP,
      ];

      await Promise.all(
        cacheKeys.map((key) =>
          deleteItemAsync(key).catch((error) =>
            logoutLogger.warn(`Failed to clear cache ${key}`, error)
          )
        )
      );

      logoutLogger.info('Cached application data cleared successfully');
    } catch (error) {
      logoutLogger.error('Failed to clear cached application data', error);
      throw error;
    }
  }

  /**
   * Reset all Zustand store states to initial values
   */
  resetStores(): void {
    try {
      logoutLogger.info('Resetting store states');

      // Reset auth store
      const authStore = useAuthStore.getState();
      authStore.setAuthState(AuthState.Unknown);
      authStore.setToken(null);

      // Reset token store
      const tokenStore = useTokenStore.getState();
      tokenStore.setToken(undefined);

      // Reset error store
      const errorStore = useErrorStore.getState();
      errorStore.clearAllErrors();

      // Reset notification store
      const notificationStore = useNotificationStore.getState();
      notificationStore.hideModal();

      logoutLogger.info('Store states reset successfully');
    } catch (error) {
      logoutLogger.error('Failed to reset store states', error);
      throw error;
    }
  }

  /**
   * Clear authentication service state
   */
  async clearAuthenticationService(): Promise<void> {
    try {
      logoutLogger.info('Clearing authentication service state');

      await authenticationService.clearAllAuthenticationData();

      logoutLogger.info('Authentication service state cleared successfully');
    } catch (error) {
      logoutLogger.error('Failed to clear authentication service state', error);
      throw error;
    }
  }
}

// Export singleton instance
export const logoutService = new LogoutServiceImpl();

/**
 * Convenience function for quick logout
 */
export const performLogout = async (): Promise<void> => {
  return await logoutService.logout();
};

/**
 * Deprecated: Alert-based logout prompt (kept for reference). Use ConfirmationDialog in UI layers instead.
 */
export const logoutWithConfirmation =
  (_t: ReturnType<typeof useTranslations>) => () => {
    // No-op: replaced by UI-level ConfirmationDialog
  };
