// biome-ignore-all lint: Encryption algorithms require bitwise operations

import { createLogger } from '@utils/logger';
import {
  AUTH_ERROR_MESSAGES,
  BIOMETRIC_ERROR_PATTERNS,
  BIOMETRIC_PROMPTS,
  SecureStorageKeys,
} from 'const';
import {
  authenticateAsync,
  hasHardwareAsync,
  isEnrolledAsync,
  type LocalAuthenticationResult,
} from 'expo-local-authentication';
import {
  deleteItemAsync,
  getItemAsync,
  setItemAsync,
  WHEN_UNLOCKED,
} from 'expo-secure-store';
import {
  type AuthenticationAttempt,
  type AuthenticationConfig,
  AuthenticationMethod,
  AuthenticationResult,
  type AuthenticationService,
  type AuthenticationState,
  type BiometricConfig,
  DEFAULT_AUTH_CONFIG,
  type PinConfig,
  type PinValidationResult,
  WEAK_PIN_PATTERNS,
} from 'types/authentication.types';

const authLogger = createLogger('AuthenticationService');

export class AuthenticationServiceImpl implements AuthenticationService {
  private authState: AuthenticationState = {
    isAuthenticated: false,
    isAuthenticating: false,
    failedAttempts: 0,
    isLockedOut: false,
  };

  private config: AuthenticationConfig | null = null;

  async initialize(): Promise<void> {
    try {
      authLogger.info('Initializing authentication service');
      this.config = await this.loadConfig();
      const isLockedOut = await this.isLockedOut();
      this.authState.isLockedOut = isLockedOut;
      authLogger.info('Authentication service initialized successfully');
    } catch (error) {
      authLogger.error('Failed to initialize authentication service', error);
      throw error;
    }
  }

  async getConfig(): Promise<AuthenticationConfig> {
    if (!this.config) {
      this.config = await this.loadConfig();
    }
    return this.config;
  }

  private async loadConfig(): Promise<AuthenticationConfig> {
    try {
      const biometric = await this.loadBiometricConfig();
      const pin = await this.loadPinConfig();
      const password = DEFAULT_AUTH_CONFIG.password!;

      return {
        biometric,
        pin,
        password,
        maxTotalAttempts: DEFAULT_AUTH_CONFIG.maxTotalAttempts!,
        extendedLockoutDuration: DEFAULT_AUTH_CONFIG.extendedLockoutDuration!,
      };
    } catch (error) {
      authLogger.error('Failed to load authentication config', error);
      throw error;
    }
  }

  private async loadBiometricConfig(): Promise<BiometricConfig> {
    try {
      const [hasHardware, isEnrolled, isEnabledStr] = await Promise.all([
        hasHardwareAsync(),
        isEnrolledAsync(),
        getItemAsync(SecureStorageKeys.BIOMETRIC_ENABLED),
      ]);

      const isEnabled = isEnabledStr === 'true';
      const isAvailable = hasHardware && isEnrolled && isEnabled;

      return {
        isEnabled,
        hasHardware,
        isEnrolled,
        isAvailable,
        biometricType: hasHardware ? 'faceId' : undefined, // TODO: Fingerprint, etc.
      };
    } catch (error) {
      authLogger.error('Failed to load biometric config', error);
      return {
        isEnabled: false,
        hasHardware: false,
        isEnrolled: false,
        isAvailable: false,
      };
    }
  }

  private async loadPinConfig(): Promise<PinConfig> {
    try {
      const [pinValue, isEnabledStr] = await Promise.all([
        getItemAsync(SecureStorageKeys.PIN_VALUE),
        getItemAsync(SecureStorageKeys.PIN_ENABLED),
      ]);

      const isEnabled = isEnabledStr === 'true';
      const isConfigured = !!pinValue;

      return {
        isEnabled,
        isConfigured,
        minLength: DEFAULT_AUTH_CONFIG.pin!.minLength,
        maxLength: DEFAULT_AUTH_CONFIG.pin!.maxLength,
        maxAttempts: DEFAULT_AUTH_CONFIG.pin!.maxAttempts,
        lockoutDuration: DEFAULT_AUTH_CONFIG.pin!.lockoutDuration,
      };
    } catch (error) {
      authLogger.error('Failed to load PIN config', error);
      return DEFAULT_AUTH_CONFIG.pin!;
    }
  }

  async isBiometricAvailable(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      return config.biometric.isAvailable;
    } catch (error) {
      authLogger.error('Failed to check biometric availability', error);
      return false;
    }
  }

  async authenticateWithBiometric(): Promise<AuthenticationResult> {
    try {
      authLogger.info('Attempting biometric authentication');

      const config = await this.getConfig();
      if (!config.biometric.isAvailable) {
        return AuthenticationResult.NOT_AVAILABLE;
      }

      this.authState.isAuthenticating = true;
      this.authState.currentMethod = AuthenticationMethod.BIOMETRIC;

      const result: LocalAuthenticationResult = await authenticateAsync({
        promptMessage: BIOMETRIC_PROMPTS.VIEW_SEED_PHRASE,
        cancelLabel: BIOMETRIC_PROMPTS.CANCEL_LABEL,
        disableDeviceFallback: true,
      });

      if (result.success) {
        await this.handleSuccessfulAuthentication(
          AuthenticationMethod.BIOMETRIC
        );
        return AuthenticationResult.SUCCESS;
      }
      await this.handleFailedAuthentication(
        AuthenticationMethod.BIOMETRIC,
        AUTH_ERROR_MESSAGES.BIOMETRIC_FAILED
      );
      const isCancelled =
        result.error &&
        (String(result.error).includes(BIOMETRIC_ERROR_PATTERNS.USER_CANCEL) ||
          String(result.error).includes(BIOMETRIC_ERROR_PATTERNS.USER_CANCEL_LOWERCASE) ||
          String(result.error).includes(BIOMETRIC_ERROR_PATTERNS.CANCELLED));

      return isCancelled
        ? AuthenticationResult.CANCELLED
        : AuthenticationResult.FAILED;
    } catch (error) {
      authLogger.error('Biometric authentication error', error);
      await this.handleFailedAuthentication(
        AuthenticationMethod.BIOMETRIC,
        String(error)
      );
      return AuthenticationResult.FAILED;
    } finally {
      this.authState.isAuthenticating = false;
      this.authState.currentMethod = undefined;
    }
  }

  async isPinConfigured(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      return config.pin.isConfigured;
    } catch (error) {
      authLogger.error('Failed to check PIN configuration', error);
      return false;
    }
  }

  async setupPin(pin: string): Promise<boolean> {
    try {
      authLogger.info('Setting up new PIN');

      const validation = this.validatePin(pin);
      if (!validation.isValid) {
        authLogger.warn('PIN validation failed', validation.errors);
        return false;
      }

      const encryptedPin = this.encryptPin(pin);
      await setItemAsync(SecureStorageKeys.PIN_VALUE, encryptedPin, {
        keychainAccessible: WHEN_UNLOCKED,
      });

      await setItemAsync(SecureStorageKeys.PIN_ENABLED, 'true', {
        keychainAccessible: WHEN_UNLOCKED,
      });

      this.config = await this.loadConfig();

      authLogger.info('PIN setup completed successfully');
      return true;
    } catch (error) {
      authLogger.error('Failed to setup PIN', error);
      return false;
    }
  }

  async verifyPin(pin: string): Promise<AuthenticationResult> {
    try {
      authLogger.info('Verifying PIN');

      const config = await this.getConfig();
      if (!(config.pin.isConfigured && config.pin.isEnabled)) {
        return AuthenticationResult.NOT_AVAILABLE;
      }

      this.authState.isAuthenticating = true;
      this.authState.currentMethod = AuthenticationMethod.PIN;

      const storedEncryptedPin = await getItemAsync(
        SecureStorageKeys.PIN_VALUE
      );
      if (!storedEncryptedPin) {
        return AuthenticationResult.NOT_AVAILABLE;
      }

      const isValid = this.verifyEncryptedPin(pin, storedEncryptedPin);

      if (isValid) {
        await this.handleSuccessfulAuthentication(AuthenticationMethod.PIN);
        return AuthenticationResult.SUCCESS;
      }
      await this.handleFailedAuthentication(
        AuthenticationMethod.PIN,
        'Invalid PIN'
      );
      return AuthenticationResult.FAILED;
    } catch (error) {
      authLogger.error('PIN verification error', error);
      await this.handleFailedAuthentication(
        AuthenticationMethod.PIN,
        String(error)
      );
      return AuthenticationResult.FAILED;
    } finally {
      this.authState.isAuthenticating = false;
      this.authState.currentMethod = undefined;
    }
  }

  async verifyPassword(password: string): Promise<AuthenticationResult> {
    try {
      authLogger.info('Verifying password');

      this.authState.isAuthenticating = true;
      this.authState.currentMethod = AuthenticationMethod.PASSWORD;

      const isValid = password === 'Test1234';

      if (isValid) {
        await this.handleSuccessfulAuthentication(
          AuthenticationMethod.PASSWORD
        );
        return AuthenticationResult.SUCCESS;
      }
      await this.handleFailedAuthentication(
        AuthenticationMethod.PASSWORD,
        'Invalid password'
      );
      return AuthenticationResult.FAILED;
    } catch (error) {
      authLogger.error('Password verification error', error);
      await this.handleFailedAuthentication(
        AuthenticationMethod.PASSWORD,
        String(error)
      );
      return AuthenticationResult.FAILED;
    } finally {
      this.authState.isAuthenticating = false;
      this.authState.currentMethod = undefined;
    }
  }

  async setBiometricEnabled(enabled: boolean): Promise<void> {
    try {
      await setItemAsync(
        SecureStorageKeys.BIOMETRIC_ENABLED,
        enabled.toString(),
        {
          keychainAccessible: WHEN_UNLOCKED,
        }
      );

      this.config = await this.loadConfig();

      authLogger.info(
        `Biometric authentication ${enabled ? 'enabled' : 'disabled'}`
      );
    } catch (error) {
      authLogger.error('Failed to set biometric enabled state', error);
      throw error;
    }
  }

  async setPinEnabled(enabled: boolean): Promise<void> {
    try {
      await setItemAsync(SecureStorageKeys.PIN_ENABLED, enabled.toString(), {
        keychainAccessible: WHEN_UNLOCKED,
      });

      this.config = await this.loadConfig();

      authLogger.info(`PIN authentication ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      authLogger.error('Failed to set PIN enabled state', error);
      throw error;
    }
  }

  getAuthenticationState(): AuthenticationState {
    return { ...this.authState };
  }

  clearAuthentication(): void {
    this.authState = {
      isAuthenticated: false,
      isAuthenticating: false,
      failedAttempts: 0,
      isLockedOut: false,
    };
    authLogger.info('Authentication state cleared');
  }

  async clearAllAuthenticationData(): Promise<void> {
    try {
      authLogger.info('Clearing all authentication data');

      this.clearAuthentication();

      await Promise.all([
        deleteItemAsync(SecureStorageKeys.PIN_VALUE),
        deleteItemAsync(SecureStorageKeys.PIN_ENABLED),
        deleteItemAsync(SecureStorageKeys.BIOMETRIC_ENABLED),
        deleteItemAsync(SecureStorageKeys.AUTH_ATTEMPT_COUNT),
        deleteItemAsync(SecureStorageKeys.LAST_AUTH_TIMESTAMP),
      ]);

      this.config = null;

      authLogger.info('All authentication data cleared successfully');
    } catch (error) {
      authLogger.error('Failed to clear all authentication data', error);
      throw error;
    }
  }

  async recordAttempt(attempt: AuthenticationAttempt): Promise<void> {
    try {
      authLogger.info('Authentication attempt recorded', {
        method: attempt.method,
        result: attempt.result,
        timestamp: attempt.timestamp,
      });
    } catch (error) {
      authLogger.error('Failed to record authentication attempt', error);
    }
  }

  async isLockedOut(): Promise<boolean> {
    try {
      const attemptCountStr = await getItemAsync(
        SecureStorageKeys.AUTH_ATTEMPT_COUNT
      );
      const lastAttemptStr = await getItemAsync(
        SecureStorageKeys.LAST_AUTH_TIMESTAMP
      );

      if (!(attemptCountStr && lastAttemptStr)) {
        return false;
      }

      const attemptCount = Number.parseInt(attemptCountStr, 10);
      const lastAttempt = Number.parseInt(lastAttemptStr, 10);
      const config = await this.getConfig();

      if (attemptCount >= config.maxTotalAttempts) {
        const timeSinceLastAttempt = Date.now() - lastAttempt;
        return timeSinceLastAttempt < config.extendedLockoutDuration;
      }

      return false;
    } catch (error) {
      authLogger.error('Failed to check lockout status', error);
      return false;
    }
  }

  async getRemainingLockoutTime(): Promise<number> {
    try {
      const isLockedOut = await this.isLockedOut();
      if (!isLockedOut) {
        return 0;
      }

      const lastAttemptStr = await getItemAsync(
        SecureStorageKeys.LAST_AUTH_TIMESTAMP
      );
      if (!lastAttemptStr) {
        return 0;
      }

      const lastAttempt = Number.parseInt(lastAttemptStr, 10);
      const config = await this.getConfig();
      const timeSinceLastAttempt = Date.now() - lastAttempt;

      return Math.max(0, config.extendedLockoutDuration - timeSinceLastAttempt);
    } catch (error) {
      authLogger.error('Failed to get remaining lockout time', error);
      return 0;
    }
  }

  async resetLockoutState(): Promise<void> {
    try {
      authLogger.info('Resetting authentication lockout state (Development)');

      await Promise.all([
        deleteItemAsync(SecureStorageKeys.AUTH_ATTEMPT_COUNT),
        deleteItemAsync(SecureStorageKeys.LAST_AUTH_TIMESTAMP),
      ]);

      this.authState.failedAttempts = 0;
      this.authState.isLockedOut = false;
      this.authState.isAuthenticated = false;
      this.authState.isAuthenticating = false;

      authLogger.info('Authentication lockout state reset successfully');
    } catch (error) {
      authLogger.error('Failed to reset authentication lockout state', error);
      throw error;
    }
  }

  private async handleSuccessfulAuthentication(
    method: AuthenticationMethod
  ): Promise<void> {
    this.authState.isAuthenticated = true;
    this.authState.authenticatedMethod = method;
    this.authState.authenticatedAt = Date.now();
    this.authState.failedAttempts = 0;
    this.authState.isLockedOut = false;

    await deleteItemAsync(SecureStorageKeys.AUTH_ATTEMPT_COUNT);
    await deleteItemAsync(SecureStorageKeys.LAST_AUTH_TIMESTAMP);

    authLogger.info(`Successful authentication with ${method}`);
  }

  private async handleFailedAuthentication(
    method: AuthenticationMethod,
    error: string
  ): Promise<void> {
    this.authState.failedAttempts += 1;

    const currentCountStr = await getItemAsync(
      SecureStorageKeys.AUTH_ATTEMPT_COUNT
    );
    const currentCount = currentCountStr
      ? Number.parseInt(currentCountStr, 10)
      : 0;
    const newCount = currentCount + 1;

    await setItemAsync(
      SecureStorageKeys.AUTH_ATTEMPT_COUNT,
      newCount.toString()
    );
    await setItemAsync(
      SecureStorageKeys.LAST_AUTH_TIMESTAMP,
      Date.now().toString()
    );

    const config = await this.getConfig();
    if (newCount >= config.maxTotalAttempts) {
      this.authState.isLockedOut = true;
    }

    authLogger.warn(`Failed authentication with ${method}`, {
      error,
      attemptCount: newCount,
    });
  }

  private validatePin(pin: string): PinValidationResult {
    const errors: string[] = [];

    const hasValidLength = pin.length === 6;
    if (!hasValidLength) {
      errors.push('PIN must be exactly 6 digits');
    }

    const hasOnlyDigits = /^\d+$/.test(pin);
    if (!hasOnlyDigits) {
      errors.push('PIN must contain only digits');
    }

    const isNotSimplePattern = !WEAK_PIN_PATTERNS.includes(pin);
    if (!isNotSimplePattern) {
      errors.push('PIN is too simple, please choose a different combination');
    }

    return {
      isValid: errors.length === 0,
      errors,
      hasValidLength,
      hasOnlyDigits,
      isNotSimplePattern,
    };
  }

  private encryptPin(pin: string): string {
    try {
      const key = 'RETI2024PIN';
      const substituted = this.substituteCharacters(pin, key, true);
      const reversed = substituted.split('').reverse().join('');

      const salt = (Date.now() % 1000).toString().padStart(3, '0');
      return `${reversed}.${salt}`;
    } catch (error) {
      authLogger.error('Failed to encrypt PIN', error);
      throw new Error('PIN encryption failed');
    }
  }

  private verifyEncryptedPin(pin: string, encryptedPin: string): boolean {
    try {
      const key = 'RETI2024PIN';

      const parts = encryptedPin.split('.');
      if (parts.length !== 2) {
        authLogger.warn('Invalid encrypted PIN format');
        return false;
      }

      const [reversed] = parts;

      const substituted = reversed.split('').reverse().join('');
      const decrypted = this.substituteCharacters(substituted, key, false);

      return decrypted === pin;
    } catch (error) {
      authLogger.error('Failed to verify encrypted PIN', error);
      return false;
    }
  }

  private substituteCharacters(
    text: string,
    key: string,
    encrypt: boolean
  ): string {
    const digits = '0123456789';
    const substitution = '9876543210';

    let result = '';
    for (let i = 0; i < text.length; i++) {
      const char = text.charAt(i);
      const digitIndex = digits.indexOf(char);

      if (digitIndex !== -1) {
        const keyOffset = key.charCodeAt(i % key.length) % 10;
        let newIndex: number;

        if (encrypt) {
          newIndex = (digitIndex + keyOffset) % 10;
          result += substitution.charAt(newIndex);
        } else {
          const substitutionIndex = substitution.indexOf(char);
          newIndex = (substitutionIndex - keyOffset + 10) % 10;
          result += digits.charAt(newIndex);
        }
      } else {
        result += char;
      }
    }

    return result;
  }
}

export const authenticationService = new AuthenticationServiceImpl();
