export const SecureStorageKeys = {
  AUTH_TOKEN: "AUTH_TOKEN",
  REFRESH_TOKEN: "REFRESH_TOKEN",
  IS_ACCOUNT_CREATED: "IS_ACCOUNT_CREATED",
  IS_SAVING_PLAN_CREATED: "IS_SAVING_PLAN_CREATED",
  TERMS_VERSION: "TERMS_VERSION",
  MNEMONIC: "MNEMONIC",
  VERIFF_SESSION_URL: "VERIFF_SESSION_URL",
  VERIFF_SESSION_TIMESTAMP: "VERIFF_SESSION_TIMESTAMP",
  // Authentication Security Keys
  PIN_VALUE: "PIN_VALUE", // Encrypted PIN value
  PIN_ENABLED: "PIN_ENABLED", // Boolean flag for PIN enabled/disabled
  BIOMETRIC_ENABLED: "BIOMETRIC_ENABLED", // Boolean flag for biometric preference
  AUTH_ATTEMPT_COUNT: "AUTH_ATTEMPT_COUNT",
  LAST_AUTH_TIMESTAMP: "LAST_AUTH_TIMESTAMP",
  // Breez SDK Liquid Network Keys
  BREEZ_WALLET_INFO: "BREEZ_WALLET_INFO",
  BREEZ_LAST_SYNC_TIME: "BREEZ_LAST_SYNC_TIME",
  BREEZ_PAYMENT_LIMITS: "BREEZ_PAYMENT_LIMITS",
  BREEZ_SERVICE_CONFIG: "BREEZ_SERVICE_CONFIG",
  BREEZ_LAST_BACKUP_TIME: "BREEZ_LAST_BACKUP_TIME",
  BREEZ_CONNECTION_INFO: "BREEZ_CONNECTION_INFO",
  BREEZ_WALLET_ADDRESS: "BREEZ_WALLET_ADDRESS",
} as const;

export type SecureStorageKeys =
  (typeof SecureStorageKeys)[keyof typeof SecureStorageKeys];
