export const BIOMETRIC_ERROR_PATTERNS = {
  USER_CANCEL: 'UserCancel' as const,
  USER_CANCEL_LOWERCASE: 'user_cancel' as const,
  CANCELLED: 'cancelled' as const,
  AUTHENTICATION_FAILED: 'AuthenticationFailed' as const,
} as const;

export const AUTH_ERROR_MESSAGES = {
  BIOMETRIC_FAILED: 'Biometric authentication failed' as const,
  PIN_VERIFICATION_FAILED:
    'PIN verification failed. Please try again.' as const,
  PASSWORD_VERIFICATION_FAILED:
    'Password verification failed. Please try again.' as const,
  AUTHENTICATION_FAILED: 'Authentication failed' as const,
  INITIALIZATION_FAILED: 'Failed to initialize authentication' as const,
  CONFIG_LOAD_FAILED: 'Failed to load authentication configuration' as const,
  PIN_SETUP_FAILED: 'Failed to setup PIN' as const,
  BIOMETR<PERSON>_SETUP_FAILED: 'Failed to setup biometric authentication' as const,
} as const;

export const BIOMETRIC_PROMPTS = {
  VIEW_SEED_PHRASE: 'Authenticate to view your seed phrase' as const,
  SECURE_ACTION: 'Authenticate to continue' as const,
  CANCEL_LABEL: 'Cancel' as const,
} as const;

export const AUTH_LOADING_MESSAGES = {
  GENERATING_ADDRESS: 'Generating address...' as const,
  VERIFYING_PIN: 'Verifying PIN...' as const,
  VERIFYING_PASSWORD: 'Verifying password...' as const,
  AUTHENTICATING: 'Authenticating...' as const,
} as const;

export const AUTH_TIMEOUTS = {
  /** Modal close delay to prevent unwanted navigation */
  MODAL_CLOSE_DELAY: 100,
  /** Biometric authentication timeout */
  BIOMETRIC_TIMEOUT: 30_000,
  /** PIN verification timeout */
  PIN_TIMEOUT: 10_000,
  /** Password verification timeout */
  PASSWORD_TIMEOUT: 10_000,
} as const;

export const AUTH_LIMITS = {
  /** Maximum attempts per authentication method */
  MAX_ATTEMPTS_PER_METHOD: 2,
  /** PIN minimum length */
  PIN_MIN_LENGTH: 6,
  /** PIN maximum length */
  PIN_MAX_LENGTH: 6,
  /** Password minimum length */
  PASSWORD_MIN_LENGTH: 8,
  /** Maximum total authentication attempts */
  MAX_TOTAL_ATTEMPTS: 5,
} as const;

export const BOOLEAN_STRINGS = {
  TRUE: 'true' as const,
  FALSE: 'false' as const,
} as const;

export const AUTH_METHOD_STRINGS = {
  BIOMETRIC: 'biometric' as const,
  PIN: 'pin' as const,
  PASSWORD: 'password' as const,
} as const;

export const AUTH_RESULT_STRINGS = {
  SUCCESS: 'success' as const,
  FAILED: 'failed' as const,
  CANCELLED: 'cancelled' as const,
  NOT_AVAILABLE: 'not_available' as const,
  NOT_ENROLLED: 'not_enrolled' as const,
  LOCKED_OUT: 'locked_out' as const,
} as const;

export const MODAL_STATES = {
  VISIBLE: true as const,
  HIDDEN: false as const,
} as const;

export const MODAL_SNAP_POINTS = {
  DEFAULT: ['50%', '75%'] as const,
  WITH_KEYBOARD: ['60%', '85%'] as const,
} as const;

export const PIN_VALIDATION = {
  DIGIT_PATTERN: /^\d{6}$/,
  ONLY_DIGITS: /^\d+$/,
} as const;

export const PASSWORD_VALIDATION = {
  MIN_LENGTH: 8,
  SPECIAL_CHARS: /[!@#$%^&*(),.?":{}|<>]/,
  NUMBERS: /\d/,
  UPPERCASE: /[A-Z]/,
} as const;

export const ENCRYPTION_CONSTANTS = {
  /** AES encryption algorithm */
  ALGORITHM: 'AES' as const,
  /** Key size in bits */
  KEY_SIZE: 256 as const,
  /** IV size in bytes */
  IV_SIZE: 16 as const,
  /** Salt size in bytes */
  SALT_SIZE: 32 as const,
  /** PBKDF2 iterations */
  PBKDF2_ITERATIONS: 10_000 as const,
} as const;

export const DEV_CONSTANTS = {
  /** Development biometric toggle label */
  BIOMETRIC_TOGGLE_LABEL: 'Enable Biometric Authentication (Dev Only)' as const,
  /** Development biometric toggle description */
  BIOMETRIC_TOGGLE_DESC:
    'Toggle biometric auth for testing three-tier authentication' as const,
  /** Development alert titles */
  ALERT_TITLE: 'Biometric Authentication' as const,
  /** Development error alert title */
  ERROR_ALERT_TITLE: 'Error' as const,
  /** Development reset lockout button label */
  RESET_LOCKOUT_LABEL: 'Reset Auth Lockout (Dev Only)' as const,
  /** Development reset lockout confirmation title */
  RESET_LOCKOUT_TITLE: 'Reset Authentication Lockout' as const,
  /** Development reset lockout confirmation message */
  RESET_LOCKOUT_MESSAGE:
    'This will clear all failed authentication attempts and re-enable authentication methods. Continue?' as const,
  /** Development reset lockout success message */
  RESET_LOCKOUT_SUCCESS:
    'Authentication lockout has been reset. All authentication methods are now available.' as const,
  /** Development reset lockout error message */
  RESET_LOCKOUT_ERROR:
    'Failed to reset authentication lockout. Check console for details.' as const,
} as const;

export type BiometricErrorPattern =
  (typeof BIOMETRIC_ERROR_PATTERNS)[keyof typeof BIOMETRIC_ERROR_PATTERNS];
export type AuthErrorMessage =
  (typeof AUTH_ERROR_MESSAGES)[keyof typeof AUTH_ERROR_MESSAGES];
export type AuthMethodString =
  (typeof AUTH_METHOD_STRINGS)[keyof typeof AUTH_METHOD_STRINGS];
export type AuthResultString =
  (typeof AUTH_RESULT_STRINGS)[keyof typeof AUTH_RESULT_STRINGS];
export type BooleanString =
  (typeof BOOLEAN_STRINGS)[keyof typeof BOOLEAN_STRINGS];
