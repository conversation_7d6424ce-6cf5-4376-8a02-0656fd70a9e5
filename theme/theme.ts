import { DefaultTheme } from 'react-native-paper';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#1FAD8C',
    onPrimary: '#FFFFFF',
    error: '#E96160',
    warning: '#F38600',
    info: '#0094FE',
    success: '#24D327',
    cyprus: '#0D3D32',
    eclipse: '#3A3A3A',
    magicMint: '#A8F0DF',
    mineralGreen: '#586A63',
    mintGreen: '#F2FDFA',
    pastelAqua: '#D4F7EF',
    sageGreen: '#A6B5AF',
    shamrock: '#0F5746',
    gray: '#6F867D',
    lilyWhite: '#F9FAFA',
    black: '#000000',
    background: '#FFFFFF',
    text: '#3F3F3F',
    inputDefaultBorderColor: '#DEE3E1',
    lightGray: '#C2CCC8',
  },
  headerLeft: {
    background: '#DEE3E1',
  },
};

export const AlertStyles = {
  error: {
    bgColor: 'rgba(222, 31, 35, 0.10)',
    borderColor: theme.colors.error,
    textColor: theme.colors.error,
    icon: 'alert-outline',
  },
  warning: {
    bgColor: 'rgba(243, 134, 0, 0.10)',
    borderColor: theme.colors.warning,
    textColor: theme.colors.warning,
    icon: 'alert-circle-outline',
  },
  info: {
    bgColor: 'rgba(0, 148, 254, 0.10)',
    borderColor: theme.colors.info,
    textColor: theme.colors.info,
    icon: 'information-outline',
  },
  success: {
    bgColor: 'rgba(36, 211, 39, 0.1)',
    borderColor: theme.colors.success,
    textColor: theme.colors.success,
    icon: 'check-circle-outline',
  },
};

export default theme;
