import CheckRectangleIcon from '@assets/check-rectangle.svg';
import LockIcon from '@assets/lock.svg';
import { useTranslations } from '@hooks';
import useVeriffStore, { VerificationStatus } from '@store/veriff.store';
import { Badge, Box, PageLayout, Text, Title } from '@ui';
import VeriffSdk from '@veriff/react-native-sdk';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Dimensions, StatusBar, View } from 'react-native';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

export default function VerificationEntryScreen() {
  const router = useRouter();
  const { theme } = useUnistyles();
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const { verificationStatus, startVerification, launchVeriffSdkFlow } =
    useVeriffStore();

  useEffect(() => {
    if (verificationStatus === VerificationStatus.COMPLETED) {
      router.replace('/(navigation)/user-details');
    }
  }, [verificationStatus, router]);

  // Don't allow starting verification if already loading or pending
  const canStartVerification =
    verificationStatus !== VerificationStatus.LOADING &&
    verificationStatus !== VerificationStatus.PENDING;

  const handleStartVerification = async () => {
    if (!canStartVerification) {
      return;
    }

    setIsLoading(true);
    try {
      await startVerification();
      const result = await launchVeriffSdkFlow();

      if (result.status === VeriffSdk.statusDone) {
        router.replace('/(navigation)/user-details');
      }
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: debug
      console.error('Failed to start verification:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const requirements = [
    'kyc.requirements.document',
    'kyc.requirements.face',
    'kyc.requirements.lighting',
    'kyc.requirements.quality',
  ];

  return (
    <PageLayout
      bgColor="grey.900"
      buttonBlurOptions={{
        tint: 'dark',
      }}
      buttonGradientVariant="dark"
      buttonProps={{
        loading: isLoading,
      }}
      buttonText={t('kyc.buttons.start')}
      buttonTopLabel={
        <Box align="center" flexDirection="row" gap={2} justify="center" mb={1}>
          <LockIcon height={14} width={14} />
          <Text color="grey.300" size="xxs">
            {t('kyc.securityNote')}
          </Text>
        </Box>
      }
      contentGap={2}
      headerBlurOptions={{
        intensity: 25,
        tint: 'systemUltraThinMaterialDark',
      }}
      onButtonPress={handleStartVerification}
      rightComponent={
        <Badge size="lg" variant="primary">
          {t('kyc.progress.introduction')}
        </Badge>
      }
      rightComponentContainerProps={{
        width: 'auto',
        marginRight: 16,
      }}
      withBackgroundGradient
    >
      <StatusBar
        backgroundColor={theme.colors.grey[900]}
        barStyle="light-content"
      />

      <Box align="center" mb={4}>
        <Image
          contentFit="contain"
          source={require('@assets/id-verification-icon.png')}
          style={styles.verificationImage}
        />
      </Box>

      <Box gap={2}>
        <Title color="white">{t('kyc.title')}</Title>
        <Text color="grey.100">{t('kyc.subtitle')}</Text>

        <Box gap={4} mt={2} pr={4}>
          <Text color="grey.100" weight="semibold">
            {t('kyc.requirements.title')}
          </Text>
          {requirements.map((requirement) => (
            <Box flexDirection="row" gap={2} key={requirement}>
              <CheckRectangleIcon
                height={20}
                style={styles.checkIcon}
                width={20}
              />
              <Text color="grey.100">{t(requirement)}</Text>
            </Box>
          ))}
        </Box>

        <View style={styles.bottomPadding} />
      </Box>
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 47,
    paddingBottom: 16,
    height: 87,
    zIndex: 10,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 40,
  },
  verificationImage: {
    width: Math.min(329, screenWidth - 64),
    height: Math.min(329, screenWidth - 64),
  },
  textContainer: {
    paddingHorizontal: 0,
  },
  title: {
    fontSize: 24,
    fontWeight: '500',
    color: theme.colors.white,
    lineHeight: 28.8,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.grey[100],
    lineHeight: 24,
    marginBottom: 32,
  },
  requirementsContainer: {
    marginBottom: 32,
  },
  requirementsTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: theme.colors.white,
    lineHeight: 21.6,
    marginBottom: 16,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    paddingRight: 8,
  },
  checkIcon: {
    width: 20,
    height: 20,
    marginRight: 12,
    marginTop: 2,
    tintColor: theme.colors.primary[600],
  },
  requirementText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    color: theme.colors.grey[100],
    lineHeight: 24,
  },
  securityNote: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  securityText: {
    fontSize: 14,
    fontWeight: '400',
    color: theme.colors.grey[100],
    lineHeight: 20,
    marginLeft: 8,
  },
  bottomPadding: {
    height: 180,
  },
  bottomBlurContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 180,
    zIndex: 5,
  },
  fixedButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingBottom: 32,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
}));
