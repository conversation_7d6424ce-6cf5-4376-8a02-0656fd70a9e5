import { Stack } from 'expo-router';

const VerificationLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        title: '',
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="verification-entry"
        options={{
          title: 'ID Verification',
          headerShown: false,
        }}
      />
    </Stack>
  );
};

export default VerificationLayout;
