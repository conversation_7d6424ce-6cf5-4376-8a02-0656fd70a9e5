import { Stack } from 'expo-router';

const AuthenticatedLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="savings-detail"
        options={{
          title: '',
          gestureEnabled: true,
        }}
      />
    </Stack>
  );
};

export default AuthenticatedLayout;
