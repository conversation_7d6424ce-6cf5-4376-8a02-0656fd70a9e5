import { getQueryData } from '@api';
import {
  BitcoinCard,
  DashboardBitcoinCardSkeleton,
  DashboardSavingsChartSkeleton,
  DashboardTransactionsSkeleton,
  type QuickAction,
  QuickActionsSlider,
  SavingsChart,
  TransactionsSection,
} from '@components';
import {
  getSavingPlanSettings,
  type SavingPlanProps,
} from '@endpoints/saving-plan.endpoints';
import {
  getProfileInfo,
  getUserInfo,
  type ProfileInfoResponse,
} from '@endpoints/user.endpoints';
import useBreezSdkStore from '@store/breez-sdk.store';
import useVeriffStore, { VerificationStatus } from '@store/veriff.store';
import { useQuery } from '@tanstack/react-query';
import { Box, PageLayout, Text, Title } from '@ui';
import toTitleCase from '@utils/capitalize';
import { TIMING_CONSTANTS } from '@utils/wallet/constants';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';
import RetiLogo from 'assets/logo-reti-green.svg';
import { useRouter } from 'expo-router';
import { useBackgroundSync } from 'hooks/use-background-sync';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { useTranslations } from 'hooks/use-translations';
import { useWalletRefresh } from 'hooks/use-wallet-refresh';
import { useCallback, useEffect, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';

const MOCK_DATA = {
  totalBalance: 345_657.12,
  portfolio: 0.9832,
  savingsValue: 345_657.12,
  depositedAmount: 250_000.0,
  bitcoinValue: 366_181.12,
  bitcoinChangePercent: 10.2,
  bitcoinChangeTimeframe: '1t',
  quickActions: [
    {
      id: 'configuration',
      type: 'configuration' as const,
      titleKey: 'dashboard.configurationBanner.text',
      buttonKey: 'dashboard.configurationBanner.button',
      highlightWordKey: 'dashboard.configurationBanner.highlightWord',
      image: require('@assets/configuration-illustration.png'),
      route: '(saving-plan)' as const,
    },
    {
      id: 'verification',
      type: 'verification' as const,
      titleKey: 'dashboard.verificationBanner.text',
      buttonKey: 'dashboard.verificationBanner.button',
      highlightWordKey: 'dashboard.verificationBanner.highlightWord',
      image: require('@assets/security-image.png'),
      route: '(id-verification)/verification-entry' as const,
    },
  ] as QuickAction[],
  savingsChartData: [
    { value: 0, hideDataPoint: true },
    { value: 2000, hideDataPoint: true },
    { value: 3000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 5000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 11_000, hideDataPoint: true },
    { value: 15_500, hideDataPoint: true },
    { value: 17_000 },
  ],
  depositedChartData: [
    { value: 0, hideDataPoint: true },
    { value: 1000, hideDataPoint: true },
    { value: 2000, hideDataPoint: true },
    { value: 3000, hideDataPoint: true },
    { value: 4000, hideDataPoint: true },
    { value: 5000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 8000, hideDataPoint: true },
    { value: 9000 },
  ],
};

export default function Index() {
  const router = useRouter();
  const t = useTranslations();

  const { data: userInfo } = useQuery(
    getQueryData<{ username: string }>(getUserInfo)
  );
  const { data: profileInfo } = useQuery(
    getQueryData<ProfileInfoResponse>(getProfileInfo)
  );
  const { data: existingPlan, isSuccess: isPlanLoaded } = useQuery(
    getQueryData<SavingPlanProps>(getSavingPlanSettings, {
      queryOptions: { retry: false, staleTime: 60_000 },
    })
  );
  const hasSavingPlan = useMemo(
    () => Boolean(isPlanLoaded && existingPlan && existingPlan.name),
    [existingPlan, isPlanLoaded]
  );

  const { state: walletState } = useSimpleWallet();
  const {
    walletInfo,
    isConnected: isBreezConnected,
    isLoading: isBreezLoading,
    // recentPayments,
  } = useBreezSdkStore();
  const {
    priceData,
    convertSatoshis,
    isLoading: isExchangeRateLoading,
  } = useExchangeRate();

  const { handleRefresh } = useWalletRefresh();

  const backgroundSyncOptions = useMemo(
    () => ({
      syncInterval: TIMING_CONSTANTS.SYNC_INTERVAL,
      syncOnAppActive: true,
      enablePeriodicSync: true,
    }),
    []
  );

  useBackgroundSync(backgroundSyncOptions);

  const isWalletDataLoading = useMemo(() => {
    const hasWallet = walletState.hasWallet && walletState.wallet;
    const hasBreezConnection = isBreezConnected && walletInfo;

    return hasWallet && (!hasBreezConnection || isExchangeRateLoading);
  }, [
    walletState.hasWallet,
    walletState.wallet,
    isBreezConnected,
    walletInfo,
    isExchangeRateLoading,
  ]);

  const isTransactionsLoading = useMemo(() => {
    const hasWallet = Boolean(walletState.hasWallet && walletState.wallet);
    const hasBreezConnection = Boolean(isBreezConnected && walletInfo);

    return hasWallet && hasBreezConnection ? isBreezLoading : false;
  }, [
    walletState.hasWallet,
    walletState.wallet,
    isBreezConnected,
    walletInfo,
    isBreezLoading,
  ]);

  const walletData = useMemo(() => {
    const hasWallet = walletState.hasWallet && walletState.wallet;
    const hasBreezConnection = isBreezConnected && walletInfo;

    if (hasWallet && hasBreezConnection) {
      const balanceSat = walletInfo.balanceSat || 0;
      const balanceBtc = balanceSat / 100_000_000;
      const balancePln = convertSatoshis(balanceSat, 'PLN');

      return {
        ...MOCK_DATA,
        portfolio: balanceBtc,
        bitcoinValue: priceData?.bitcoin.PLN || MOCK_DATA.bitcoinValue,
        totalBalance: balancePln,
      };
    }

    return {
      ...MOCK_DATA,
      portfolio: 0,
      totalBalance: 0,
    };
  }, [walletState, isBreezConnected, walletInfo, convertSatoshis, priceData]);

  // const transactions = useMemo((): Transaction[] => {
  //   const hasWallet = walletState.hasWallet && walletState.wallet;
  //   const hasBreezConnection = isBreezConnected && walletInfo;

  //   if (hasWallet && hasBreezConnection && recentPayments?.length > 0) {
  //     return recentPayments.slice(0, 3).map((payment, index) => ({
  //       id: index + 1,
  //       type: payment.paymentType === 'receive' ? 'deposit' : 'withdrawal',
  //       date: new Date(
  //         (payment.timestamp || Date.now() / 1000) * 1000
  //       ).toISOString(),
  //       amount: convertSatoshis(payment.amountSat, 'PLN'),
  //       btc: payment.amountSat / 100_000_000,
  //     }));
  //   }
  //   return walletState.hasWallet ? [] : [];
  // }, [
  //   walletState,
  //   isBreezConnected,
  //   walletInfo,
  //   recentPayments,
  //   convertSatoshis,
  // ]);

  const { verificationStatus, pollVerificationStatus } = useVeriffStore();

  useEffect(() => {
    if (verificationStatus === VerificationStatus.PENDING) {
      pollVerificationStatus();
    }
  }, [verificationStatus, pollVerificationStatus]);

  const quickActions = useMemo((): QuickAction[] => {
    const baseActions: QuickAction[] = [];

    if (!walletState.hasWallet) {
      baseActions.unshift({
        id: 'walletCreation',
        type: 'wallet-creation' as const,
        titleKey: 'dashboard.walletCreationBanner.text',
        buttonKey: 'dashboard.walletCreationBanner.button',
        highlightWordKey: 'dashboard.walletCreationBanner.highlightWord',
        image: require('@assets/wallet-image.png'),
        route: '(wallet)' as const,
      });
    }

    if (
      !(
        verificationStatus === VerificationStatus.PENDING ||
        verificationStatus === VerificationStatus.COMPLETED
      )
    ) {
      baseActions.unshift({
        id: 'verification',
        type: 'verification' as const,
        titleKey: 'dashboard.verificationBanner.text',
        buttonKey: 'dashboard.verificationBanner.button',
        highlightWordKey: 'dashboard.verificationBanner.highlightWord',
        image: require('@assets/security-image.png'),
        route: '(id-verification)/verification-entry' as const,
      });
    }

    if (!hasSavingPlan) {
      baseActions.unshift({
        id: 'configuration',
        type: 'configuration' as const,
        titleKey: 'dashboard.configurationBanner.text',
        buttonKey: 'dashboard.configurationBanner.button',
        highlightWordKey: 'dashboard.configurationBanner.highlightWord',
        image: require('@assets/configuration-illustration.png'),
        route: '(saving-plan)' as const,
      });
    }

    return baseActions;
  }, [walletState.hasWallet, hasSavingPlan, verificationStatus]);

  const bitcoinCardData = useMemo(() => {
    if (priceData?.bitcoin.PLN) {
      return {
        bitcoinValue: priceData.bitcoin.PLN,
        bitcoinChangePercent: MOCK_DATA.bitcoinChangePercent,
        bitcoinChangeTimeframe: MOCK_DATA.bitcoinChangeTimeframe,
      };
    }
    return {
      bitcoinValue: MOCK_DATA.bitcoinValue,
      bitcoinChangePercent: MOCK_DATA.bitcoinChangePercent,
      bitcoinChangeTimeframe: MOCK_DATA.bitcoinChangeTimeframe,
    };
  }, [priceData]);

  const openSavingsDetail = useCallback(() => {
    router.push({
      pathname: '/(authenticated)/savings-detail',
      params: {
        savingsValue: walletData.savingsValue.toString(),
        depositedAmount: walletData.depositedAmount.toString(),
        savingsChartData: JSON.stringify(walletData.savingsChartData),
        depositedChartData: JSON.stringify(walletData.depositedChartData),
      },
    });
  }, [router, walletData]);

  return (
    <PageLayout
      leftComponent={
        <TouchableOpacity
          onPress={() => router.push('(navigation)')}
          style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}
        >
          <RetiLogo height={32} style={{ borderRadius: 16 }} width={32} />
          <Text size="lg">
            {t('dashboard.greeting')}{' '}
            {profileInfo?.profile?.first_and_middle_name
              ? toTitleCase(profileInfo?.profile?.first_and_middle_name)
              : userInfo?.username?.split('@')[0]}
            !
          </Text>
        </TouchableOpacity>
      }
      leftComponentContainerProps={{
        alignItems: 'flex-start',
        paddingInline: 16,
        width: '100%',
      }}
      noAnimation
      noPadding
      onRefresh={handleRefresh}
      pullToRefresh
      refreshIndicatorOffset={30}
    >
      <Box align="center" mb={16} mt={12} px={4}>
        <Text color="grey.500" mb={4} size="md">
          {t('dashboard.totalBalance')}
        </Text>
        <Title color="black" size="xl" weight="medium">
          {walletState.hasWallet
            ? !(isBreezConnected && walletInfo) || isExchangeRateLoading
              ? '— PLN'
              : `${Number(
                  (walletData.portfolio * walletData.bitcoinValue).toFixed(2)
                )
                  .toLocaleString('en-US')
                  .replace(',', ' ')} PLN`
            : '0.00 PLN'}
        </Title>
        <Text color="grey.500" mt={1}>
          {walletState.hasWallet
            ? isBreezConnected && walletInfo
              ? `≈ ${walletData.portfolio.toLocaleString('en-US', {
                  minimumFractionDigits: 8,
                  maximumFractionDigits: 8,
                })} BTC`
              : '≈ — BTC'
            : '≈ 0.00000000 BTC'}
        </Text>
      </Box>
      {/* TODO: add loading state */}
      {quickActions.length > 0 && (
        <QuickActionsSlider quickActions={quickActions} />
      )}

      <Box mb={6} px={4}>
        <Text color="grey.500" mb={2}>
          {t('dashboard.sections.savings')}
        </Text>
        {isWalletDataLoading ? (
          <DashboardSavingsChartSkeleton />
        ) : (
          <TouchableOpacity activeOpacity={0.8} onPress={openSavingsDetail}>
            <SavingsChart
              depositedAmount={walletData.depositedAmount}
              depositedChartData={walletData.depositedChartData}
              savingsChartData={walletData.savingsChartData}
              savingsValue={walletData.savingsValue}
            />
          </TouchableOpacity>
        )}
        {isWalletDataLoading ? (
          <DashboardBitcoinCardSkeleton />
        ) : (
          <BitcoinCard
            bitcoinChangePercent={bitcoinCardData.bitcoinChangePercent}
            bitcoinChangeTimeframe={bitcoinCardData.bitcoinChangeTimeframe}
            bitcoinValue={bitcoinCardData.bitcoinValue}
          />
        )}
      </Box>

      <Box mb={6} px={4}>
        <Text color="grey.500" mb={2}>
          {t('dashboard.sections.transactions')}
        </Text>
        {isTransactionsLoading ? (
          <DashboardTransactionsSkeleton />
        ) : (
          <TransactionsSection />
        )}
      </Box>
    </PageLayout>
  );
}
