import SvgArrowUpRight from '@assets/arrow-up-right.svg';
import { useTranslations } from '@hooks';
import { Box, PageLayout, Text } from '@ui';
import { useLocalSearchParams } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { IconButton } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import { SavingsChart } from '../../components/dashboard/savings-chart';

export default function SavingsDetailScreen() {
  const t = useTranslations();
  const params = useLocalSearchParams();
  const { theme } = useUnistyles();

  const savingsValue = Number(params.savingsValue) || 0;
  const depositedAmount = Number(params.depositedAmount) || 0;
  const savingsChartData = params.savingsChartData
    ? JSON.parse(params.savingsChartData as string)
    : [];
  const depositedChartData = params.depositedChartData
    ? JSON.parse(params.depositedChartData as string)
    : [];

  return (
    <PageLayout
      bgColor="white"
      statusBarOptions={{
        backgroundColor: 'transparent',
        barStyle: 'dark-content',
        translucent: true,
      }}
      title={t('dashboard.modal.savings.title')}
    >
      <Box mb={6}>
        <Text color="grey.500" mb={2} size="md">
          {t('dashboard.sections.savings')}
        </Text>

        <SavingsChart
          depositedAmount={depositedAmount}
          depositedChartData={depositedChartData}
          savingsChartData={savingsChartData}
          savingsValue={savingsValue}
        >
          <Box align="start">
            <Box style={styles.timeControls}>
              <TouchableOpacity
                style={[styles.timeButton, styles.timeButtonActive]}
              >
                <IconButton
                  icon="infinity"
                  iconColor="#3F3F3F"
                  size={16}
                  style={styles.infinityIcon}
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.timeButton}>
                <Text style={styles.timeButtonText}>
                  {t('dashboard.modal.savings.timeframes.5y')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.timeButton}>
                <Text style={styles.timeButtonText}>
                  {t('dashboard.modal.savings.timeframes.1y')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.timeButton}>
                <Text style={styles.timeButtonText}>
                  {t('dashboard.modal.savings.timeframes.6m')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.timeButton}>
                <Text style={styles.timeButtonText}>
                  {t('dashboard.modal.savings.timeframes.1m')}
                </Text>
              </TouchableOpacity>
            </Box>
          </Box>
        </SavingsChart>
      </Box>

      <Box mt={2}>
        <Box align="baseline" flexDirection="row" justify="between" mb={2}>
          <Text color="grey.500" size="md">
            {t('dashboard.modal.savings.detailsTitle')}
          </Text>
          <Text color="dark" size="sm">
            {t('dashboard.modal.savings.fullPeriod')}
          </Text>
        </Box>

        <Box style={styles.detailsCard}>
          <Box align="center" flexDirection="row" justify="between" py={3}>
            <Text color="grey.500" size="sm">
              {t('dashboard.modal.savings.totalBalancePLN')}
            </Text>
            <Text color="dark" size="md" weight="medium">
              {savingsValue
                .toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
                .replace(',', ' ')}{' '}
              PLN
            </Text>
          </Box>

          <Box align="center" flexDirection="row" justify="between" py={3}>
            <Text color="grey.500" size="sm">
              {t('dashboard.modal.savings.totalBalanceBTC')}
            </Text>
            <Text color="dark" size="md" weight="medium">
              0.9832 BTC
            </Text>
          </Box>

          <Box align="center" flexDirection="row" justify="between" py={3}>
            <Text color="grey.500" size="sm">
              {t('dashboard.modal.savings.deposited')}
            </Text>
            <Text color="dark" size="md" weight="medium">
              {depositedAmount
                .toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
                .replace(',', ' ')}{' '}
              PLN
            </Text>
          </Box>

          <Box align="center" flexDirection="row" justify="between" py={3}>
            <Text color="grey.500" size="sm">
              {t('dashboard.modal.savings.percentageBalance')}
            </Text>
            <Box
              align="center"
              flexDirection="row"
              style={styles.percentageContainer}
            >
              <Text
                color="dark"
                size="md"
                style={{ color: theme.colors.green }}
              >
                +38.26%
              </Text>
              <Box align="center" justify="center" ml={1}>
                <SvgArrowUpRight />
              </Box>
            </Box>
          </Box>
          <Box align="center" flexDirection="row" justify="between" py={3}>
            <Text color="grey.500" size="sm">
              {t('dashboard.modal.savings.amountBalance')}
            </Text>
            <Text color="dark" size="md" weight="medium">
              {Number(95_000)
                .toLocaleString('en-US', {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                })
                .replace(',', ' ')}{' '}
              PLN
            </Text>
          </Box>
        </Box>
      </Box>
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  timeControls: {
    flexDirection: 'row',
    backgroundColor: theme.colors.grey[100],
    borderRadius: 100,
    padding: theme.padding(0.25),
    gap: theme.gap(0.25),
    height: 32,
  },
  timeButton: {
    paddingHorizontal: theme.padding(2.5),
    borderRadius: 100,
    backgroundColor: 'transparent',
    minWidth: 36,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeButtonActive: {
    backgroundColor: theme.colors.white,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeButtonText: {
    fontSize: 16,
    fontWeight: '400',
    color: theme.colors.grey[300],
    lineHeight: 19.2,
    textAlign: 'center',
  },
  infinityIcon: {
    margin: 0,
  },
  detailsCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 16,
    padding: theme.padding(4),
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  percentageContainer: {
    gap: theme.gap(1),
  },
}));
