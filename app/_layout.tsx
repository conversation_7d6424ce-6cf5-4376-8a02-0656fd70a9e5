import { Buffer } from 'buffer';

global.Buffer = Buffer;

import 'react-native-gesture-handler';
import { getQueryClient } from '@api';
import { MainContainer } from '@components';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { useOnAuthorizationChange } from '@hooks';
import { QueryClientProvider } from '@tanstack/react-query';
import { theme } from '@theme';
import { Modal } from '@ui';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { memo, useEffect, useMemo, useState } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PaperProvider } from 'react-native-paper';
import '@utils/dayjs-config';
import '@utils/i18next';

SplashScreen.preventAutoHideAsync();

const RootLayoutNav = memo(({ appIsReady }: { appIsReady: boolean }) => {
  useOnAuthorizationChange(appIsReady);

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(authorization)" />
      <Stack.Screen name="(registration)" />
      <Stack.Screen name="(id-verification)" />
      <Stack.Screen name="(authenticated)" />
      <Stack.Screen name="(wallet)" />
      <Stack.Screen name="(password-reset)" />
      <Stack.Screen name="(confirmation)" />
      <Stack.Screen name="(bank-connection)" />
    </Stack>
  );
});

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    'Rubik-Regular': require('@assets/fonts/Rubik-Regular.ttf'),
  });
  const [appIsReady, setAppIsReady] = useState(false);

  const queryClient = useMemo(() => getQueryClient(), []);

  useEffect(() => {
    const initializeApp = async () => {
      if (appIsReady && fontsLoaded) {
        await SplashScreen.hideAsync();
      }
    };
    initializeApp();
    setAppIsReady(true);
  }, [appIsReady, fontsLoaded]);

  useEffect(() => {
    if (appIsReady) {
      const { breezAppLifecycleManager } = require('@breez');

      const reconnectionCallback = async (): Promise<boolean> => {
        try {
          const { default: useBreezSdkStore } = await import(
            '@store/breez-sdk.store'
          );
          const breezStore = useBreezSdkStore.getState();

          if (!breezStore.isConnected && breezStore.isInitialized) {
            const reconnectSuccess = await breezStore.forceReconnect();
            if (!reconnectSuccess) {
              await breezStore.startAutoConnection();
            }
            return reconnectSuccess;
          }
          return true;
        } catch {
          return false;
        }
      };

      breezAppLifecycleManager.initialize(reconnectionCallback);

      return () => {
        breezAppLifecycleManager.cleanup();
      };
    }
  }, [appIsReady]);

  if (!(fontsLoaded && appIsReady)) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <BottomSheetModalProvider>
        <QueryClientProvider client={queryClient}>
          <PaperProvider theme={theme}>
            <Modal />
            <MainContainer>
              <RootLayoutNav appIsReady={appIsReady} />
            </MainContainer>
          </PaperProvider>
        </QueryClientProvider>
      </BottomSheetModalProvider>
    </GestureHandlerRootView>
  );
}
