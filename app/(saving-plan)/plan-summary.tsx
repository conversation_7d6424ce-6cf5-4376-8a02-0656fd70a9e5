import {
  getSavingPlanSettings,
  type SavingPlanProps,
} from '@endpoints/saving-plan.endpoints';
import useBreezSdkStore from '@store/breez-sdk.store';
import { useQuery } from '@tanstack/react-query';
import { Box, CachedImage, HeaderBackButton, Text, Title } from '@ui';
import { getQueryData } from '@utils/api/api-functions';
import { transactionsLogger } from '@utils/logger';
import { TransactionItem } from 'components/dashboard/transactions-section';
import { SavingPlanSummaryInfo } from 'components/saving-plan/summary-info';
import { Label } from 'components/saving-plan/summary-parts';
import { router } from 'expo-router';
import { useTranslations } from 'hooks/use-translations';
import { useEffect, useMemo } from 'react';
import { Dimensions, ScrollView, StatusBar, View } from 'react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

const HEADER_HEIGHT = 108;

const savingPlanBackgrounds = {
  house: require('@assets/saving-plan/house-bg.png'),
  bucket: require('@assets/saving-plan/bucket-bg.png'),
  car: require('@assets/saving-plan/car-bg.png'),
  luggage: require('@assets/saving-plan/luggage-bg.png'),
  event: require('@assets/saving-plan/event-bg.png'),
  upbringing: require('@assets/saving-plan/upbringing-bg.png'),
  graduation: require('@assets/saving-plan/graduation-bg.png'),
  safety: require('@assets/saving-plan/safety-bg.png'),
  finance: require('@assets/saving-plan/finance-bg.png'),
  company: require('@assets/saving-plan/company-bg.png'),
  technology: require('@assets/saving-plan/technology-bg.png'),
} as const;

type SavingPlanBackground = keyof typeof savingPlanBackgrounds;

export default function ExistingPlanSummaryScreen() {
  const insets = useSafeAreaInsets();
  const t = useTranslations('wallet');

  const { data: plan } = useQuery(
    getQueryData<SavingPlanProps>(getSavingPlanSettings, {
      queryOptions: { retry: false, staleTime: 60_000 },
    })
  );
  const { recentPayments, isConnected, loadPayments } = useBreezSdkStore();

  const hasTransactions = useMemo(
    () => recentPayments.length > 0,
    [recentPayments.length]
  );

  useEffect(() => {
    const loadAll = async () => {
      try {
        if (isConnected && recentPayments.length === 0) {
          await loadPayments({ limit: 200, offset: 0 });
        }
      } catch (error) {
        transactionsLogger.error(
          'Failed to load plan summary transactions',
          error
        );
      }
    };
    loadAll();
  }, [isConnected, recentPayments.length, loadPayments]);

  return (
    <Box bgColor="primary.600" flex={1}>
      <StatusBar barStyle="light-content" />

      <View
        style={[
          styles.headerOverlay,
          {
            paddingTop: insets.top,
            paddingLeft: insets.left,
            paddingRight: insets.right,
          },
        ]}
      >
        <View style={styles.headerButton}>
          <HeaderBackButton
            overrideOnPress={() => router.replace('(navigation)')}
          />
        </View>
        {/* <View style={styles.headerButton}>
          <IconButton icon="help-circle-outline" onPress={handleHelpPress} />
        </View> */}
      </View>

      <Box h="45%" left={0} pos="absolute" right={0} top={0} zIndex={0}>
        <CachedImage
          source={
            savingPlanBackgrounds[plan?.target?.name as SavingPlanBackground] ??
            savingPlanBackgrounds.house
          }
          style={styles.image}
        />
      </Box>

      <Animated.View entering={FadeIn.delay(500)} style={styles.header}>
        <Title color="white" weight="semibold">
          {plan?.name}
        </Title>
        <Text color="primary.100" weight="regular">
          {plan?.target?.icon_label}
        </Text>
      </Animated.View>

      <Box bottom={0} h="55%" left={0} pos="absolute" right={0} zIndex={2}>
        <Box style={styles.card}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            style={{ width: '100%' }}
          >
            {plan && (
              <SavingPlanSummaryInfo
                adjustedPlanOption={plan.plan_type}
                adjustedPlanPercentage={plan.percentage_value}
                fixedPlanValue={plan.amount_value}
                frequency={plan.purchase_interval}
                ignoreTransactionsAbove={plan.ignore_transaction_above}
                savingAmount={plan.amount_value}
                selectedPlan={plan.plan_type}
                timeHorizon={plan.time_horizon}
              />
            )}

            <Box gap={3} style={{ width: '100%' }}>
              <Label>{t('transactions.title')}</Label>

              {hasTransactions && isConnected ? (
                <Box gap={3}>
                  {recentPayments.map((payment, index) => (
                    <TransactionItem
                      key={payment.txId || `payment-${index}`}
                      payment={payment}
                    />
                  ))}
                </Box>
              ) : (
                <Box align="center" p={6}>
                  <Text color="grey.300" textAlign="center">
                    {t('transactions.noTransactions')}
                  </Text>
                </Box>
              )}
            </Box>
          </ScrollView>
        </Box>
      </Box>
    </Box>
  );
}

const styles = StyleSheet.create((theme) => ({
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_HEIGHT,
    zIndex: 3,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerButton: {
    width: 60,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  selectedIconContainer: {
    width: 72,
    height: 72,
    borderRadius: 100,
    backgroundColor: theme.colors.grey[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  image: {
    position: 'absolute',
    top: 0,
    left: '50%',
    opacity: 0.6,
    transform: [
      { translateX: -(Math.min(329, screenWidth - 32) / 2) },
      { scale: 1.4 },
    ],
    right: 0,
    bottom: 0,
    zIndex: 0,
    width: Math.min(329, screenWidth - 32),
    contentFit: 'contain',
  },
  header: {
    height: '45%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 1,
  },
  card: {
    flex: 2,
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    width: '100%',
    backgroundColor: theme.colors.grey[50],
    paddingVertical: 30,
    paddingHorizontal: 24,
  },
  scrollContent: {
    paddingBottom: 20,
  },
}));
