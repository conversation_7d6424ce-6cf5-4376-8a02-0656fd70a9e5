import { getQueryData } from '@api';
import SvgCheckIconFilled from '@assets/check-circle-filled.svg';
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import {
  getSavingPlanTargets,
  type SavingPlansData,
  type Target,
} from '@endpoints/saving-plan.endpoints';
import { useSetProgressPosition, useTranslations } from '@hooks';
import useSavingPlanStore from '@store/saving-plan.store';
import { useQuery } from '@tanstack/react-query';
import { Alert, Box, CachedImage, HeaderCloseButton, PageLayout } from '@ui';
import { memo, useCallback, useMemo } from 'react';
import { Dimensions, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const numColumns = 2;
const screenWidth = Dimensions.get('window').width;

const savingPlanIcons = {
  house: require('@assets/saving-plan/house.png'),
  bucket: require('@assets/saving-plan/bucket.png'),
  car: require('@assets/saving-plan/car.png'),
  luggage: require('@assets/saving-plan/luggage.png'),
  event: require('@assets/saving-plan/event.png'),
  upbringing: require('@assets/saving-plan/upbringing.png'),
  graduation: require('@assets/saving-plan/graduation.png'),
  safety: require('@assets/saving-plan/safety.png'),
  finance: require('@assets/saving-plan/finance.png'),
  company: require('@assets/saving-plan/company.png'),
  technology: require('@assets/saving-plan/technology.png'),
} as const;

type SavingPlanIcon = keyof typeof savingPlanIcons;

const GridItem = memo(
  ({ item, itemSize }: { item: Target; itemSize: number }) => {
    const { setTarget, selectedTarget } = useSavingPlanStore();
    const { theme } = useUnistyles();

    const handlePress = useCallback(() => {
      setTarget(item);
    }, [item, setTarget]);

    const isSelected = item.id === selectedTarget?.id;

    return (
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={handlePress}
        style={[
          styles.item,
          { height: itemSize, width: itemSize },
          isSelected && styles.itemSelected,
        ]}
      >
        {isSelected && (
          <Box pos="absolute" right={8} top={8}>
            <SvgCheckIconFilled height={24} width={24} />
          </Box>
        )}
        {item.icon && (
          <CachedImage
            source={savingPlanIcons[item.icon as SavingPlanIcon]}
            style={{ width: 72, height: 72 }}
          />
        )}
        <Text
          style={[
            isSelected && {
              color: theme.colors.primary[500],
            },
          ]}
        >
          {item.icon_label}
        </Text>
      </TouchableOpacity>
    );
  }
);

export default function SavingPlanStep2() {
  const t = useTranslations();
  const { theme } = useUnistyles();

  const { selectedTarget } = useSavingPlanStore();

  useSetProgressPosition(1, DEFAULT_SAVING_PLAN__TOTAL_STEPS);

  const itemMargin = theme.padding(4);
  const itemSize = useMemo(
    () => (screenWidth - itemMargin * (numColumns + 1)) / numColumns,
    [itemMargin]
  );

  const mapIcon = useCallback((iconLabel: string): string => {
    return (
      [
        { icon: 'house', title: 'Własne M' },
        { icon: 'bucket', title: 'Remont i urządzanie' },
        { icon: 'car', title: 'Auto i transport' },
        { icon: 'luggage', title: 'Podróże' },
        { icon: 'event', title: 'Wydarzenie rodzinne' },
        { icon: 'upbringing', title: 'Start dziecka w dorosłość' },
        { icon: 'graduation', title: 'Edukacja i rozwój' },
        { icon: 'safety', title: 'Poduszka bezpieczeństwa' },
        { icon: 'finance', title: 'Emerytura' },
        { icon: 'company', title: 'Własna firma' },
        { icon: 'technology', title: 'Technologia i gadżety' },
      ].find((element) => element.title === iconLabel)?.icon || ''
    );
  }, []);

  const { data } = useQuery(
    getQueryData<SavingPlansData>(getSavingPlanTargets)
  );

  const targets = useMemo(
    () =>
      data?.targets.flat(2).map((item) => ({
        ...item,
        icon: mapIcon(item.icon_label),
      })) || [],
    [data, mapIcon]
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: selectedTarget === undefined,
        href: '/saving-plan-step-3',
      }}
      buttonText={t('common.continue')}
      contentMarginTop={2}
      headerProgress={1 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.savingGoalSelection.title')}
    >
      <Alert message="savingPlan.savingGoalSelection.info" type="info" />
      <Box
        flexDirection="row"
        justify="between"
        mt={5}
        style={{ flexWrap: 'wrap' }}
      >
        {targets.map((item) => (
          <GridItem item={item} itemSize={itemSize} key={item.id} />
        ))}
      </Box>
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  screenContainer: {
    backgroundColor: theme.colors.grey[50],
  },
  item: {
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    backgroundColor: theme.colors.white,
    boxShadow: '0px 4px 24px -8px rgba(13, 61, 50, 0.10)',
    elevation: 3,
    marginBottom: theme.gap(4),
  },
  text: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
    color: theme.colors.black,
  },
  checkContainer: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  itemSelected: {
    borderWidth: 1,
    borderColor: theme.colors.primary[500],
  },
}));
