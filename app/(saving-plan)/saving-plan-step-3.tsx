import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { useTranslations } from '@hooks';
import useSavingPlanStore from '@store/saving-plan.store';
import { Alert, Box, HeaderCloseButton, PageLayout, TextInput } from '@ui';

export default () => {
  const t = useTranslations();

  const { planName, setPlanName } = useSavingPlanStore();

  return (
    <PageLayout
      buttonProps={{
        disabled: !planName,
        href: '/saving-plan-step-4',
      }}
      buttonText={t('common.continue')}
      contentMarginTop={2}
      headerProgress={2 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.nameStep.title')}
    >
      <Alert message="savingPlan.nameStep.info" type="info" />
      <Box mt={5}>
        <TextInput
          autoFocus
          label={t('savingPlan.nameStep.inputLabel')}
          onChangeText={setPlanName}
          placeholder={t('savingPlan.nameStep.placeholder')}
          value={planName}
        />
      </Box>
    </PageLayout>
  );
};
