import { Stack } from 'expo-router';

const AdjustedLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        headerShown: false,
        title: '',
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="adjusted-ignore-transactions"
        options={{
          title: '',
        }}
      />
    </Stack>
  );
};

export default AdjustedLayout;
