import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { useTranslations } from '@hooks';
import useSavingPlanStore from '@store/saving-plan.store';
import { Alert, HeaderCloseButton, PageLayout, Stepper, Text } from '@ui';

export default () => {
  const t = useTranslations();

  const { ignoreTransactionsAbove, setIgnoreTransactionsAbove } =
    useSavingPlanStore();

  return (
    <PageLayout
      buttonProps={{
        disabled: !ignoreTransactionsAbove,
        href: '/summary',
      }}
      buttonText={t('common.continue')}
      contentGap={5}
      contentMarginTop={2}
      headerProgress={9 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.ignoreTransactionsStep.title')}
    >
      <Text color="grey.500" size="sm">
        {t('savingPlan.ignoreTransactionsStep.inputLabel')}
      </Text>
      <Stepper
        max={2000}
        min={0}
        onChange={(value) => setIgnoreTransactionsAbove(value ?? 0)}
        placeholder="0"
        step={50}
        unit="savingPlan.ignoreTransactionsStep.unit"
        value={ignoreTransactionsAbove}
      />
      <Alert
        message="savingPlan.ignoreTransactionsStep.message"
        type="warning"
      />
    </PageLayout>
  );
};
