import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { useTranslations } from '@hooks';
import useSavingPlanStore, { PlanType } from '@store/saving-plan.store';
import {
  Alert,
  Box,
  HeaderCloseButton,
  PageLayout,
  RadioCard,
  RadioGroup,
  Stepper,
  Text,
} from '@ui';
import { useCallback } from 'react';

// TODO HARDCODED PERCENTAGE FIXTURE FIKSTURA
const WARNING_ADJUSTED_PLAN_PERCENTAGE_VALUE = 10;

const AdjustedPlanPercentage = () => {
  const t = useTranslations();

  const { adjustedPlanPercentage, setAdjustedPlanPercentage } =
    useSavingPlanStore();

  return (
    <Box gap={4}>
      <Text color="grey.600" size="sm">
        {t('savingPlan.adjustedPlan.percentagePlan.description')}
      </Text>
      <Stepper
        max={100}
        min={2}
        onChange={(percentage) =>
          setAdjustedPlanPercentage(percentage as number)
        }
        placeholder="2"
        step={1}
        unit="units.percent"
        value={adjustedPlanPercentage}
      />
      {adjustedPlanPercentage > WARNING_ADJUSTED_PLAN_PERCENTAGE_VALUE && (
        <Alert
          message="savingPlan.adjustedPlan.percentagePlan.alert"
          type="warning"
        />
      )}
    </Box>
  );
};

const AdjustedPlanFixedAmount = () => {
  const t = useTranslations();

  const { setFixedPlanValue, fixedPlanValue } = useSavingPlanStore();

  return (
    <Box gap={4}>
      <Text color="grey.600" size="sm">
        {t('savingPlan.adjustedPlan.fixedPlan.description')}
      </Text>
      <Stepper
        max={100}
        min={1}
        onChange={(value) => setFixedPlanValue(value as number)}
        placeholder="0"
        step={5}
        unit="savingPlan.amountStep.unit"
        value={fixedPlanValue}
      />
    </Box>
  );
};

const adjustedPlans = [
  {
    key: PlanType.Percentage,
    titleKey: 'savingPlan.adjustedPlan.percentagePlan.heading',
    icon: require('@assets/saving-plan/plan_adjusted_percent.png'),
    component: <AdjustedPlanPercentage />,
  },
  {
    key: PlanType.Amount,
    titleKey: 'savingPlan.adjustedPlan.fixedPlan.heading',
    icon: require('@assets/saving-plan/plan_adjusted_const.png'),
    component: <AdjustedPlanFixedAmount />,
  },
];

export default () => {
  const t = useTranslations();

  const { adjustedPlanOption, setAdjustedPlanOption } = useSavingPlanStore();

  const onSelect = useCallback(
    (id: string) => {
      setAdjustedPlanOption(id as PlanType);
    },
    [setAdjustedPlanOption]
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: !adjustedPlanOption,
        href: '/(saving-plan)/adjusted/adjusted-ignore-transactions',
      }}
      buttonText={t('common.continue')}
      contentMarginTop={2}
      headerProgress={4 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.adjustedPlan.title')}
    >
      <RadioGroup
        onValueChange={onSelect}
        value={adjustedPlanOption?.toString() as string}
      >
        {adjustedPlans.map((plan) => (
          <RadioCard
            icon={plan.icon}
            key={plan.key}
            title={t(plan.titleKey)}
            titleProps={{
              size: 'lg',
            }}
            value={plan.key.toString()}
          >
            {plan.component}
          </RadioCard>
        ))}
      </RadioGroup>
    </PageLayout>
  );
};
