import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { useSetProgressPosition, useTranslations } from '@hooks';
import useSavingPlanStore, { PlanType } from '@store/saving-plan.store';
import {
  <PERSON><PERSON>,
  Badge,
  Box,
  HeaderCloseButton,
  PageLayout,
  RadioCard,
  RadioGroup,
  Text,
} from '@ui';
import { useCallback } from 'react';

const icons = {
  [PlanType.Percentage]: require('@assets/saving-plan/plan_percentage.png'),
  [PlanType.ReccuringPurchase]: require('@assets/saving-plan/plan_reccuring.png'),
};

const plans = [
  {
    key: PlanType.Percentage,
    titleKey: 'savingPlan.plans.adjusted.title',
    descriptionKey: 'savingPlan.plans.adjusted.description',
    tagsAmount: 3,
    alert: {
      message: 'savingPlan.plans.adjusted.message',
      type: 'success' as const,
    },
  },
  {
    key: PlanType.ReccuringPurchase,
    titleKey: 'savingPlan.plans.simple.title',
    descriptionKey: 'savingPlan.plans.simple.description',
    tagsAmount: 3,
    alert: {
      message: 'savingPlan.plans.simple.message',
      type: 'warning' as const,
    },
  },
];

export default () => {
  const t = useTranslations();

  const { selectedPlan, setSelectedPlan } = useSavingPlanStore();

  useSetProgressPosition(4, DEFAULT_SAVING_PLAN__TOTAL_STEPS);

  const onSelect = useCallback(
    (planName: string) => {
      setSelectedPlan(planName as PlanType);
    },
    [setSelectedPlan]
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: !selectedPlan,
        href:
          selectedPlan === PlanType.Percentage
            ? '/(saving-plan)/adjusted'
            : '/(saving-plan)/simple',
      }}
      buttonText={t('common.continue')}
      contentMarginTop={2}
      headerProgress={4 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.plans.title')}
    >
      <RadioGroup
        onValueChange={onSelect}
        value={selectedPlan?.toString() as string}
      >
        {plans.map((plan) => (
          <RadioCard
            icon={icons[plan.key]}
            key={plan.key}
            title={t(plan.titleKey)}
            titleProps={{
              size: 'lg',
            }}
            value={plan.key.toString()}
          >
            <Box gap={2}>
              <Text color="grey.600">{t(plan.descriptionKey)}</Text>
              {plan.tagsAmount && (
                <Box flexDirection="row" gap={1}>
                  {Array.from({ length: plan.tagsAmount }, (_, index) => (
                    <Badge
                      // biome-ignore lint/suspicious/noArrayIndexKey: no other keys available
                      key={index}
                      variant={
                        plan.key === PlanType.Percentage
                          ? 'lightPrimary'
                          : 'grey'
                      }
                    >
                      {t(
                        `savingPlan.plans.${plan.key === PlanType.Percentage ? 'adjusted' : 'simple'}.tags.${index + 1}`
                      )}
                    </Badge>
                  ))}
                </Box>
              )}
              <Alert message={plan.alert.message} type={plan.alert.type} />
            </Box>
          </RadioCard>
        ))}
      </RadioGroup>
    </PageLayout>
  );
};
