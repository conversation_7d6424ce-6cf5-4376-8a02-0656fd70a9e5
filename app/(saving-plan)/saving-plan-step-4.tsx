import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { usePluralUnit, useTranslations } from '@hooks';
import useSavingPlanStore from '@store/saving-plan.store';
import { Alert, Box, HeaderCloseButton, PageLayout, Stepper } from '@ui';
import { useEffect } from 'react';

export default () => {
  const t = useTranslations();

  const { timeHorizon, setTimeHorizon } = useSavingPlanStore();
  const getPluralUnit = usePluralUnit();

  const handleTimeHorizonChange = (value: number | null) => {
    setTimeHorizon(value || 0);
  };

  useEffect(() => {
    if (!timeHorizon) {
      setTimeHorizon(1);
    }
  }, [timeHorizon, setTimeHorizon]);

  return (
    <PageLayout
      buttonProps={{
        href: '/saving-plan-step-5',
      }}
      buttonText={t('common.continue')}
      contentMarginTop={2}
      headerProgress={3 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.timeHorizonStep.title')}
    >
      <Alert message="savingPlan.timeHorizonStep.info" type="info" />
      <Box mt={10}>
        <Stepper
          max={100}
          min={1}
          onChange={handleTimeHorizonChange}
          placeholder="1"
          step={1}
          unit={getPluralUnit(timeHorizon, 'savingPlan.timeHorizonStep.unit')}
          value={timeHorizon}
        />
      </Box>
    </PageLayout>
  );
};
