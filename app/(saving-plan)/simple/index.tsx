import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/saving-plan-steps';
import { useTranslations } from '@hooks';
import useSavingPlanStore from '@store/saving-plan.store';
import { Box, HeaderCloseButton, PageLayout, Stepper, Title } from '@ui';
import { Chip } from 'react-native-paper';
import { StyleSheet } from 'react-native-unistyles';

const frequencies = ['W', 'M', 'Q'] as const;

export default () => {
  const t = useTranslations();

  const { savingAmountAndFrequency, setSavingAmountAndFrequency } =
    useSavingPlanStore();

  return (
    <PageLayout
      buttonProps={{
        disabled:
          savingAmountAndFrequency.amount === 0 ||
          savingAmountAndFrequency.frequency === null,
        href: '/summary',
      }}
      buttonText={t('common.continue')}
      contentGap={2}
      contentMarginTop={2}
      headerProgress={9 / DEFAULT_SAVING_PLAN__TOTAL_STEPS}
      headerWithProgress
      rightComponent={<HeaderCloseButton />}
      rightComponentContainerProps={{
        justifyContent: 'center',
        alignItems: 'flex-end',
        paddingRight: 16,
      }}
      title={t('savingPlan.amountStep.title')}
    >
      <Box gap={2}>
        <Title color="grey" size="sm">
          {t('savingPlan.amountStep.amountLabel')}
        </Title>
        <Stepper
          max={9_999_999}
          min={1}
          onChange={(amount) =>
            setSavingAmountAndFrequency({
              amount: amount ?? 0,
              frequency: savingAmountAndFrequency.frequency,
            })
          }
          placeholder="0"
          step={50}
          unit="savingPlan.amountStep.unit"
          value={savingAmountAndFrequency.amount}
        />
      </Box>
      <Box gap={2}>
        <Title color="grey" size="sm">
          {t('savingPlan.amountStep.frequencyLabel')}
        </Title>
        <Box flexDirection="row" gap={2} wrap>
          {frequencies.map((frequency) => (
            <Chip
              key={frequency}
              mode="outlined"
              onPress={() =>
                setSavingAmountAndFrequency({
                  amount: savingAmountAndFrequency.amount,
                  frequency,
                })
              }
              style={[
                styles.chip,
                savingAmountAndFrequency.frequency === frequency &&
                  styles.chipSelected,
              ]}
              textStyle={[
                styles.chipText,
                savingAmountAndFrequency.frequency === frequency &&
                  styles.chipTextSelected,
              ]}
            >
              {t(`savingPlan.amountStep.frequencies.${frequency}`)}
            </Chip>
          ))}
        </Box>
      </Box>
    </PageLayout>
  );
};

const styles = StyleSheet.create((theme) => ({
  screenContainer: {
    backgroundColor: theme.colors.grey[50],
  },
  contentContainer: {
    display: 'flex',
    gap: 8,
  },
  amountLabel: {
    fontSize: 14,
    fontWeight: 400,
    color: theme.colors.grey[600],
    marginBottom: 4,
  },
  frequencyTags: {
    marginTop: 4,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  chip: {
    borderRadius: 100,
    paddingHorizontal: 14,
    paddingVertical: 7,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: theme.colors.grey[300],
    backgroundColor: theme.colors.grey[50],
  },
  chipText: {
    fontWeight: '400',
    fontSize: 16,
    color: theme.colors.dark,
  },
  chipSelected: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },
  chipTextSelected: {
    color: theme.colors.grey[50],
  },
}));
