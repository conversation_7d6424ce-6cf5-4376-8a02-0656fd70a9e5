import { useTranslations } from '@hooks';
import {
  Badge,
  Box,
  CachedImage,
  FeatureSection,
  PageLayout,
  Title,
} from '@ui';
import { Dimensions } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

export default () => {
  const t = useTranslations();

  return (
    <PageLayout
      bgColor="grey.900"
      buttonBlurOptions={{
        tint: 'dark',
      }}
      buttonGradientVariant="dark"
      buttonProps={{
        href: '/saving-plan-step-2',
      }}
      buttonText={t('savingPlan.button')}
      contentGap={4}
      headerBlurOptions={{
        intensity: 25,
        tint: 'systemUltraThinMaterialDark',
      }}
      rightComponent={
        <Badge size="lg" variant="primary">
          {t('savingPlan.introduction')}
        </Badge>
      }
      rightComponentContainerProps={{
        width: 'auto',
        marginRight: 16,
      }}
      statusBarOptions={{
        barStyle: 'light-content',
      }}
      withBackgroundGradient
    >
      <Box align="center" mb={4}>
        <CachedImage
          source={require('@assets/saving-plan-pig.png')}
          style={styles.pigImage}
        />
      </Box>
      <Title color="white" pr={5} size="lg">
        {t('savingPlan.formProps.title')}
      </Title>
      <Box flex={1} gap={4}>
        <FeatureSection headline="savingPlan.howItWorks.title" icon="bitcoin">
          {t('savingPlan.howItWorks.description')}
        </FeatureSection>
        <FeatureSection headline="savingPlan.whyItIsWorth.title" icon="bitcoin">
          {t('savingPlan.whyItIsWorth.description')}
        </FeatureSection>
      </Box>
    </PageLayout>
  );
};

const styles = StyleSheet.create(() => ({
  pigImage: {
    width: Math.min(329, screenWidth - 32),
    height: Math.min(329, screenWidth - 32),
    contentFit: 'cover',
  },
}));
