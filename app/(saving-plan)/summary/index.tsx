import { fetchAPI, getQueryClient } from '@api';
import {
  getSavingPlanSettings,
  postSavingPlanSettings,
  type SavingPlanPostProps,
} from '@endpoints/saving-plan.endpoints';
import { useTranslations } from '@hooks';
import useSavingPlanStore, { PlanType } from '@store/saving-plan.store';
import { useMutation } from '@tanstack/react-query';
import { Box, Button, CachedImage, Text, Title } from '@ui';
import { SavingPlanInfoFromStore } from 'components/saving-plan/summary-info';
import { useRouter } from 'expo-router';
import { Dimensions, StatusBar, View } from 'react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StyleSheet } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

const savingPlanBackgrounds = {
  house: require('@assets/saving-plan/house-bg.png'),
  bucket: require('@assets/saving-plan/bucket-bg.png'),
  car: require('@assets/saving-plan/car-bg.png'),
  luggage: require('@assets/saving-plan/luggage-bg.png'),
  event: require('@assets/saving-plan/event-bg.png'),
  upbringing: require('@assets/saving-plan/upbringing-bg.png'),
  graduation: require('@assets/saving-plan/graduation-bg.png'),
  safety: require('@assets/saving-plan/safety-bg.png'),
  finance: require('@assets/saving-plan/finance-bg.png'),
  company: require('@assets/saving-plan/company-bg.png'),
  technology: require('@assets/saving-plan/technology-bg.png'),
} as const;

type SavingPlanBackground = keyof typeof savingPlanBackgrounds;

export default () => {
  const t = useTranslations();
  const router = useRouter();
  // const { theme } = useUnistyles();

  const {
    planName,
    timeHorizon,
    selectedPlan,
    adjustedPlanPercentage,
    selectedTarget,
    ignoreTransactionsAbove,
    fixedPlanValue,
    savingAmountAndFrequency,
    adjustedPlanOption,
  } = useSavingPlanStore();

  const { mutate: createPlanPress, isPending: loading } = useMutation({
    mutationFn: async () => {
      const dataToPost: SavingPlanPostProps = {
        name: planName,
        time_horizon: timeHorizon,
        plan_type: selectedPlan,
        target_id: selectedTarget?.id,
        ignore_transaction_above: ignoreTransactionsAbove,
        ...(adjustedPlanOption === PlanType.Amount
          ? { amount_value: fixedPlanValue }
          : adjustedPlanOption === PlanType.Percentage
            ? { percentage_value: adjustedPlanPercentage }
            : { percentage_value: undefined }),
        ...(selectedPlan === PlanType.ReccuringPurchase && {
          amount_value: savingAmountAndFrequency.amount,
          purchase_interval: savingAmountAndFrequency.frequency,
        }),
      };
      if (selectedPlan === PlanType.ReccuringPurchase) {
        dataToPost.percentage_value = undefined;
        dataToPost.ignore_transaction_above = undefined;
      }
      // TODO CLEAN UP NOT NEEDED FIELDS DEPENDING ON PLAN TYPE
      await fetchAPI(postSavingPlanSettings, { data: dataToPost });
      return;
    },
    onSuccess: () => {
      getQueryClient().invalidateQueries({
        queryKey: [getSavingPlanSettings.key],
      });

      router.push('(confirmation)/saving-plan-created');
    },
    // biome-ignore lint/suspicious/noConsole: debug
    onError: (e) => console.log('error', (e as any).response.data),
  });

  return (
    <Box bgColor="primary.600" flex={1}>
      <StatusBar barStyle="light-content" />
      {/* <LinearGradient
        colors={[
          getColorWithAlpha(theme.colors.primary[800], 0.4) as string,
          getColorWithAlpha(theme.colors.primary[800], 0.8) as string,
        ]}
        style={{
          ...StyleSheet.absoluteFillObject,
          zIndex: 1,
          height: '100%',
        }}
      /> */}
      <Box h="45%" left={0} pos="absolute" right={0} top={0} zIndex={0}>
        <CachedImage
          source={
            savingPlanBackgrounds[
              selectedTarget?.name as SavingPlanBackground
            ] ?? savingPlanBackgrounds.house
          }
          style={styles.image}
        />
      </Box>
      <Animated.View entering={FadeIn.delay(500)} style={styles.header}>
        <Title color="white" weight="semibold">
          {planName}
        </Title>
        {/* TODO translate */}
        <Text color="primary.100" weight="regular">
          {selectedTarget?.icon_label}
        </Text>
      </Animated.View>
      {/* <Animated.View
          entering={SlideInDown.delay(150)
            .duration(400)
            .easing(Easing.inOut(Easing.ease))}
          style={styles.card}
        > */}
      <Box style={styles.card}>
        <View>
          <SavingPlanInfoFromStore />
        </View>
        <Box flexDirection="row" gap={2} justify="around" mb={3} w="100%">
          <Button href="/(saving-plan)" variant="outlined" w="45%">
            {t('savingPlan.summary.buttons.changePlan')}
          </Button>
          <Button loading={loading} onPress={() => createPlanPress()} w="45%">
            {t('savingPlan.summary.buttons.createPlan')}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export const InfoSavedPlanContainer = () => {
  return <SavingPlanInfoFromStore />;
};

const styles = StyleSheet.create((theme) => ({
  selectedIconContainer: {
    width: 72,
    height: 72,
    borderRadius: 100,
    backgroundColor: theme.colors.grey[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  image: {
    position: 'absolute',
    top: 0,
    left: '50%',
    transform: [
      { translateX: -(Math.min(329, screenWidth - 32) / 2) },
      { scale: 1.4 },
    ],
    right: 0,
    bottom: 0,
    zIndex: 0,
    width: Math.min(329, screenWidth - 32),
    contentFit: 'contain',
  },
  header: {
    height: '45%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 1,
  },
  card: {
    flex: 2,
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    width: '100%',
    backgroundColor: theme.colors.grey[50],
    paddingVertical: 30,
    paddingHorizontal: 24,
  },
  labelContainer: {
    marginBottom: 15,
  },
}));
