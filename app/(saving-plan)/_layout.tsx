import { Stack } from 'expo-router';

const SavingPlanLayout = () => {
  return (
    <Stack
      screenOptions={{
        title: '',
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="simple"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="adjusted"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="summary"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="plan-summary"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="saving-plan-step-2"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="saving-plan-step-3"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="saving-plan-step-4"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="saving-plan-step-5"
        options={{
          title: '',
        }}
      />
    </Stack>
  );
};

export default SavingPlanLayout;
