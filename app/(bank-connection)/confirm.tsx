import { confirmActivation } from '@endpoints/bank-connection.endpoints';
import { useTranslations } from '@hooks';
import { PageLayout, Text } from '@ui';
import { fetchAPI } from '@utils/api';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

export default function ConfirmBankScreen() {
  const { ref, error } = useLocalSearchParams<{
    ref: string;
    error?: string;
    details?: string;
  }>();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>(
    'loading'
  );
  const [message, setMessage] = useState('');
  const router = useRouter();
  const t = useTranslations();
  const { theme } = useUnistyles();

  useEffect(() => {
    if (!ref) {
      setStatus('error');
      setMessage(t('bank-connection.noRef'));
      return;
    }

    // If the webview reported a user-cancelled session, show a friendlier message without calling confirm
    if (error === 'UserCancelledSession') {
      setStatus('error');
      setMessage(t('bank-connection.userCancelled'));
      return;
    }

    fetchAPI(confirmActivation, { params: { ref } })
      .then(() => {
        setStatus('success');
        setMessage(t('bank-connection.success'));
        setTimeout(() => {
          router.replace('(bank-connection)');
        }, 3000);
      })
      .catch(() => {
        setStatus('error');
        setMessage(t('bank-connection.error'));
      });
  }, [ref, error, t, router]);

  return (
    <PageLayout
      bgColor="white"
      buttonProps={{
        href: '/(bank-connection)/connect-bank-account',
        hrefType: 'replace',
      }}
      buttonText={t('common.tryAgain')}
      contentMarginTop={4}
      description={
        status === 'error' && error === 'UserCancelledSession'
          ? t('bank-connection.userCancelledDescription')
          : t('bank-connection.confirmationDescription')
      }
      title={
        status === 'error' && error === 'UserCancelledSession'
          ? t('bank-connection.userCancelledTitle')
          : t('bank-connection.confirmationTitle')
      }
    >
      {status === 'loading' && (
        <ActivityIndicator color={theme.colors.primary[500]} size="large" />
      )}
      {status !== 'loading' && status !== 'error' && (
        <Text style={styles.message}>{message}</Text>
      )}
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  message: {
    fontSize: 18,
    textAlign: 'center',
    color: theme.colors.dark,
    marginBottom: 20,
  },
  info: {
    fontSize: 16,
    color: theme.colors.dark,
    textAlign: 'center',
    marginBottom: 10,
  },
  backButton: {
    fontSize: 16,
    color: theme.colors.primary[500],
    marginTop: 20,
    textDecorationLine: 'underline',
  },
}));
