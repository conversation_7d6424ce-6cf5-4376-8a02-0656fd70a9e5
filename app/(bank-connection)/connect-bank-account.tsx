import {
  type Bank,
  type ConfirmBankChoiceResponse,
  confirmBankChoice,
  getAllBanks,
} from '@endpoints/bank-connection.endpoints';
import { useTranslations } from '@hooks';
import { useQuery } from '@tanstack/react-query';
import { Box, PageLayout, Text } from '@ui';
import { fetchAPI, getQueryData } from '@utils/api';
import { router } from 'expo-router';
import { ActivityIndicator, Image, TouchableOpacity } from 'react-native';
import { IconButton } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

export default function BankListScreen() {
  const { theme } = useUnistyles();
  const t = useTranslations();

  const { data: banks, isLoading } = useQuery(
    getQueryData<Bank[]>(getAllBanks)
  );

  const handlePress = async (bankItem: Bank) => {
    const response = await fetchAPI<ConfirmBankChoiceResponse>(
      confirmBankChoice,
      {
        slugs: { bankId: bankItem.id },
      }
    );
    router.replace({
      pathname: '/(bank-connection)/webview',
      params: {
        id: bankItem.id,
        url: response.link,
      },
    });
  };

  if (isLoading) {
    return (
      <PageLayout
        description={t('bank-connection.chooseBank')}
        title={t('bank-connection.addNewAccount')}
      >
        <ActivityIndicator color={theme.colors.primary[600]} size="large" />
      </PageLayout>
    );
  }

  return (
    <PageLayout
      bgColor="white"
      description={t('bank-connection.chooseBank')}
      title={t('bank-connection.addNewAccount')}
    >
      {banks?.length ? (
        banks?.map?.((bank) => (
          <TouchableOpacity
            key={bank.id}
            onPress={() => handlePress(bank)}
            style={styles.bankItem}
          >
            <Box align="center" flexDirection="row">
              <Image source={{ uri: bank.logo }} style={styles.logo} />
              <Text size="lg">{bank.name}</Text>
            </Box>
            <IconButton
              icon="chevron-right"
              iconColor={theme.colors.grey[600]}
              onPress={() => handlePress(bank)}
              size={24}
              style={styles.chevron}
            />
          </TouchableOpacity>
        ))
      ) : (
        <Text>{t('bank-connection.noBanks')}</Text>
      )}
    </PageLayout>
  );
}

const styles = StyleSheet.create(() => ({
  bankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  logo: {
    width: 32,
    height: 32,
    marginRight: 12,
    borderRadius: 4,
  },
  chevron: {
    margin: 0,
  },
}));
