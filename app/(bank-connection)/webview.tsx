import { useTranslations } from '@hooks';
import { PageLayout, Text } from '@ui';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native-unistyles';
import { WebView } from 'react-native-webview';

export default function AisWebViewScreen() {
  const { url } = useLocalSearchParams();
  const router = useRouter();
  const t = useTranslations();

  if (!url) {
    return (
      <PageLayout>
        <Text>{t('messages.urlError')}</Text>
      </PageLayout>
    );
  }

  const handleRedirect = (request: any) => {
    const targetUrl = request.url;
    if (
      targetUrl.includes('reti://bank-connection/confirmed-nordigen-activation')
    ) {
      const parsed = new URL(targetUrl);
      const ref = parsed.searchParams.get('ref');
      const error = parsed.searchParams.get('error');
      const details = parsed.searchParams.get('details');

      if (ref) {
        router.replace({
          pathname: '/(bank-connection)/confirm',
          params: {
            ref,
            error: error ?? undefined,
            details: details ?? undefined,
          },
        });
        return false;
      }
    }

    return true;
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <WebView
        onNavigationStateChange={(navState) => {
          handleRedirect(navState);
        }}
        onShouldStartLoadWithRequest={handleRedirect}
        originWhitelist={['*']}
        source={{ uri: url as string }}
        startInLoadingState={true}
        style={styles.webview}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create(() => ({
  webview: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}));
