import { deleteBankAccountConnection } from '@endpoints/bank-connection.endpoints';
import { useTranslations } from '@hooks';
import useBankListStore from '@store/bank-connection.store';
import { Box, Button, PageLayout, Text } from '@ui';
import { fetchAPI } from '@utils/api';
import { BankCard } from 'components/bank-connection/bank-card';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useCallback } from 'react';
import { getBankLogo, getBankName } from '.';

const Details = () => {
  const router = useRouter();
  const t = useTranslations();
  const { accountId } = useLocalSearchParams();
  const { bankAccounts, bankInfoDict } = useBankListStore();

  const account = bankAccounts.find((acc) => acc.id === accountId);

  const handleDisconnectAccount = useCallback(async () => {
    await fetchAPI(deleteBankAccountConnection);
    router.back();
  }, [router]);

  if (!account) {
    return (
      <PageLayout title={t('common.notFound')}>
        {/** biome-ignore lint/complexity/noUselessFragments: . */}
        <></>
      </PageLayout>
    );
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return t('common.none');
    const date = new Date(dateString);

    return date.toLocaleDateString('pl-PL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <PageLayout
      bgColor="white"
      buttonProps={{
        variant: 'muted',
        href: '/(bank-connection)',
      }}
      buttonText={t('common.back')}
      contentGap={6}
      contentMarginTop={4}
      title={t('bank-connection.details')}
    >
      <BankCard
        accountNumber={account.iban}
        allowUnmask={true}
        logo={getBankLogo(bankInfoDict, account.institution_id)}
        name={getBankName(bankInfoDict, account)}
      />

      <Box align="center" flexDirection="row" justify="between">
        <Text>{t('bank-connection.connectionDate')}:</Text>
        <Text color="grey.900">{formatDate(account.created)}</Text>
      </Box>

      <Box align="center" flexDirection="row" justify="between">
        <Text>{t('bank-connection.lastRenew')}:</Text>
        <Text color="grey.900">{formatDate(account.last_accessed)}</Text>
      </Box>

      <Box align="center" flexDirection="row" justify="between">
        <Text>{t('bank-connection.expiryDate')}:</Text>
        <Text color="grey.900">{formatDate(account.last_accessed)}</Text>
      </Box>

      <Box flexDirection="row" justify="between" w="100%">
        <Button
          onPress={handleDisconnectAccount}
          size="xs"
          variant="outlinedGrey"
          w="49%"
        >
          {t('bank-connection.disconect')}
        </Button>
        <Button
          onPress={() => console.log('renew', account.id)}
          size="xs"
          variant="muted"
          w="49%"
        >
          {t('bank-connection.renew')}
        </Button>
      </Box>
    </PageLayout>
  );
};

export default Details;
