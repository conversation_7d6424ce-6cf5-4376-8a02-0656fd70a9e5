import { fetchAPI } from '@api';
import {
  type Bank,
  type BankAccount,
  type BankInfoDict,
  deleteBankAccountConnection,
  getAllBanks,
  getBankAccountsInfo,
} from '@endpoints/bank-connection.endpoints';
import { useTranslations } from '@hooks';
import useBankListStore from '@store/bank-connection.store';
import { useQuery } from '@tanstack/react-query';
import { Box, PageLayout, Text } from '@ui';
import { getQueryData } from '@utils/api';
import { BankActionButtons } from 'components/bank-connection/bank-action-buttons';
import { BankCard } from 'components/bank-connection/bank-card';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  type ImageSourcePropType,
  View,
} from 'react-native';
import { IconButton } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

export const getBankLogo = (
  bankDict: BankInfoDict,
  id: string
): ImageSourcePropType => {
  return bankDict[id]?.logo
    ? { uri: bankDict[id].logo }
    : require('../../assets/logo.png');
};

export const getBankName = (
  bankDict: BankInfoDict,
  account: BankAccount
): string => {
  return account.name || bankDict[account.institution_id]?.name || '';
};

export default function BankConnectionScreen() {
  const router = useRouter();
  const t = useTranslations();
  const { theme } = useUnistyles();

  const { bankInfoDict, setBankInfoDict, bankAccounts, setBankAccounts } =
    useBankListStore();
  const [showModal, setShowModal] = useState(false);

  const { data: banks } = useQuery<Bank[]>(getQueryData(getAllBanks));
  const { data: availableAccounts, isLoading: isAccountsLoading } = useQuery<
    BankAccount[]
  >(getQueryData(getBankAccountsInfo));

  const hasBankAccount = useMemo(
    () => bankAccounts?.length > 0,
    [bankAccounts]
  );

  useEffect(() => {
    setBankAccounts(availableAccounts ?? []);

    if (!banks?.length) return;

    const newBankInfoDict: BankInfoDict = {};
    banks?.forEach?.((bank) => {
      newBankInfoDict[bank.id] = {
        logo: bank.logo,
        name: bank.name,
      };
    });

    setBankInfoDict(newBankInfoDict);
  }, [banks, setBankInfoDict, availableAccounts, setBankAccounts]);

  useCallback(() => setShowModal(!showModal), [showModal]);

  const handleAccountDetails = (accountId: string) => {
    router.push({
      pathname: '/(bank-connection)/details',
      params: { accountId },
    });
  };

  const handleDisconnectAccount = useCallback(async () => {
    await fetchAPI(deleteBankAccountConnection);
    setBankAccounts([]);
  }, [setBankAccounts]);

  const formatAccountNumber = useCallback((accountNumber: string) => {
    if (!accountNumber) {
      return 'PL •••• •••• •••• ••••';
    }
    const visibleDigits = 4;
    const maskedPart = '•'.repeat(
      Math.max(0, accountNumber?.length - visibleDigits)
    );
    return `${maskedPart}${accountNumber?.slice(-visibleDigits)}`;
  }, []);

  return (
    <PageLayout
      bgColor="white"
      buttonProps={{
        href: hasBankAccount
          ? undefined
          : '(bank-connection)/connect-bank-account',
        hrefType: 'replace',
        variant: hasBankAccount ? 'muted' : undefined,
      }}
      buttonText={
        isAccountsLoading
          ? undefined
          : hasBankAccount
            ? t('common.back')
            : t('bank-connection.buttonTitle')
      }
      contentGap={6}
      contentMarginTop={4}
      description={t('bank-connection.description')}
      onButtonPress={hasBankAccount ? () => router.back() : undefined}
      rightComponent={
        <IconButton
          containerColor={theme.colors.grey[100]}
          icon="help"
          // onPress={toggleModal}
          size={18}
        />
      }
      rightComponentContainerProps={{
        width: 'auto',
        marginRight: 16,
      }}
      title={t('bank-connection.title')}
    >
      {isAccountsLoading && (
        <ActivityIndicator color={theme.colors.primary[500]} size="large" />
      )}
      {!isAccountsLoading && hasBankAccount ? (
        <Box gap={4}>
          {bankAccounts?.map?.((account) => (
            <BankCard
              accountNumber={formatAccountNumber(account.name)}
              allowUnmask={false}
              key={account.id}
              logo={getBankLogo(bankInfoDict, account.institution_id)}
              name={getBankName(bankInfoDict, account)}
            >
              {/* <View style={styles.bankExpiryRow}>
                <View style={styles.expiryLabelContainer}>
                  <Text style={styles.bankExpiryText}>{t('common.expires')}</Text>
                  <IconButton 
                    icon="information-outline" 
                    size={16} 
                    onPress={showExpiryTooltip}
                    style={styles.tooltipIcon}
                  />
                </View>
                <Text style={styles.bankExpiryText}>
                  {new Date(account.createDate).toLocaleDateString()}
                </Text>
              </View>

              {shouldShowRenewButton(new Date(account.createDate)) && (
                <UiHeaderButton
                  translationTitle="bank-connection.renewConnection"
                  onPress={() => handleRenewConnection(account.accountId)}
                />
              )} */}

              <BankActionButtons
                onLeftButtonClick={() => handleAccountDetails(account.id)}
                onRightButtonClick={handleDisconnectAccount}
              />
            </BankCard>
          ))}
        </Box>
      ) : (
        !isAccountsLoading && (
          <View style={styles.emptyContainer}>
            <Image
              resizeMode="contain"
              source={require('../../assets/no-banks-image.png')}
              style={styles.noBanksImage}
            />
            <Text color="grey.400" textAlign="center">
              {t('bank-connection.noAccounts')}
            </Text>
          </View>
        )
      )}

      {/* <Modal
          animationType="slide"
          onRequestClose={toggleModal}
          transparent={true}
          visible={showModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text>{t('bank-connection.helpModalContent')}</Text>
              <IconButton
                icon="close"
                onPress={toggleModal}
                style={styles.closeButton}
              />
            </View>
          </View>
        </Modal> */}
    </PageLayout>
  );
}
const styles = StyleSheet.create((theme) => ({
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
  },
  bankCard: {
    width: '100%',
    padding: 16,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: theme.colors.grey[100],
    backgroundColor: theme.colors.grey[50],
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    alignItems: 'center',
  },
  closeButton: {
    marginTop: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 48,
    marginBottom: 32,
    gap: 24,
  },
  noBanksImage: {
    width: '100%',
    height: 170,
    opacity: 0.9,
  },
  expiryLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}));
