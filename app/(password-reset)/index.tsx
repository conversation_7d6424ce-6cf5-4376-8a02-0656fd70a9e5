import { fetchAP<PERSON> } from '@api';
import { ConfirmationScreen, EmailForm, ErrorContainer } from '@components';
import {
  confirmPasswordReset,
  startPasswordReset,
} from '@endpoints/password-reset.endpoints';
import { useTranslations, useValidator } from '@hooks';
import useAuthStore from '@store/auth.store';
import useErrorStore from '@store/error.store';
import { usePasswordResetCooldownStore } from '@store/password-reset.store';
import { useMutation } from '@tanstack/react-query';
import { Box, PageLayout, Text, TextInput } from '@ui';
import { passwordConfirmSchema, passwordSchema } from '@utils';
import { ResendResetLink } from 'components/password-reset/resend-reset-link';
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { AuthState } from 'types/auth.types';
import { ErrorK<PERSON> } from 'types/error.types';

export default () => {
  const t = useTranslations();
  const tNavigation = useTranslations('navigation');
  const router = useRouter();
  const params = useGlobalSearchParams();
  const email = (params?.email as string) || '';
  const token = (params?.token as string) || '';

  const { canSend, markSent } = usePasswordResetCooldownStore();
  const { authenticated } = useAuthStore();

  // DEV: allow pasting token manually when deep link param is not present
  // const [devToken, setDevToken] = useState('');
  const effectiveToken = token; // || devToken;

  const showEmailStep = !(email || effectiveToken);
  const showLinkSentStep = !!email && !effectiveToken;
  const showSetPasswordStep = !!effectiveToken; // allow without email

  const description = useMemo(() => {
    if (showLinkSentStep) return;
    if (showEmailStep) return t('passwordReset.descriptions.email');
    return t('passwordReset.descriptions.setPassword');
  }, [showEmailStep, showLinkSentStep, t]);

  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirm, setNewPasswordConfirm] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const [passwordValidatorMessage, validatePassword] =
    useValidator(passwordSchema);
  const [passwordConfirmValidatorMessage, validateConfirmPassword] =
    useValidator(passwordConfirmSchema);

  const { addError, clearError } = useErrorStore();

  const { mutate: startReset } = useMutation({
    mutationFn: () => fetchAPI(startPasswordReset, { data: { email } }),
    onSuccess: () => markSent(email),
    onError: (data: { error: string; status: number }) => {
      addError(ErrorKey.PasswordResetError, {
        key: ErrorKey.PasswordResetError,
        errorText: data.error,
        status: data?.status,
        translate: false,
      });
    },
  });

  const { mutate: submitReset, isPending: loading } = useMutation({
    mutationFn: () =>
      fetchAPI(confirmPasswordReset, {
        slugs: { token: effectiveToken },
        data: {
          new_password: newPassword,
          confirm_password: newPasswordConfirm,
        },
      }),
    onSuccess: () => {
      if (authenticated === AuthState.Authenticated) {
        setIsSuccess(true);
      } else {
        const navParams = email ? { email } : undefined;
        router.replace({ pathname: '/sign-in', params: navParams });
      }
    },
    onError: (data: { error: string; status: number }) => {
      addError(ErrorKey.PasswordResetError, {
        key: ErrorKey.PasswordResetError,
        errorText: data.error,
        status: data?.status,
        translate: false,
      });
    },
  });

  useEffect(() => {
    clearError(ErrorKey.PasswordResetError);
    if (showLinkSentStep && email && canSend(email)) {
      startReset();
    }
  }, [clearError, showLinkSentStep, email, canSend, startReset]);

  // Auto-redirect after showing success when authenticated
  useFocusEffect(
    useCallback(() => {
      if (!(isSuccess && authenticated === AuthState.Authenticated)) {
        return;
      }
      const REDIRECT_TIMEOUT = 4000;
      const timeout = setTimeout(() => {
        router.replace('/(navigation)');
      }, REDIRECT_TIMEOUT);
      return () => clearTimeout(timeout);
    }, [isSuccess, authenticated, router])
  );

  const onPasswordChange = useCallback(
    (value: string) => {
      setNewPassword(value);
      validatePassword(value);
      validateConfirmPassword({
        password: value,
        passwordConfirm: newPasswordConfirm,
      });
    },
    [newPasswordConfirm, validateConfirmPassword, validatePassword]
  );

  const onConfirmPasswordChange = useCallback(
    (value: string) => {
      setNewPasswordConfirm(value);
      validateConfirmPassword({
        password: newPassword,
        passwordConfirm: value,
      });
    },
    [newPassword, validateConfirmPassword]
  );

  const isFormValid = !(
    passwordValidatorMessage ||
    passwordConfirmValidatorMessage ||
    newPassword.length === 0 ||
    newPasswordConfirm.length === 0
  );

  if (isSuccess && authenticated === AuthState.Authenticated) {
    return (
      <ConfirmationScreen
        buttonTitle="common.continue"
        message="requestInfo.success"
        onButtonPress={() => router.replace('/(navigation)')}
      />
    );
  }

  return (
    <PageLayout
      buttonProps={
        showSetPasswordStep ? { disabled: !isFormValid, loading } : undefined
      }
      buttonText={
        showSetPasswordStep ? tNavigation('security.changePassword') : undefined
      }
      contentGap={5}
      contentMarginTop={5}
      description={description}
      onButtonPress={showSetPasswordStep ? () => submitReset() : undefined}
      title={t('passwordReset.title')}
    >
      {showEmailStep && <EmailForm redirectPath="/(password-reset)" />}

      {showLinkSentStep && (
        <Box gap={4}>
          <ErrorContainer errorKey={ErrorKey.PasswordResetError} />
          <Text size="lg">{t('passwordReset.linkSentInfo')}</Text>
          {/* DEV: paste token manually */}
          {/* <TextInput
            autoCapitalize="none"
            label="DEV: Paste token"
            onChangeText={setDevToken}
            placeholder="abc123..."
            value={devToken}
          /> */}
          <ResendResetLink email={email} />
        </Box>
      )}

      {showSetPasswordStep && (
        <>
          <ErrorContainer errorKey={ErrorKey.PasswordResetError} />
          <TextInput
            autoCapitalize="none"
            errorMessage={passwordValidatorMessage}
            isPassword
            label={t('signUp.inputs.password.label')}
            onChangeText={onPasswordChange}
            value={newPassword}
          />
          <TextInput
            autoCapitalize="none"
            errorMessage={passwordConfirmValidatorMessage}
            isPassword
            label={t('signUp.inputs.confirmPassword.label')}
            onChangeText={onConfirmPasswordChange}
            value={newPasswordConfirm}
          />
        </>
      )}
    </PageLayout>
  );
};
