// biome-ignore-all lint: development flag

import { SecureStorageKeys } from '@const/secure-storage-keys';
import { useBackgroundWalletCreationStatus, useTranslations } from '@hooks';
import useAuthStore from '@store/auth.store';
import useBreezSdkStore from '@store/breez-sdk.store';
import {
  Box,
  Button,
  ConfirmationDialog,
  CopyButton,
  PageLayout,
  Text,
  Title,
} from '@ui';
import { createLogger } from '@utils/logger';
import { TIMING_CONSTANTS } from '@utils/wallet/constants';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';
import { SeedPhrase } from 'components/wallet/create-wallet/seed-phrase';
import {
  adaptPaymentToUnified,
  UnifiedTransactionItem,
} from 'components/wallet/unified-transaction-item';
import { BOOLEAN_STRINGS, DEV_CONSTANTS } from 'const';
import { useRouter } from 'expo-router';
import { getItemAsync, setItemAsync } from 'expo-secure-store';
import { useBackgroundSync } from 'hooks/use-background-sync';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { useWalletRefresh } from 'hooks/use-wallet-refresh';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, Switch, TouchableOpacity } from 'react-native';
import { useUnistyles } from 'react-native-unistyles';
import { authenticationService } from 'services/authentication.service';
import { AuthState } from 'types/auth.types';

const logger = createLogger('WalletScreen');

export default function WalletScreen() {
  const t = useTranslations('wallet');
  const router = useRouter();
  const { authenticated } = useAuthStore();
  const { theme } = useUnistyles();

  const isAuthenticated = authenticated === AuthState.Authenticated;

  const { state: walletState, actions: walletActions } = useSimpleWallet();

  const {
    isInProgress: isWalletCreationInProgress,
    error: walletCreationError,
    generatedAddress: backgroundGeneratedAddress,
    clearStatus: clearWalletCreationStatus,
  } = useBackgroundWalletCreationStatus();

  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);
  const [isLoadingBiometric, setIsLoadingBiometric] = useState(false);

  const {
    walletInfo,
    isConnected: isBreezConnected,
    recentPayments,
    isLoading: isBreezLoading,
    generatedWalletAddress,
  } = useBreezSdkStore();

  // Use the proper exchange rate hook for currency conversion
  const {
    convertSatoshis,
    isLoading: isExchangeRateLoading,
    priceData,
  } = useExchangeRate();

  const { handleRefresh } = useWalletRefresh();

  const backgroundSyncOptions = useMemo(
    () => ({
      syncInterval: 60000,
      syncOnAppActive: false,
      enablePeriodicSync: false,
    }),
    []
  );

  useBackgroundSync(backgroundSyncOptions);

  // Monitor background wallet creation completion and refresh wallet info
  useEffect(() => {
    if (
      !isWalletCreationInProgress &&
      backgroundGeneratedAddress &&
      isAuthenticated &&
      isBreezConnected
    ) {
      logger.debug(
        '🎉 Background wallet creation completed, refreshing wallet info'
      );

      // Refresh wallet info to update balance and sync state
      const timeoutId = setTimeout(async () => {
        try {
          const { refreshWalletInfo: currentRefreshWalletInfo } =
            useBreezSdkStore.getState();
          await currentRefreshWalletInfo({ skipSync: true });
          logger.debug(
            '✅ Wallet info refreshed after background creation completion'
          );
        } catch (error) {
          logger.warn(
            '⚠️ Failed to refresh wallet info after background creation:',
            error
          );
        }
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [
    isWalletCreationInProgress,
    backgroundGeneratedAddress,
    isAuthenticated,
    isBreezConnected,
  ]);

  useEffect(() => {
    const loadBiometricSetting = async () => {
      try {
        const biometricEnabled = await getItemAsync(
          SecureStorageKeys.BIOMETRIC_ENABLED
        );
        setIsBiometricEnabled(biometricEnabled === BOOLEAN_STRINGS.TRUE);
      } catch (error) {
        logger.error('Failed to load biometric setting', error);
      }
    };

    if (__DEV__) {
      loadBiometricSetting();
    }
  }, []);

  const handleToggleBiometric = useCallback(async () => {
    if (!__DEV__) return;

    setIsLoadingBiometric(true);
    try {
      const newValue = !isBiometricEnabled;

      await setItemAsync(
        SecureStorageKeys.BIOMETRIC_ENABLED,
        newValue ? BOOLEAN_STRINGS.TRUE : BOOLEAN_STRINGS.FALSE
      );

      await authenticationService.initialize();
      setIsBiometricEnabled(newValue);

      Alert.alert(
        DEV_CONSTANTS.ALERT_TITLE,
        `Biometric authentication ${newValue ? 'enabled' : 'disabled'} for testing.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      logger.error('Failed to toggle biometric authentication', error);
      Alert.alert(
        DEV_CONSTANTS.ERROR_ALERT_TITLE,
        'Failed to toggle biometric authentication. Check console for details.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoadingBiometric(false);
    }
  }, [isBiometricEnabled]);

  const handleResetAuthLockout = useCallback(async () => {
    if (!__DEV__) return;

    Alert.alert(
      DEV_CONSTANTS.RESET_LOCKOUT_TITLE,
      DEV_CONSTANTS.RESET_LOCKOUT_MESSAGE,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              logger.operation('Resetting authentication lockout state');

              await authenticationService.resetLockoutState();

              Alert.alert('Success', DEV_CONSTANTS.RESET_LOCKOUT_SUCCESS, [
                { text: 'OK' },
              ]);

              logger.info('Authentication lockout reset successfully');
            } catch (error) {
              logger.error('Failed to reset authentication lockout', error);
              Alert.alert(
                DEV_CONSTANTS.ERROR_ALERT_TITLE,
                DEV_CONSTANTS.RESET_LOCKOUT_ERROR,
                [{ text: 'OK' }]
              );
            }
          },
        },
      ]
    );
  }, []);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleDeleteWallet = useCallback(async () => {
    setShowDeleteDialog(true);
  }, []);

  const confirmDeleteWallet = useCallback(async () => {
    setShowDeleteDialog(false);
    try {
      const breezStore = useBreezSdkStore.getState();
      const ok = await breezStore.deleteWallet?.();

      walletActions.clearWallet();

      if (!ok) {
        logger.warn('deleteWallet returned false');
      }

      logger.success('Wallet deletion completed');
      router.replace('/(wallet)/(create)/select-wallet-type');
    } catch (error) {
      logger.error('Wallet deletion failed', { error });
    }
  }, [walletActions, router]);

  const handleDebugStorage = async () => {
    logger.operation('Debugging storage contents');
    await walletActions.debugStorage();
  };

  const formatBalance = useCallback(
    (satoshis: number | undefined | null): string => {
      if (typeof satoshis !== 'number' || isNaN(satoshis)) {
        return '0.00000000';
      }
      return (satoshis / 100_000_000).toFixed(8);
    },
    []
  );

  const getEstimatedPLN = useCallback(
    (satoshis: number | undefined | null): number => {
      if (typeof satoshis !== 'number' || isNaN(satoshis)) return 0;
      return convertSatoshis(satoshis, 'PLN');
    },
    [convertSatoshis]
  );

  const truncateAddress = useCallback((address: string): string => {
    if (address.length <= 20) return address;
    return `${address.slice(0, 12)}...${address.slice(-12)}`;
  }, []);

  const displayBalance = useMemo(() => {
    if (isAuthenticated && isBreezConnected && walletInfo) {
      return {
        btc: `${formatBalance(walletInfo.balanceSat)}`,
        pln: `${getEstimatedPLN(walletInfo.balanceSat).toFixed(2)}`,
        isLive: true,
        loading: false,
      };
    }
    // Loading placeholders while fetching from Breez SDK (no wallet info yet)
    if (isAuthenticated && isBreezConnected && !walletInfo) {
      return {
        btc: '~ —',
        pln: '—',
        isLive: false,
        loading: true,
      };
    }
    // No wallet or not authenticated: show zeros
    return {
      btc: '0.00000000',
      pln: '0.00',
      isLive: false,
      loading: false,
    };
  }, [
    isAuthenticated,
    isBreezConnected,
    walletInfo,
    formatBalance,
    getEstimatedPLN,
  ]);

  // CRITICAL: Proper address display priority to ensure users see the correct address
  const displayAddress = useMemo(() => {
    // Priority 1: Background generated address (from current wallet creation/import)
    if (backgroundGeneratedAddress) {
      logger.debug('🎯 Using background generated address', {
        address: `${backgroundGeneratedAddress.substring(0, 10)}...`,
        source: 'background-generation',
      });
      return {
        address: backgroundGeneratedAddress,
        source: 'background-generation',
        isLive: true,
      };
    }

    // Priority 2: Generated wallet address from Breez SDK (from current connected wallet)
    if (isAuthenticated && isBreezConnected && generatedWalletAddress) {
      logger.debug('🎯 Using Breez SDK generated address', {
        address: `${generatedWalletAddress.substring(0, 10)}...`,
        source: 'breez-sdk',
      });
      return {
        address: generatedWalletAddress,
        source: 'breez-sdk',
        isLive: true,
      };
    }

    // Priority 3: Simple wallet address (fallback - may be from backend)
    if (walletState.wallet?.address) {
      logger.warn(
        '⚠️ Using backend address as fallback - may not match imported wallet!',
        {
          address: `${walletState.wallet.address.substring(0, 10)}...`,
          source: 'simple-wallet',
        }
      );
      return {
        address: walletState.wallet.address,
        source: 'simple-wallet',
        isLive: false,
      };
    }

    logger.warn('❌ No wallet address available for display');
    return null;
  }, [
    backgroundGeneratedAddress,
    isAuthenticated,
    isBreezConnected,
    generatedWalletAddress,
    walletState.wallet?.address,
  ]);

  const displayTransactions = useMemo(() => {
    const unifiedTransactions = (recentPayments || [])
      .slice(0, 3)
      .map((payment) =>
        adaptPaymentToUnified(payment, (satoshis: number) =>
          convertSatoshis(satoshis, 'PLN')
        )
      );

    return {
      transactions: unifiedTransactions,
      isLive:
        isAuthenticated && isBreezConnected && unifiedTransactions.length > 0,
      hasTransactions: unifiedTransactions.length > 0,
    };
  }, [isAuthenticated, isBreezConnected, recentPayments, convertSatoshis]);

  // Skip skeletons on wallet entry; show content immediately

  if (walletCreationError) {
    return (
      <PageLayout
        description={t('walletSetupFailed.description')}
        title={t('walletSetupFailed.title')}
      >
        <Box align="center" flex={1} justify="center" p={4}>
          <Box bgColor="red.50" mb={6} p={4} radius={12}>
            <Text color="red.700" size="sm" textAlign="center">
              {walletCreationError}
            </Text>
          </Box>

          <Button
            mb={4}
            onPress={clearWalletCreationStatus}
            size="lg"
            variant="contained"
          >
            {t('walletSetupFailed.tryAgain')}
          </Button>
        </Box>
      </PageLayout>
    );
  }

  if (
    walletState.isInitialized &&
    !walletState.hasWallet &&
    !isWalletCreationInProgress
  ) {
    return (
      <PageLayout
        buttonProps={{
          href: '/(wallet)/(create)/select-wallet-type',
          hrefType: 'replace',
        }}
        buttonText={t('noWalletFound.createWallet')}
        description={t('noWalletFound.description')}
        title={t('noWalletFound.title')}
      >
        {walletState.error && (
          <Box bgColor="red.50" mb={4} p={3} radius={8}>
            <Text color="red.700" size="sm" textAlign="center">
              {t('errors.generic')}: {walletState.error}
            </Text>
          </Box>
        )}

        {__DEV__ && (
          <Box gap={2} mt={6} w="100%">
            <Text color="grey.600" size="lg" weight="medium">
              {t('developmentTools.title')}
            </Text>
            <Button onPress={handleDebugStorage} size="xs" variant="outlined">
              {t('noWalletFound.debugStorage')}
            </Button>
          </Box>
        )}
      </PageLayout>
    );
  }

  return (
    <PageLayout
      contentMarginTop={6}
      onRefresh={handleRefresh}
      pullToRefresh
      title={t('title')}
    >
      <Box gap={6}>
        <Box gap={2}>
          <Text size="lg">{t('balance')}</Text>

          <Box gap={1}>
            <Title color="black" weight="medium">
              {displayBalance.btc} BTC
            </Title>
            <Text color="grey.600" size="lg">
              {displayBalance.loading || isExchangeRateLoading || !priceData
                ? '— PLN'
                : `≈ ${displayBalance.pln} PLN`}
            </Text>
          </Box>
        </Box>

        <Box gap={2}>
          <Text size="lg">{t('address')}</Text>

          {isWalletCreationInProgress ? (
            <Box align="center" flexDirection="row" gap={3}>
              <Box align="center" flexDirection="row" gap={2}>
                <Text color="grey.500" size="md">
                  🔄 {t('generatingAddress')}
                </Text>
              </Box>
            </Box>
          ) : displayAddress ? (
            <Box align="center" flexDirection="row" gap={3}>
              <Text
                accessibilityLabel={`Wallet address: ${displayAddress.address}`}
                color="black"
                flex={1}
                size="xxxl"
              >
                {truncateAddress(displayAddress.address)}
              </Text>
              <CopyButton textToCopy={displayAddress.address} />
            </Box>
          ) : walletCreationError ? (
            <Box gap={2}>
              <Text color="red" size="md">
                ❌ {t('addressGenerationFailed')}
              </Text>
              <Text color="grey.500" size="sm">
                {walletCreationError}
              </Text>
            </Box>
          ) : (
            <Text color="grey.400" size="md">
              {t('noAddressAvailable')}
            </Text>
          )}
        </Box>

        <Box gap={2}>
          <Text size="lg">{t('seedPhrase')}</Text>

          <SeedPhrase
            isBlurred={false}
            isRevealed={false}
            onPress={(e) => {
              e.stopPropagation();
              router.push('/(wallet)/view-seed-phrase');
            }}
            showEyeIcon
          />
        </Box>

        <Box gap={2}>
          <Box align="center" flexDirection="row" justify="between">
            <Text color="grey.600" size="lg" weight="medium">
              {t('transactions.title')}
            </Text>
            {displayTransactions.hasTransactions && (
              <TouchableOpacity
                onPress={() => router.push('/(wallet)/transactions')}
              >
                <Text color="primary" size="sm" weight="medium">
                  {t('transactions.viewAll')}
                </Text>
              </TouchableOpacity>
            )}
          </Box>

          {displayTransactions.hasTransactions ? (
            <Box gap={3}>
              {displayTransactions.transactions.map((transaction, index) => (
                <UnifiedTransactionItem
                  key={`${transaction.id}-${index}`}
                  transaction={transaction}
                />
              ))}
            </Box>
          ) : (
            <Box align="center" p={4}>
              <Text color="grey.600" size="sm" textAlign="center">
                {isAuthenticated && isBreezConnected
                  ? t('transactions.noTransactions')
                  : isAuthenticated
                    ? t('connection.loadingBalance')
                    : t('connection.authenticationRequired')}
              </Text>
            </Box>
          )}
        </Box>

        <Box bgColor="white" p={6} radius={12}>
          <Text color="grey.600" mb={4} size="lg" weight="medium">
            {t('developmentTools.title')}
          </Text>
          <Box gap={3}>
            <Box
              bgColor="grey.50"
              borderColor="grey.200"
              borderWidth={1}
              gap={2}
              p={3}
              radius={8}
            >
              <Box align="center" flexDirection="row" justify="between">
                <Box flex={1}>
                  <Text size="md" weight="medium">
                    {DEV_CONSTANTS.BIOMETRIC_TOGGLE_LABEL}
                  </Text>
                  <Text color="grey.600" size="sm">
                    {DEV_CONSTANTS.BIOMETRIC_TOGGLE_DESC}
                  </Text>
                </Box>
                <Switch
                  disabled={isLoadingBiometric}
                  onValueChange={handleToggleBiometric}
                  thumbColor={theme.colors.white}
                  trackColor={{
                    false: theme.colors.grey[200],
                    true: theme.colors.primary[500],
                  }}
                  value={isBiometricEnabled}
                />
              </Box>
            </Box>

            <Button
              href="/(wallet)/wallet-management"
              size="lg"
              variant="outlined"
            >
              {t('developmentTools.distributeBTC')}
            </Button>
            <Button onPress={handleDeleteWallet} size="lg" variant="outlined">
              {t('developmentTools.deleteWallet')}
            </Button>

            <ConfirmationDialog
              cancelButtonVariant="contained"
              cancelLabel={t('deleteWallet.confirm')}
              confirmButtonVariant="outlined"
              confirmLabel={t('deleteWallet.cancel')}
              message={t('deleteWallet.message')}
              onCancel={confirmDeleteWallet}
              onConfirm={() => setShowDeleteDialog(false)}
              title={t('deleteWallet.title')}
              visible={showDeleteDialog}
            />

            <Button
              onPress={handleResetAuthLockout}
              size="lg"
              variant="outlined"
            >
              {DEV_CONSTANTS.RESET_LOCKOUT_LABEL}
            </Button>
            {__DEV__ && (
              <Button onPress={handleDebugStorage} size="xs" variant="outlined">
                {t('developmentTools.debugStorage')}
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </PageLayout>
  );
}
