// biome-ignore-all lint: .

import { useTranslations } from '@hooks';
import {
  Box,
  CopyButton,
  CopyButtonVariants,
  PageLayout,
  Text,
  Title,
  Alert as UiAlert,
} from '@ui';
import Security from 'assets/password-required.png';
import { PasswordAuthenticationModal } from 'components/authentication/password-authentication-modal';
import { PinAuthenticationModal } from 'components/authentication/pin-authentication-modal';
import { CachedImage as Image } from 'components/ui/cached-image';
import { SeedPhrase } from 'components/wallet/create-wallet/seed-phrase';
import { AUTH_LIMITS, SecureStorageKeys } from 'const';
import { router } from 'expo-router';
import {
  addScreenshotListener,
  allowScreenCaptureAsync,
  preventScreenCaptureAsync,
  removeScreenshotListener,
} from 'expo-screen-capture';
import { getItemAsync as getItemAsyncSecureStore } from 'expo-secure-store';
import { useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { authenticationService } from 'services/authentication.service';
import {
  AuthenticationMethod,
  AuthenticationResult,
  type AuthenticationState,
} from 'types/authentication.types';

const ViewSeedPhraseScreen = () => {
  const t = useTranslations('wallet');
  const tSecurity = useTranslations('security');
  const tCommon = useTranslations('common');

  const [authState, setAuthState] = useState<AuthenticationState>({
    isAuthenticated: false,
    isAuthenticating: false,
    failedAttempts: 0,
    isLockedOut: false,
  });

  const [methodAttempts, setMethodAttempts] = useState({
    [AuthenticationMethod.PIN]: 0,
    [AuthenticationMethod.PASSWORD]: 0,
    [AuthenticationMethod.BIOMETRIC]: 0,
  });

  const [isSeedVisible, setIsSeedVisible] = useState(false);
  const [mnemonic, setMnemonic] = useState('');
  const [currentAuthMethod, setCurrentAuthMethod] =
    useState<AuthenticationMethod | null>(null);
  const [authError, setAuthError] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);

  const [showPinModal, setShowPinModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const [isAuthenticating, setIsAuthenticating] = useState(false);

  useEffect(() => {
    let subscription: any = null;

    if (isSeedVisible) {
      subscription = addScreenshotListener(() => {
        router.push('/(wallet)/wallet');
        Alert.alert(
          t('screenshotWarning.title'),
          t('screenshotWarning.message'),
          [{ text: t('screenshotWarning.button') }]
        );
      });
      preventScreenCaptureAsync();
    } else {
      allowScreenCaptureAsync();
    }

    return () => {
      if (subscription) {
        removeScreenshotListener(subscription);
      }
      allowScreenCaptureAsync();
    };
  }, [isSeedVisible, t, tCommon]);

  const handleModalCancel = useCallback(() => {
    if (!(isAuthenticating || authState.isAuthenticated)) {
      router.back();
    }
  }, [isAuthenticating, authState.isAuthenticated]);

  const handleAuthenticationSuccess = useCallback(
    async (method: AuthenticationMethod) => {
      try {
        setIsAuthenticating(true);

        setShowPinModal(false);
        setShowPasswordModal(false);
        setCurrentAuthMethod(null);
        setAuthError('');

        setAuthState((prev) => ({
          ...prev,
          isAuthenticated: true,
          authenticatedMethod: method,
          authenticatedAt: Date.now(),
          failedAttempts: 0,
          isLockedOut: false,
        }));

        const storedMnemonic = await getItemAsyncSecureStore(
          SecureStorageKeys.MNEMONIC
        );
        if (storedMnemonic) {
          setMnemonic(storedMnemonic);
          setIsSeedVisible(true);
          setIsAuthenticating(false);
        } else {
          setIsAuthenticating(false);
          Alert.alert(
            t('errors.generic'),
            'No seed phrase found. Please create a wallet first.',
            [{ text: tCommon('common.ok'), onPress: () => router.back() }]
          );
        }
      } catch (error) {
        setIsAuthenticating(false);
        Alert.alert(t('errors.generic'), 'Failed to load seed phrase.', [
          { text: tCommon('common.ok'), onPress: () => router.back() },
        ]);
      }
    },
    [t, tCommon]
  );

  const handleAuthenticationFailure = useCallback(
    async (method: AuthenticationMethod, error: string) => {
      try {
        const newMethodAttempts = methodAttempts[method] + 1;
        setMethodAttempts((prev) => ({
          ...prev,
          [method]: newMethodAttempts,
        }));

        setAuthState((prev) => ({
          ...prev,
          failedAttempts: prev.failedAttempts + 1,
          isAuthenticating: false,
        }));

        const config = await authenticationService.getConfig();
        const maxAttemptsPerMethod = AUTH_LIMITS.MAX_ATTEMPTS_PER_METHOD;

        // Determine next authentication method based on current failure
        if (method === AuthenticationMethod.BIOMETRIC) {
          // Fallback from biometric to PIN
          if (config.pin.isEnabled && config.pin.isConfigured) {
            setCurrentAuthMethod(AuthenticationMethod.PIN);
            setShowPinModal(true);
            setAuthError('');
          } else {
            // Fallback from biometric to password
            setCurrentAuthMethod(AuthenticationMethod.PASSWORD);
            setShowPasswordModal(true);
            setAuthError('');
          }
        } else if (method === AuthenticationMethod.PIN) {
          if (newMethodAttempts >= maxAttemptsPerMethod) {
            // Check if both biometric and PIN are enabled
            // If both are enabled and both have failed, deny access without password fallback
            const bothMethodsEnabled =
              config.biometric.isAvailable &&
              config.pin.isEnabled &&
              config.pin.isConfigured;

            if (bothMethodsEnabled) {
              // Both biometric and PIN are enabled and both have failed
              // Deny access without password fallback for enhanced security
              Alert.alert(
                tSecurity('authentication.accessDenied'),
                tSecurity('authentication.bothMethodsFailed'),
                [{ text: tCommon('common.ok'), onPress: () => router.back() }]
              );
            } else {
              // Only PIN is enabled (biometric not available), allow password fallback
              setTimeout(() => {
                setCurrentAuthMethod(AuthenticationMethod.PASSWORD);
                setShowPinModal(false);
                setAuthError('');
                // Delay password modal opening to prevent state interference
                setTimeout(() => {
                  setShowPasswordModal(true);
                }, 50);
              }, 100);
            }
          } else {
            setAuthError(error);
          }
        } else {
          // Password failed
          if (newMethodAttempts >= maxAttemptsPerMethod) {
            // Lock out after 2 password attempts
            Alert.alert(
              tSecurity('authentication.tooManyAttempts'),
              tSecurity('authentication.lockedOut'),
              [{ text: tCommon('common.ok'), onPress: () => router.back() }]
            );
          } else {
            // Show error but keep password modal open for retry
            setAuthError(error);
          }
        }
      } catch (_error) {
        router.back();
      }
    },
    [methodAttempts, tSecurity, tCommon]
  );

  // Initialize authentication service and determine authentication method
  const initializeAuthentication = useCallback(async () => {
    try {
      setAuthState((prev) => ({ ...prev, isAuthenticating: true }));

      await authenticationService.initialize();
      const config = await authenticationService.getConfig();

      // Check if user is locked out
      const isLockedOut = await authenticationService.isLockedOut();
      if (isLockedOut) {
        setAuthState((prev) => ({
          ...prev,
          isLockedOut: true,
          isAuthenticating: false,
        }));
        const remainingTime =
          await authenticationService.getRemainingLockoutTime();
        const minutes = Math.ceil(remainingTime / (60 * 1000));
        Alert.alert(
          tSecurity('seedPhrase.lockout.title'),
          tSecurity('seedPhrase.lockout.description', { minutes }),
          [{ text: tCommon('common.ok'), onPress: () => router.back() }]
        );
        return;
      }

      setIsInitialized(true);

      // Start with biometric authentication if available
      if (config.biometric.isAvailable) {
        await attemptBiometricAuthentication();
      } else if (config.pin.isEnabled && config.pin.isConfigured) {
        // Fallback to PIN if biometric not available
        setCurrentAuthMethod(AuthenticationMethod.PIN);
        setShowPinModal(true);
      } else {
        // Fallback to password
        setCurrentAuthMethod(AuthenticationMethod.PASSWORD);
        setShowPasswordModal(true);
      }
    } catch (error) {
      console.error('Failed to initialize authentication:', error);
      Alert.alert(
        tSecurity('authentication.authenticationFailed'),
        String(error),
        [{ text: tCommon('common.ok'), onPress: () => router.back() }]
      );
    } finally {
      setAuthState((prev) => ({ ...prev, isAuthenticating: false }));
    }
  }, [tSecurity, tCommon]);

  // Attempt biometric authentication
  const attemptBiometricAuthentication = useCallback(async () => {
    try {
      setAuthState((prev) => ({ ...prev, isAuthenticating: true }));
      setCurrentAuthMethod(AuthenticationMethod.BIOMETRIC);

      const result = await authenticationService.authenticateWithBiometric();

      if (result === AuthenticationResult.SUCCESS) {
        await handleAuthenticationSuccess(AuthenticationMethod.BIOMETRIC);
      } else if (result === AuthenticationResult.CANCELLED) {
        router.back();
      } else {
        // Fallback to PIN or password
        await handleAuthenticationFailure(
          AuthenticationMethod.BIOMETRIC,
          'Biometric authentication failed'
        );
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      await handleAuthenticationFailure(
        AuthenticationMethod.BIOMETRIC,
        String(error)
      );
    }
  }, [handleAuthenticationSuccess, handleAuthenticationFailure]);

  // Initialize authentication service and start authentication flow
  useEffect(() => {
    initializeAuthentication();
  }, [initializeAuthentication]);

  // Show loading state while initializing
  if (!isInitialized && authState.isAuthenticating) {
    return (
      <PageLayout
        contentGap={4}
        contentMarginTop={8}
        title={t('viewSeedPhrase.title')}
      >
        <Box align="center" gap={4}>
          <Image
            contentFit="contain"
            source={Security}
            style={{ width: 72, height: 72 }}
          />
          <Title size="lg" textAlign="center">
            {t('viewSeedPhrase.authenticationRequired')}
          </Title>
          <Text color="grey.600" textAlign="center">
            {t('viewSeedPhrase.authenticating')}
          </Text>
        </Box>
      </PageLayout>
    );
  }

  return (
    <>
      <PageLayout
        buttonProps={{
          variant: 'muted',
        }}
        buttonText={isSeedVisible ? tCommon('common.back') : undefined}
        contentGap={4}
        contentMarginTop={2}
        description={t('viewSeedPhrase.description')}
        onButtonPress={() => router.back()}
        title={t('viewSeedPhrase.title')}
      >
        <UiAlert message="wallet:viewSeedPhrase.alert" type="warning" />
        <SeedPhrase
          isBlurred={true}
          isRevealed={isSeedVisible}
          mnemonic={mnemonic || ''}
          showEyeIcon={false}
        />
      </PageLayout>

      <PinAuthenticationModal
        description={tSecurity('pin.description')}
        failedAttempts={authState.failedAttempts}
        isLoading={authState.isAuthenticating}
        isVisible={showPinModal}
        onCancel={handleModalCancel}
        onFailure={handleAuthenticationFailure}
        onSuccess={handleAuthenticationSuccess}
        pinError={
          currentAuthMethod === 'pin' && authError ? authError : undefined
        }
        title={tSecurity('pin.title')}
      />

      <PasswordAuthenticationModal
        description={tSecurity('password.description')}
        failedAttempts={authState.failedAttempts}
        isLoading={authState.isAuthenticating}
        isVisible={showPasswordModal}
        onCancel={handleModalCancel}
        onFailure={handleAuthenticationFailure}
        onSuccess={handleAuthenticationSuccess}
        passwordError={
          currentAuthMethod === 'password' && authError ? authError : undefined
        }
        title={tSecurity('password.title')}
      />
    </>
  );
};

export default ViewSeedPhraseScreen;
