import { useTranslations } from '@hooks';
import { Box, PageLayout, Text, Title } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { memo } from 'react';
import { Trans } from 'react-i18next';
import { StyleSheet } from 'react-native-unistyles';

const WalletCreationSuccessScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'creationSuccess' });

  return (
    <PageLayout
      bgColor="grey.900"
      buttonBlurOptions={{
        tint: 'dark',
      }}
      buttonGradientVariant="dark"
      buttonProps={{
        href: '/(wallet)/wallet',
        hrefType: 'replace',
      }}
      buttonText={t('button')}
      headerBlurOptions={{
        intensity: 25,
        tint: 'systemUltraThinMaterialDark',
      }}
      noBackButton
      rightComponentContainerProps={{
        width: 'auto',
        paddingRight: 16,
      }}
      statusBarOptions={{
        barStyle: 'light-content',
      }}
      withBackgroundGradient
    >
      <Image
        contentFit="contain"
        source={require('@assets/wallet-image.png')}
        style={styles.image}
      />
      <Box gap={2} mt={4}>
        <Title color="white">
          {t('title', 'Wallet Created Successfully!')}
        </Title>
        <Text color="grey.100" size="md">
          <Trans
            components={{ bold: <Text color="white" weight="semibold" /> }}
            i18nKey="wallet:creationSuccess.paragraphs"
          />
        </Text>
      </Box>
    </PageLayout>
  );
};

export default memo(WalletCreationSuccessScreen);

const styles = StyleSheet.create(() => ({
  image: {
    width: 370,
    height: 370,
    alignSelf: 'center',
  },
}));
