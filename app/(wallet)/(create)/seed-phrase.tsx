import 'react-native-get-random-values';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import { useTranslations } from '@hooks';
import useBreezSdkStore from '@store/breez-sdk.store';
import useNotificationStore from '@store/notification.store';
import useWalletStore from '@store/wallet.store';
import {
  Alert,
  BackgroundProcessWarningBottomSheet,
  Box,
  PageLayout,
  Text,
} from '@ui';
import { createLogger } from '@utils/logger';
import { WALLET_VALIDATION } from '@utils/wallet/constants';
import { useWalletAddressIntegration } from '@utils/wallet/wallet-integration';
import * as bip39 from 'bip39';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import {
  addScreenshotListener,
  usePreventScreenCapture,
} from 'expo-screen-capture';
import { setItemAsync } from 'expo-secure-store';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { AppState, Keyboard, Alert as RNAlert, TextInput } from 'react-native';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const inactiveOrBackgroundRegex = /inactive|background/i;
const seedPhraseLogger = createLogger('SeedPhraseScreen');

const SeedPhraseScreen = () => {
  usePreventScreenCapture();
  const t = useTranslations('wallet', { keyPrefix: 'saveSeedPhrase' });
  const tCommon = useTranslations('common');
  const tWallet = useTranslations('wallet');

  const { selectedWallet, setMnemonic } = useWalletStore();
  const { showModal, hideModal } = useNotificationStore();
  const overlayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const continuedRef = useRef(false);

  const [showBackgroundWarning, setShowBackgroundWarning] = useState(false);
  const creationUnsubRef = useRef<null | (() => void)>(null);
  const { isLoading: isBreezLoading } = useBreezSdkStore();
  const { storeWalletAddress } = useWalletAddressIntegration();

  const [isCreating, setIsCreating] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);

  const [phrases, setPhrases] = useState<string[]>([]);
  const [isConfirmation, setIsConfirmation] = useState(false);
  const [userMnemonic, setUserMnemonic] = useState<string[]>(
    new Array(WALLET_VALIDATION.MNEMONIC_WORD_COUNT).fill('')
  );
  // const [isMnemonicCorrect, setIsMnemonicCorrect] = useState(false);
  const [isBlurred, setIsBlurred] = useState(false);

  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    const generateMnemonic = () => {
      try {
        const mnemonic = bip39.generateMnemonic();
        const words = mnemonic.split(' ');
        setPhrases(words);
        setMnemonic(mnemonic);
        seedPhraseLogger.success('Mnemonic generated successfully', {
          wordCount: words.length,
        });
      } catch (error) {
        seedPhraseLogger.error('Failed to generate mnemonic', { error });
        RNAlert.alert(t('error'), t('seedPhraseNotGenerated'));
      }
    };

    if (selectedWallet === 'private' && phrases.length === 0) {
      generateMnemonic();
    }
  }, [selectedWallet, phrases.length, setMnemonic, t]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (inactiveOrBackgroundRegex.test(nextAppState)) {
        setIsBlurred(true);
      } else {
        setIsBlurred(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    const handleScreenshot = () => {
      RNAlert.alert(
        t('screenshotDetected.title'),
        t('screenshotDetected.message'),
        [{ text: t('screenshotDetected.ok') }]
      );
    };

    const subscription = addScreenshotListener(handleScreenshot);
    return () => {
      if (subscription && typeof subscription.remove === 'function') {
        subscription.remove();
      }
    };
  }, [t]);

  // Cleanup overlay timer and modal on unmount
  useEffect(() => {
    return () => {
      if (overlayTimerRef.current) {
        clearTimeout(overlayTimerRef.current);
        overlayTimerRef.current = null;
      }
      hideModal();
    };
  }, [hideModal]);

  const validateUserMnemonic = useCallback(() => {
    const userPhrase = userMnemonic.join(' ').trim();
    const originalPhrase = phrases.join(' ').trim();
    const isCorrect = userPhrase === originalPhrase;
    // setIsMnemonicCorrect(isCorrect);
    return isCorrect;
  }, [userMnemonic, phrases]);

  const handleCreateWallet = useCallback(async () => {
    if (!validateUserMnemonic()) {
      RNAlert.alert(t('incorrectPhrase.title'), t('incorrectPhrase.message'));
      return;
    }

    try {
      seedPhraseLogger.operation(
        'Creating private wallet with confirmed mnemonic'
      );

      const mnemonic = phrases.join(' ');

      setIsCreating(true);
      setCreationError(null);
      continuedRef.current = false;

      hideModal();
      showModal(tWallet('saveSeedPhrase.creatingWallet'));
      if (overlayTimerRef.current) clearTimeout(overlayTimerRef.current);

      // Responsive logic: whichever happens first wins
      // 1) Listen for background status to become COMPLETE to navigate immediately
      creationUnsubRef.current?.();
      creationUnsubRef.current = useBreezSdkStore.subscribe(
        (s) => s.backgroundCreationStatus,
        (status) => {
          if (status === 'COMPLETE' && !continuedRef.current) {
            const { generatedAddress } = useBreezSdkStore
              .getState()
              .getBackgroundCreationStatus();
            if (generatedAddress) {
              seedPhraseLogger.debug(
                'Creation finished quickly; navigating to success'
              );
              creationUnsubRef.current?.();
              if (overlayTimerRef.current) {
                clearTimeout(overlayTimerRef.current);
                overlayTimerRef.current = null;
              }
              hideModal();
              router.replace('/(wallet)/(create)/wallet-creation-success');
            }
          }
        }
      );

      // 2) 10-second timeout fallback to show "taking longer" only if not COMPLETE yet
      overlayTimerRef.current = setTimeout(() => {
        const { status } = useBreezSdkStore
          .getState()
          .getBackgroundCreationStatus();
        if (status === 'COMPLETE' || continuedRef.current) return;

        seedPhraseLogger.debug(
          'Creation taking longer than 10s; showing continue option'
        );
        showModal(tWallet('creationOverlay.takingLonger'), {
          bottomButtonLabel: tCommon('common.continue'),
          onBottomButtonPress: () => {
            // Show a background process warning before navigating using inline bottom sheet
            hideModal();
            setShowBackgroundWarning(true);
          },
        });
      }, 10_000);

      await setItemAsync(SecureStorageKeys.MNEMONIC, mnemonic);

      const { startBackgroundWalletCreation } = useBreezSdkStore.getState();

      seedPhraseLogger.operation('Starting background wallet creation');

      // Start background creation and wait for initial setup to complete
      try {
        await startBackgroundWalletCreation(mnemonic, {
          storeWalletAddress: async (
            address: string,
            type?: string,
            label?: string
          ) => {
            try {
              await storeWalletAddress(address, type as 'liquid', label);
              return true;
            } catch (error) {
              seedPhraseLogger.warn('API address storage failed', error);
              return false;
            }
          },
        });

        seedPhraseLogger.success(
          'Background wallet creation started successfully, navigating to success screen'
        );
      } catch (_creationError) {
        seedPhraseLogger.error(
          'Failed to start background wallet creation',
          creationError
        );
        throw new Error('Failed to start wallet creation process');
      }

      setIsCreating(false);

      // Do not navigate here; the 10-second timer callback decides navigation or showing the "taking longer" modal.
      seedPhraseLogger.debug('Creation initiated; awaiting 10s timer decision');
    } catch (error) {
      seedPhraseLogger.error('Failed to create private wallet', { error });
      setIsCreating(false);

      // Clear timer and modal on error
      if (overlayTimerRef.current) {
        clearTimeout(overlayTimerRef.current);
        overlayTimerRef.current = null;
      }
      hideModal();

      setCreationError(
        error instanceof Error ? error.message : 'Unknown error'
      );
      RNAlert.alert(t('error'), t('walletCreationFailed'));
    }
  }, [
    validateUserMnemonic,
    phrases,
    t,
    storeWalletAddress,
    hideModal,
    showModal,
    tCommon,
    creationError,
    tWallet,
  ]);

  const handleContinue = useCallback(() => {
    if (phrases.length === 0) {
      RNAlert.alert(t('error'), t('seedPhraseNotGenerated'));
      return;
    }
    setIsConfirmation(true);
  }, [phrases.length, t]);

  const handleInputChange = useCallback(
    (text: string, index: number) => {
      validateUserMnemonic();
      setUserMnemonic((prevUserMnemonic) => {
        const oldWord = prevUserMnemonic[index].trim() || '';
        if (text.length > oldWord.length + 1) {
          return prevUserMnemonic;
        }
        const newUserMnemonic = [...prevUserMnemonic];
        newUserMnemonic[index] = text.trim();
        return newUserMnemonic;
      });
    },
    [validateUserMnemonic]
  );

  const half = Math.ceil(phrases.length / 2);
  const firstHalf = phrases.slice(0, half);
  const secondHalf = phrases.slice(half);

  const focusNextInput = useCallback((index: number) => {
    if (inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    } else {
      Keyboard.dismiss();
    }
  }, []);

  if (isCreating || isBreezLoading) {
    // Global overlay is handling the UI; render an empty layout to keep structure
    return <PageLayout />;
  }

  if (creationError) {
    return (
      <PageLayout
        buttonProps={{
          onPress: () => {
            setCreationError(null);
            router.back();
          },
        }}
        buttonText={t('tryAgain')}
        title={t('walletCreationFailed')}
      >
        <Box align="center" flex={1} justify="center" p={6}>
          <Text color="grey.600" textAlign="center">
            {creationError}
          </Text>
        </Box>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      buttonProps={{
        disabled: !isConfirmation && phrases.length === 0,
      }}
      buttonText={
        isConfirmation ? tCommon('common.continue') : t('savedOffline')
      }
      contentGap={2}
      contentMarginTop={4}
      description={isConfirmation ? t('confirmDescription') : t('description')}
      onButtonPress={isConfirmation ? handleCreateWallet : handleContinue}
      title={isConfirmation ? t('confirmTitle') : t('title')}
    >
      {isBlurred && (
        <BlurView intensity={100} style={StyleSheet.absoluteFillObject} />
      )}

      <Alert
        // biome-ignore lint/complexity/noUselessFragments: .
        message={<>{isConfirmation ? t('alertConfirmation') : t('alert')}</>}
        type={isConfirmation ? 'info' : 'warning'}
      />

      <Box flex={1}>
        {isConfirmation ? (
          <Box
            bgColor="grey.50"
            borderColor="grey.100"
            borderWidth={1}
            flexDirection="row"
            gap={4}
            mt={2}
            p={6}
            radius={24}
          >
            <Box flex={1} gap={4}>
              {Array.from({ length: half }).map((_, index) => (
                <RenderPhraseItem
                  asInput
                  focusNextInput={focusNextInput}
                  handleInputChange={handleInputChange}
                  index={index}
                  key={index}
                  phrase={phrases[index]}
                  setRef={(el) => {
                    inputRefs.current[index] = el;
                  }}
                  userMnemonic={userMnemonic}
                />
              ))}
            </Box>
            <Box flex={1} gap={4}>
              {Array.from({ length: secondHalf.length }).map((_, i) => {
                const index = i + half;

                return (
                  <RenderPhraseItem
                    asInput
                    focusNextInput={focusNextInput}
                    handleInputChange={handleInputChange}
                    index={index}
                    key={index}
                    phrase={phrases[index]}
                    setRef={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    userMnemonic={userMnemonic}
                  />
                );
              })}
            </Box>
          </Box>
        ) : (
          <Box
            bgColor="grey.50"
            borderColor="grey.100"
            borderWidth={1}
            flexDirection="row"
            gap={4}
            mt={2}
            p={6}
            radius={24}
          >
            <Box flex={1} gap={4}>
              {firstHalf.map((phrase, index) => (
                <RenderPhraseItem index={index} key={phrase} phrase={phrase} />
              ))}
            </Box>
            <Box flex={1} gap={4}>
              {secondHalf.map((phrase, index) => (
                <RenderPhraseItem
                  index={index + half}
                  key={phrase}
                  phrase={phrase}
                />
              ))}
            </Box>
          </Box>
        )}
      </Box>

      <BackgroundProcessWarningBottomSheet
        isVisible={showBackgroundWarning}
        onConfirm={() => {
          setShowBackgroundWarning(false);
          creationUnsubRef.current?.();
          hideModal();
          router.replace('/(wallet)/wallet');
        }}
        onDismiss={() => setShowBackgroundWarning(false)}
      />
    </PageLayout>
  );
};

function RenderPhraseItem({
  asInput,
  phrase,
  index,
  handleInputChange,
  focusNextInput,
  userMnemonic,
  setRef,
}: {
  asInput?: boolean;
  phrase: string;
  index: number;
  handleInputChange?: (text: string, index: number) => void;
  focusNextInput?: (index: number) => void;
  userMnemonic?: string[];
  setRef?: (el: TextInput | null) => void;
}) {
  const t = useTranslations('wallet', { keyPrefix: 'saveSeedPhrase' });
  const { theme } = useUnistyles();

  return (
    <Box align="center" flexDirection="row" gap={2} key={phrase}>
      <Text color="grey.200" size="lg" w={20}>
        {index + 1}
      </Text>
      {asInput ? (
        <TextInput
          autoCapitalize="none"
          blurOnSubmit={false}
          contextMenuHidden
          onChangeText={(text) => handleInputChange?.(text, index)}
          onSubmitEditing={() => focusNextInput?.(index)}
          placeholder={t('placeholder')}
          placeholderTextColor={theme.colors.grey[300]}
          ref={(el) => {
            setRef?.(el);
          }}
          returnKeyType={index === 11 ? 'done' : 'next'}
          style={{
            flex: 1,
            fontSize: 18,
            lineHeight: 24,
            fontWeight: '500',
          }}
          value={userMnemonic?.[index]}
        />
      ) : (
        <Text size="lg" weight="medium">
          {phrase}
        </Text>
      )}
    </Box>
  );
}

export default memo(SeedPhraseScreen);
