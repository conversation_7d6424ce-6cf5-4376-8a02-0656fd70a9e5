// biome-ignore-all: .
import 'react-native-get-random-values';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import { useTranslations, useValidator } from '@hooks';
import useBreezSdkStore from '@store/breez-sdk.store';
import useNotificationStore from '@store/notification.store';
import useWalletStore from '@store/wallet.store';
import {
  Alert,
  BackgroundProcessWarningBottomSheet,
  Box,
  PageLayout,
  Text,
  TextInput,
  type TextInputType,
} from '@ui';
import { passwordConfirmWalletSchema, passwordWalletSchema } from '@utils';
import { createLogger } from '@utils/logger';
import { DEFAULT_NETWORK } from '@utils/wallet/constants';
import {
  useRecoveryBlobIntegration,
  useWalletAddressIntegration,
} from '@utils/wallet/wallet-integration';
import * as bip39 from 'bip39';
import { PasswordBackupConfirmationDialog } from 'components/ui/dialog/password-backup-confirmation-dialog';
import { VerificationList } from 'components/wallet/create-wallet/verification-list';
import { router } from 'expo-router';
import { setItemAsync } from 'expo-secure-store';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { handleWalletCreationError } from 'utils/async-utils';

const cloudWalletLogger = createLogger('CloudBackupPasswordScreen');

const CloudBackupPasswordScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'private.secure' });
  const tCommon = useTranslations('common');
  const tWallet = useTranslations('wallet');

  const { setMnemonic, selectedWallet } = useWalletStore();
  const {
    initialize,
    startBackgroundWalletCreation,
    isInitialized,
    isLoading,
    clearError,
  } = useBreezSdkStore();

  const { storeRecoveryBlob } = useRecoveryBlobIntegration();
  const { storeWalletAddress } = useWalletAddressIntegration();

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);
  const [confirmPasswordTouched, setConfirmPasswordTouched] = useState(false);
  const [isCreatingWallet, setIsCreatingWallet] = useState(false);
  const [walletCreationError, setWalletCreationError] = useState<string | null>(
    null
  );

  const confirmPasswordRef = useRef<TextInputType>(null);

  // Overlay/Modal handling via notification store
  const showOverlayRef = useRef<NodeJS.Timeout | null>(null);
  const continuedRef = useRef(false);
  const creationUnsubRef = useRef<null | (() => void)>(null);
  const { showModal, hideModal } = useNotificationStore();

  const [showBackgroundWarning, setShowBackgroundWarning] = useState(false);

  useEffect(() => {
    const initializeBreezSdk = async () => {
      if (!isInitialized) {
        await initialize();
      }
    };

    initializeBreezSdk();
  }, [isInitialized, initialize]);

  useEffect(() => {
    clearError();
  }, [clearError]);

  const [passwordValidatorMessage, validatePassword] =
    useValidator(passwordWalletSchema);

  const [passwordConfirmValidatorMessage, validateConfirmPassword] =
    useValidator(passwordConfirmWalletSchema);

  const handlePasswordChange = useCallback(
    (newPassword: string) => {
      setPassword(newPassword);
      if (!passwordTouched) {
        setPasswordTouched(true);
      }
      validatePassword(newPassword);
      validateConfirmPassword({
        password: newPassword,
        passwordConfirm: confirmPassword,
      });
    },
    [
      confirmPassword,
      validateConfirmPassword,
      validatePassword,
      passwordTouched,
    ]
  );

  const handleConfirmPasswordChange = useCallback(
    (newConfirmPassword: string) => {
      setConfirmPassword(newConfirmPassword);
      if (!confirmPasswordTouched) {
        setConfirmPasswordTouched(true);
      }
      validateConfirmPassword({
        password,
        passwordConfirm: newConfirmPassword,
      });
    },
    [password, validateConfirmPassword, confirmPasswordTouched]
  );
  // Cleanup overlay timer on unmount
  useEffect(() => {
    return () => {
      if (showOverlayRef.current) {
        clearTimeout(showOverlayRef.current);
        showOverlayRef.current = null;
      }
      hideModal();
    };
  }, [hideModal]);

  const canGoToNextStep = useMemo(() => {
    return (
      !(passwordValidatorMessage || passwordConfirmValidatorMessage) &&
      password &&
      confirmPassword
    );
  }, [
    passwordValidatorMessage,
    passwordConfirmValidatorMessage,
    password,
    confirmPassword,
  ]);

  const onContinue = () => {
    if (!canGoToNextStep || isCreatingWallet) {
      return;
    }

    setWalletCreationError(null);
    setIsDialogVisible(true);
  };

  const onConfirm = async () => {
    setIsCreatingWallet(true);
    setIsDialogVisible(false);
    setWalletCreationError(null);
    continuedRef.current = false; // Reset continuation flag

    // Start overlay
    hideModal();
    showModal(tWallet('saveSeedPhrase.creatingWallet'));
    if (showOverlayRef.current) clearTimeout(showOverlayRef.current);

    // Responsive logic: subscribe for quick completion OR show fallback after 10s
    // 1) Listen for background creation to complete fast
    creationUnsubRef?.current?.();
    // create a local ref to store unsubscribe
    const unsubscribe = useBreezSdkStore.subscribe(
      (s) => s.backgroundCreationStatus,
      (status) => {
        if (status === 'COMPLETE' && !continuedRef.current) {
          const { generatedAddress } = useBreezSdkStore
            .getState()
            .getBackgroundCreationStatus();
          if (generatedAddress) {
            unsubscribe?.();
            if (showOverlayRef.current) {
              clearTimeout(showOverlayRef.current);
              showOverlayRef.current = null;
            }
            hideModal();
            router.replace('/(wallet)/(create)/wallet-creation-success');
          }
        }
      }
    );
    creationUnsubRef.current = unsubscribe;

    // 2) 10-second fallback to show "taking longer" only if not COMPLETE yet
    showOverlayRef.current = setTimeout(() => {
      const { status } = useBreezSdkStore
        .getState()
        .getBackgroundCreationStatus();
      if (status === 'COMPLETE' || continuedRef.current) return;

      showModal(tWallet('creationOverlay.takingLonger'), {
        bottomButtonLabel: tCommon('common.continue'),
        onBottomButtonPress: () => {
          continuedRef.current = true;
          hideModal();
          setShowBackgroundWarning(true);
        },
      });
    }, 10_000);

    try {
      clearError();
      const mnemonic = bip39.generateMnemonic();

      setMnemonic(mnemonic);

      try {
        await setItemAsync(SecureStorageKeys.MNEMONIC, mnemonic);
      } catch {
        throw new Error(t('errors.walletCreation.secureStorageFailed'));
      }

      try {
        const recoveryBlobResult = await storeRecoveryBlob(
          mnemonic,
          password,
          selectedWallet,
          DEFAULT_NETWORK
        );

        if (recoveryBlobResult) {
          cloudWalletLogger.success(
            'Recovery blob sent to backend successfully'
          );
        } else {
          cloudWalletLogger.warn('Failed to send recovery blob to backend');
        }
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: debug logging
        console.error('Error sending recovery blob to backend', error);
      }

      cloudWalletLogger.operation('Starting background wallet creation');

      // Start background creation and wait for initial setup to complete
      try {
        await startBackgroundWalletCreation(mnemonic, {
          storeWalletAddress: async (
            address: string,
            type?: string,
            label?: string
          ) => {
            try {
              await storeWalletAddress(address, type as 'liquid', label);
              return true;
            } catch (error) {
              cloudWalletLogger.warn('API address storage failed', error);
              return false;
            }
          },
        });

        cloudWalletLogger.success(
          'Background wallet creation started successfully, navigating to success screen'
        );
      } catch (creationError) {
        cloudWalletLogger.error(
          'Failed to start background wallet creation',
          creationError
        );
        throw new Error('Failed to start wallet creation process');
      }

      // Do not navigate here; the 10-second timer callback decides navigation or showing the "taking longer" modal.
      cloudWalletLogger.debug(
        'Creation initiated; awaiting 10s timer decision'
      );
    } catch (error) {
      // Clear timer and modal on error
      if (showOverlayRef.current) {
        clearTimeout(showOverlayRef.current);
        showOverlayRef.current = null;
      }
      hideModal();

      handleWalletCreationError(
        error,
        'CloudBackupPasswordScreen',
        setWalletCreationError
      );
    } finally {
      setIsCreatingWallet(false);
    }
  };

  const description = useMemo(
    () => (
      <Trans
        components={{ bold: <Text weight="semibold" /> }}
        i18nKey="wallet:private.secure.description"
      />
    ),
    []
  );

  const alert = useMemo(
    () => (
      <Trans
        components={{ bold: <Text color="yellow" weight="semibold" /> }}
        i18nKey="wallet:private.secure.alert"
      />
    ),
    []
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: !canGoToNextStep || isCreatingWallet,
        loading: isCreatingWallet || isLoading,
      }}
      buttonText={
        isCreatingWallet ? t('creatingWallet') : tCommon('common.continue')
      }
      contentGap={2}
      contentMarginTop={2}
      description={description}
      onButtonPress={onContinue}
      title={t('title')}
    >
      <Box gap={5}>
        <Alert message={alert} type="warning" />

        {walletCreationError && (
          <Alert message={walletCreationError} type="error" />
        )}

        <TextInput
          errorMessage={passwordTouched ? passwordValidatorMessage : undefined}
          isPassword
          label={t('password')}
          onChangeText={handlePasswordChange}
          onSubmitEditing={() => confirmPasswordRef.current?.focus()}
          placeholder={t('passwordPlaceholder')}
          returnKeyType="next"
          value={password}
        />
        <TextInput
          errorMessage={
            confirmPasswordTouched ? passwordConfirmValidatorMessage : undefined
          }
          isPassword
          label={t('confirmPassword')}
          onChangeText={handleConfirmPasswordChange}
          onSubmitEditing={onContinue}
          placeholder={t('passwordPlaceholder')}
          ref={confirmPasswordRef}
          returnKeyType="done"
          value={confirmPassword}
        />

        <VerificationList password={password} />
      </Box>

      <BackgroundProcessWarningBottomSheet
        isVisible={showBackgroundWarning}
        onConfirm={() => {
          setShowBackgroundWarning(false);
          creationUnsubRef.current?.();
          hideModal();
          router.replace('/(wallet)/wallet');
        }}
        onDismiss={() => setShowBackgroundWarning(false)}
      />

      <PasswordBackupConfirmationDialog
        isVisible={isDialogVisible}
        onCancel={() => setIsDialogVisible(false)}
        onConfirm={onConfirm}
      />
    </PageLayout>
  );
};

export default memo(CloudBackupPasswordScreen);
