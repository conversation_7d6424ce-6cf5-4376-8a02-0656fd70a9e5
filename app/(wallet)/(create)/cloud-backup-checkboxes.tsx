import { useTranslations } from '@hooks';
import useWalletStore from '@store/wallet.store';
import { Box, PageLayout, Text } from '@ui';
import { NewCheckbox } from 'components/ui/checkboxes/new-checkbox';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { Trans } from 'react-i18next';

const WalletScreen = () => {
  const tCommon = useTranslations('common');
  const t = useTranslations('wallet', {
    keyPrefix: 'cloud',
  });

  const checkboxIds = useMemo(() => ['1', '2', '3'], []);

  const { checkedCheckboxes, setCheckboxSelected, toggleCheckbox } =
    useWalletStore();

  const handleUnselectCheckboxes = useCallback(() => {
    for (const checkbox of checkboxIds) {
      setCheckboxSelected(checkbox, false);
    }
  }, [setCheckboxSelected, checkboxIds]);

  const areAllCheckboxesChecked = checkedCheckboxes.length === 3;

  useEffect(() => {
    handleUnselectCheckboxes();
  }, [handleUnselectCheckboxes]);

  return (
    <PageLayout
      buttonProps={{
        disabled: !areAllCheckboxesChecked,
        href: '/(wallet)/(create)/cloud-backup-password',
        hrefType: 'replace',
      }}
      buttonText={tCommon('common.continue')}
      contentGap={4}
      contentMarginTop={4}
      onBackPress={handleUnselectCheckboxes}
      title={t('title')}
    >
      <Box gap={4}>
        {checkboxIds.map((checkbox) => (
          <NewCheckbox
            isChecked={checkedCheckboxes.includes(checkbox.toString())}
            key={checkbox.toString()}
            label={
              <Text>
                <Trans
                  components={{
                    bold: (
                      <Text
                        color="red"
                        key={checkbox.toString()}
                        weight="medium"
                      />
                    ),
                  }}
                  i18nKey={`wallet:cloud.checkboxes.${checkbox}`}
                />
              </Text>
            }
            onPress={() => toggleCheckbox(checkbox)}
          />
        ))}
      </Box>
    </PageLayout>
  );
};

export default memo(WalletScreen);
