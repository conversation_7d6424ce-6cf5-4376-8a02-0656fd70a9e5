import { useTranslations } from '@hooks';
import useWalletStore, { type WalletType } from '@store/wallet.store';
import { PageLayout, RadioCard, RadioGroup, Text } from '@ui';
import { memo, useCallback } from 'react';
import { Trans } from 'react-i18next';

const wallets = [
  {
    id: 'cloud',
    title: 'cloud.title',
    description: 'selectWalletType.cloud.description',
  },
  {
    id: 'private',
    title: 'private.title',
    description: 'selectWalletType.private.description',
  },
];

const WalletScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'selectWalletType' });
  const tCommon = useTranslations('common');

  const { selectedWallet, setSelectedWallet } = useWalletStore();

  const handleSelectWallet = useCallback(
    (walletId: string) => {
      setSelectedWallet(walletId as WalletType);
    },
    [setSelectedWallet]
  );

  return (
    <PageLayout
      buttonProps={{
        href:
          selectedWallet === 'cloud'
            ? '/(wallet)/(create)/cloud-backup-checkboxes'
            : '/(wallet)/(create)/private-wallet-checkboxes',
        hrefType: 'replace',
      }}
      buttonText={tCommon('common.continue')}
      contentMarginTop={4}
      description={t('description')}
      title={t('title')}
    >
      <RadioGroup onValueChange={handleSelectWallet} value={selectedWallet}>
        {wallets.map((wallet) => (
          <RadioCard
            icon={null}
            key={wallet.id}
            title={t(wallet.title)}
            value={wallet.id}
          >
            <Text color="grey.600" size="sm">
              <Trans
                components={{
                  bold: <Text color="grey.600" size="sm" weight="semibold" />,
                }}
                i18nKey={`wallet:${wallet.description}`}
              />
            </Text>
          </RadioCard>
        ))}
      </RadioGroup>
    </PageLayout>
  );
};

export default memo(WalletScreen);
