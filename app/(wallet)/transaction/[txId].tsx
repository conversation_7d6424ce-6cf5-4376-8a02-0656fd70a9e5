import { useTranslations } from '@hooks';
import useBreezSdkStore from '@store/breez-sdk.store';
import { Box, CopyButton, PageLayout, Text } from '@ui';
import { useLocalSearchParams } from 'expo-router';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { useMemo } from 'react';
import dayjs from 'utils/dayjs-config';

export default function TransactionDetailScreen() {
  const t = useTranslations('wallet');
  const { txId } = useLocalSearchParams<{ txId: string }>();
  const { recentPayments } = useBreezSdkStore();
  const { convertSatoshis } = useExchangeRate();

  const transaction = useMemo(() => {
    return recentPayments.find((payment) => payment.txId === txId);
  }, [recentPayments, txId]);

  if (!transaction) {
    return (
      <PageLayout title={t('transactions.title')}>
        <Box align="center" flex={1} justify="center" p={4}>
          <Text color="grey.600" size="lg" textAlign="center">
            {t('transactions.notFound')}
          </Text>
        </Box>
      </PageLayout>
    );
  }

  const isReceived = transaction.paymentType === 'receive';
  const amount = transaction.amountSat / 100_000_000;
  const amountPLN = convertSatoshis(transaction.amountSat, 'PLN');
  const timestamp = transaction.timestamp
    ? dayjs(transaction.timestamp * 1000)
    : dayjs();

  return (
    <PageLayout
      title={isReceived ? t('transactions.received') : t('transactions.sent')}
    >
      <Box gap={6}>
        <Box bgColor="white" p={6} radius={12}>
          <Box align="center" gap={2}>
            <Text color={isReceived ? 'green' : 'red'} size="xxl" weight="bold">
              {isReceived ? '+' : '-'}
              {amount.toFixed(8)} BTC
            </Text>
            <Text color="grey.600" size="lg">
              ≈ {amountPLN.toFixed(2)} PLN
            </Text>
          </Box>
        </Box>

        <Box bgColor="white" gap={4} p={6} radius={12}>
          <Box flexDirection="row" justify="between">
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.status')}
            </Text>
            <Text
              color={
                transaction.status === 'complete'
                  ? 'green'
                  : transaction.status === 'pending'
                    ? 'orange'
                    : 'red'
              }
              size="sm"
              weight="medium"
            >
              {transaction.status === 'complete'
                ? t('transactions.completed')
                : transaction.status === 'pending'
                  ? t('transactions.pending')
                  : t('transactions.failed')}
            </Text>
          </Box>

          <Box flexDirection="row" justify="between">
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.type')}
            </Text>
            <Text size="sm">
              {isReceived ? t('transactions.received') : t('transactions.sent')}
            </Text>
          </Box>

          <Box flexDirection="row" justify="between">
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.date')}
            </Text>
            <Text size="sm">{timestamp.format('MMM DD, YYYY HH:mm')}</Text>
          </Box>

          <Box flexDirection="row" justify="between">
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.timeAgo')}
            </Text>
            <Text size="sm">{timestamp.fromNow()}</Text>
          </Box>
        </Box>

        <Box bgColor="white" gap={4} p={6} radius={12}>
          <Text color="grey.600" size="lg" weight="medium">
            {t('transactions.details.transactionDetails')}
          </Text>

          <Box gap={3}>
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.transactionId')}
            </Text>
            <Box align="center" flexDirection="row" gap={3}>
              <Text color="grey.600" flex={1} numberOfLines={1} size="sm">
                {transaction.txId}
              </Text>
              <CopyButton textToCopy={transaction.txId ?? ''} />
            </Box>
          </Box>

          <Box gap={3}>
            <Text color="grey.700" size="sm" weight="medium">
              {t('transactions.details.amountSatoshis')}
            </Text>
            <Text color="grey.600" size="sm">
              {transaction.amountSat.toLocaleString()} sats
            </Text>
          </Box>

          {transaction.feesSat && (
            <Box gap={3}>
              <Text color="grey.700" size="sm" weight="medium">
                {t('transactions.details.networkFee')}
              </Text>
              <Text color="grey.600" size="sm">
                {transaction.feesSat.toLocaleString()} sats
              </Text>
            </Box>
          )}
        </Box>
      </Box>
    </PageLayout>
  );
}
