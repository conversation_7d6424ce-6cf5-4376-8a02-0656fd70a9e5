import { useTranslations } from '@hooks';
import useAuthStore from '@store/auth.store';
import useBreezSdkStore from '@store/breez-sdk.store';
import { Box, PageLayout, Text } from '@ui';
import { transactionsLogger } from '@utils/logger';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';
import { TransactionItem } from 'components/wallet/transaction-item';
import { useCallback, useEffect, useState } from 'react';
import { AuthState } from 'types/auth.types';

export default function TransactionsScreen() {
  const t = useTranslations('wallet');
  const { authenticated } = useAuthStore();
  const isAuthenticated = authenticated === AuthState.Authenticated;

  const { actions: walletActions } = useSimpleWallet();
  const { recentPayments, isConnected, loadPayments } = useBreezSdkStore();

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);

  const handleRefresh = useCallback(async () => {
    try {
      await walletActions.refresh();

      if (isConnected) {
        const payments = await loadPayments({ limit: 50, offset: 0 });
        setHasMoreData(payments.length === 50);
      }
    } catch (error) {
      transactionsLogger.error('Failed to refresh transactions', error);
    }
  }, [isConnected, loadPayments, walletActions]);

  const handleLoadMore = useCallback(async () => {
    if (!isConnected || isLoadingMore || !hasMoreData) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const payments = await loadPayments({
        limit: 50,
        offset: recentPayments.length,
      });
      setHasMoreData(payments.length === 50);
    } catch (error) {
      transactionsLogger.error('Failed to load more transactions', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [
    isConnected,
    isLoadingMore,
    hasMoreData,
    loadPayments,
    recentPayments.length,
  ]);

  useEffect(() => {
    if (isConnected && recentPayments.length === 0) {
      handleRefresh();
    }
  }, [isConnected, recentPayments.length, handleRefresh]);

  const renderEmptyState = () => (
    <Box bgColor="white" p={6} radius={12}>
      <Box align="center" p={4}>
        <Text color="grey.600" size="lg" textAlign="center">
          {isAuthenticated
            ? isConnected
              ? t('transactions.noTransactions')
              : t('connection.notConnected')
            : t('connection.authenticationRequired')}
        </Text>
        <Text color="grey.500" mt={2} size="sm" textAlign="center">
          {isAuthenticated
            ? isConnected
              ? t('transactions.noTransactionsDescription')
              : t('connection.connectToViewTransactions')
            : t('connection.pleaseAuthenticate')}
        </Text>
      </Box>
    </Box>
  );

  return (
    <PageLayout
      onRefresh={handleRefresh}
      pullToRefresh
      title={t('transactions.title')}
    >
      <Box gap={4}>
        {!(isAuthenticated && isConnected) || recentPayments.length === 0 ? (
          renderEmptyState()
        ) : (
          <Box gap={4}>
            <Text color="grey.600" size="lg" weight="medium">
              {t('transactions.title')} ({recentPayments.length})
            </Text>
            <Box gap={3}>
              {recentPayments.map((payment, index) => (
                <TransactionItem
                  key={payment.txId || `payment-${index}`}
                  payment={payment}
                />
              ))}
            </Box>
            {hasMoreData && (
              <Box align="center" mt={4}>
                <Text
                  color="primary"
                  onPress={handleLoadMore}
                  size="sm"
                  weight="medium"
                >
                  {isLoadingMore
                    ? t('transactions.loadingMore')
                    : t('transactions.loadMore')}
                </Text>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </PageLayout>
  );
}
