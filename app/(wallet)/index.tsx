import { useTranslations } from '@hooks';
import { Badge, Box, PageLayout, Text, Title } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { memo } from 'react';
import { StyleSheet } from 'react-native-unistyles';

const WalletIndexScreen = () => {
  const t = useTranslations('wallet');
  const tCommon = useTranslations('common');

  return (
    <PageLayout
      backgroundGradientOptions={{
        style: {
          opacity: 0.9,
        },
      }}
      bgColor="grey.900"
      buttonBlurOptions={{
        tint: 'dark',
      }}
      buttonGradientVariant="dark"
      buttonProps={{
        href: '/(wallet)/(create)/select-wallet-type',
        hrefType: 'replace',
      }}
      buttonText={tCommon('common.continue')}
      headerBlurOptions={{
        intensity: 25,
        tint: 'systemUltraThinMaterialDark',
      }}
      headerTitleThresholdY={450}
      noLargeTitle
      rightComponent={
        <Badge size="lg" variant="primary">
          {tCommon('common.introduction')}
        </Badge>
      }
      rightComponentContainerProps={{
        width: 'auto',
        marginRight: 16,
      }}
      statusBarOptions={{
        barStyle: 'light-content',
      }}
      title={t('title')}
      titleColor="grey.50"
      withBackgroundGradient
    >
      <Image
        contentFit="contain"
        source={require('@assets/security-image.png')}
        style={styles.image}
      />
      <Box gap={2}>
        <Title color="white">{t('title')}</Title>
        <Text color="grey.100" size="md">
          {t('description')}
        </Text>
      </Box>
    </PageLayout>
  );
};

export default memo(WalletIndexScreen);

const styles = StyleSheet.create((theme) => ({
  header: {
    position: 'absolute',
    top: 47,
    left: 16,
    right: 16,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 10,
  },
  image: {
    width: 370,
    height: 370,
    alignSelf: 'center',
  },
  textContainer: {
    marginTop: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.white,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400',
    color: theme.colors.grey[400],
  },
}));
