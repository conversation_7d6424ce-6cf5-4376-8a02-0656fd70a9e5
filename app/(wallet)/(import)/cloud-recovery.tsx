/** biome-ignore-all lint/nursery/noShadow: . */
import 'react-native-get-random-values';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import { useTranslations, useValidator } from '@hooks';
import useBreezSdkStore from '@store/breez-sdk.store';
import useNotificationStore from '@store/notification.store';
import {
  Alert,
  BackgroundProcessWarningBottomSheet,
  Box,
  PageLayout,
  Text,
  TextInput,
} from '@ui';
import { passwordWalletSchema } from '@utils';
import { createLogger } from '@utils/logger';
import { useGetLatestRecoveryBlob } from '@utils/wallet/wallet-api';
import {
  decryptMnemonicDeterministic,
  useWalletAddressIntegration,
} from '@utils/wallet/wallet-integration';
import { useRouter } from 'expo-router';
import { setItemAsync } from 'expo-secure-store';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { Trans } from 'react-i18next';

const cloudRecoveryLogger = createLogger('CloudRecoveryScreen');

const CloudRecoveryScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'import.cloudRecovery' });
  const tWallet = useTranslations('wallet');
  const tCommon = useTranslations('common');
  const router = useRouter();

  const { startBackgroundWalletCreation } = useBreezSdkStore();
  const { storeWalletAddress } = useWalletAddressIntegration();

  const {
    data: recoveryBlobData,
    isLoading: isLoadingBlob,
    error: blobError,
  } = useGetLatestRecoveryBlob();

  const [showBackgroundWarning, setShowBackgroundWarning] = useState(false);

  const [password, setPassword] = useState('');
  const [passwordTouched, setPasswordTouched] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [importError, setImportError] = useState<string | null>(null);

  const [passwordValidatorMessage, validatePassword] =
    useValidator(passwordWalletSchema);

  const isFormValid =
    !passwordValidatorMessage && password && !!recoveryBlobData?.recovery_blob;

  const handlePasswordChange = useCallback(
    (value: string) => {
      setPassword(value);
      setImportError(null);
      if (!passwordTouched) {
        setPasswordTouched(true);
      }
      validatePassword(value);
    },
    [passwordTouched, validatePassword]
  );

  const handlePasswordBlur = useCallback(() => {
    setPasswordTouched(true);
  }, []);

  const { showModal, hideModal } = useNotificationStore();
  // Cleanup modal on unmount
  useEffect(() => () => hideModal(), [hideModal]);

  const handleImportWallet = useCallback(async () => {
    if (!(isFormValid && recoveryBlobData?.recovery_blob)) {
      return;
    }

    try {
      setIsImporting(true);
      setImportError(null);

      cloudRecoveryLogger.operation('Starting cloud wallet recovery');
      cloudRecoveryLogger.debug('Processing recovery blob');

      // Start modal immediately
      hideModal();
      showModal(tWallet('saveSeedPhrase.creatingWallet'));

      // Responsive logic: subscribe for quick completion and fallback on 10s
      let unsub: null | (() => void) = null;
      unsub = useBreezSdkStore.subscribe(
        (s) => s.backgroundCreationStatus,
        (status) => {
          if (status === 'COMPLETE') {
            const { generatedAddress } = useBreezSdkStore
              .getState()
              .getBackgroundCreationStatus();
            if (generatedAddress) {
              unsub?.();
              if (timerRef.current) {
                clearTimeout(timerRef.current);
                timerRef.current = null as any;
              }
              hideModal();
              router.push('/(wallet)/(import)/import-success');
            }
          }
        }
      );

      if (timerRef.current) clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        const { status } = useBreezSdkStore
          .getState()
          .getBackgroundCreationStatus();
        if (status === 'COMPLETE') return;

        showModal(tWallet('creationOverlay.takingLonger'), {
          bottomButtonLabel: tCommon('common.continue'),
          onBottomButtonPress: () => {
            unsub?.();
            hideModal();
            setShowBackgroundWarning(true);
          },
        });
      }, 10_000);

      const encryptedMnemonic = recoveryBlobData.recovery_blob;

      if (!encryptedMnemonic || encryptedMnemonic.length === 0) {
        throw new Error(t('errors.invalidRecoveryBlob'));
      }

      cloudRecoveryLogger.debug('Recovery blob extracted successfully', {
        encryptedLength: encryptedMnemonic.length,
      });

      let decryptedMnemonic: string;
      try {
        decryptedMnemonic = decryptMnemonicDeterministic(
          encryptedMnemonic,
          password
        );
        cloudRecoveryLogger.success('Recovery blob decrypted successfully');
      } catch (decryptError) {
        cloudRecoveryLogger.error(
          'Failed to decrypt recovery blob',
          decryptError
        );
        throw new Error(t('errors.invalidPassword'));
      }

      await setItemAsync(SecureStorageKeys.MNEMONIC, decryptedMnemonic);
      cloudRecoveryLogger.debug('Mnemonic stored in secure storage');
      cloudRecoveryLogger.operation('Starting background wallet creation');

      // Start background creation
      let creationErrorCaught: any = null;
      await startBackgroundWalletCreation(decryptedMnemonic, {
        storeWalletAddress: async (
          address: string,
          type?: string,
          label?: string
        ) => {
          try {
            await storeWalletAddress(address, type as 'liquid', label);
            return true;
          } catch (error) {
            cloudRecoveryLogger.warn('API address storage failed', error);
            return false;
          }
        },
      }).catch((err) => {
        creationErrorCaught = err;
      });

      if (creationErrorCaught) {
        hideModal();
        throw new Error('Failed to start wallet creation process');
      }
      // Check if quick completion happened; if not, show continue option
      const { status, generatedAddress } = useBreezSdkStore
        .getState()
        .getBackgroundCreationStatus();
      if (status === 'COMPLETE' && generatedAddress) {
        hideModal();
        router.replace('/(wallet)/(import)/import-success');
      } else {
        showModal(tWallet('creationOverlay.takingLonger'), {
          bottomButtonLabel: tCommon('common.continue'),
          onBottomButtonPress: () => {
            unsub?.();
            hideModal();
            setShowBackgroundWarning(true);
          },
        });
      }

      setIsImporting(false);
    } catch (error) {
      cloudRecoveryLogger.error('Failed to recover cloud wallet', { error });
      setIsImporting(false);

      const errorMessage =
        error instanceof Error ? error.message : t('errors.generic');
      setImportError(errorMessage);
    }
  }, [
    isFormValid,
    recoveryBlobData?.recovery_blob,
    password,
    startBackgroundWalletCreation,
    storeWalletAddress,
    router,
    t,
    tCommon,
    tWallet,
    showModal,
    hideModal,
  ]);

  if (isLoadingBlob) {
    return (
      <PageLayout title={t('title')}>
        <Box align="center" flex={1} justify="center" p={4}>
          <Text color="grey.600" size="lg" textAlign="center">
            {t('loadingRecoveryData')}
          </Text>
        </Box>
      </PageLayout>
    );
  }

  if (blobError || !recoveryBlobData?.recovery_blob) {
    return (
      <PageLayout title={t('title')}>
        <Box align="center" flex={1} justify="center" p={4}>
          <Text color="red.600" mb={4} size="lg" textAlign="center">
            {t('errors.noRecoveryData')}
          </Text>
          <Text color="grey.600" size="md" textAlign="center">
            {t('errors.noRecoveryDataDescription')}
          </Text>
        </Box>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      buttonProps={{
        onPress: handleImportWallet,
        disabled: !isFormValid || isImporting,
      }}
      buttonText={isImporting ? t('importing') : t('importWallet')}
      contentMarginTop={4}
      description={
        <Trans
          components={{ bold: <Text weight="semibold" /> }}
          i18nKey="wallet:import.cloudRecovery.description"
        />
      }
      title={t('title')}
    >
      <Box gap={2}>
        <TextInput
          autoCapitalize="none"
          autoComplete="password"
          autoCorrect={false}
          autoFocus
          errorMessage={passwordTouched ? passwordValidatorMessage : undefined}
          isPassword
          label={t('passwordLabel')}
          onBlur={handlePasswordBlur}
          onChangeText={handlePasswordChange}
          placeholder={t('passwordPlaceholder')}
          returnKeyType="done"
          textContentType="password"
          value={password}
        />

        <Box gap={4}>
          {/** biome-ignore lint/complexity/noUselessFragments: . */}
          {importError && <Alert message={<>{importError}</>} type="error" />}

          <Alert
            message="wallet:import.cloudRecovery.securityNote"
            type="info"
          />
        </Box>

        <BackgroundProcessWarningBottomSheet
          isVisible={showBackgroundWarning}
          onConfirm={() => {
            setShowBackgroundWarning(false);
            hideModal();
            router.replace('/(wallet)/wallet');
          }}
          onDismiss={() => setShowBackgroundWarning(false)}
        />
      </Box>
    </PageLayout>
  );
};

export default memo(CloudRecoveryScreen);
