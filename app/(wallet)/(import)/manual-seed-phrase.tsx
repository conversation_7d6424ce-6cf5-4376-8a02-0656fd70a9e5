import 'react-native-get-random-values';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import { useTranslations } from '@hooks';
import useBreezSdkStore from '@store/breez-sdk.store';
import useNotificationStore from '@store/notification.store';
import {
  Alert,
  BackgroundProcessWarningBottomSheet,
  Box,
  PageLayout,
  Text,
} from '@ui';
import { createLogger } from '@utils/logger';
import { WALLET_VALIDATION } from '@utils/wallet/constants';
import { useWalletAddressIntegration } from '@utils/wallet/wallet-integration';
import * as bip39 from 'bip39';
import { SeedPhraseGrid } from 'components/wallet/seed-phrase-input';
import { useRouter } from 'expo-router';
import { setItemAsync } from 'expo-secure-store';
import { memo, useCallback, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { Keyboard, type TextInput } from 'react-native';

const manualImportLogger = createLogger('ManualSeedPhraseImportScreen');

const ManualSeedPhraseImportScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'import.manualSeedPhrase' });
  const tWallet = useTranslations('wallet');
  const tCommon = useTranslations('common');
  const router = useRouter();

  const { startBackgroundWalletCreation } = useBreezSdkStore();
  const { storeWalletAddress } = useWalletAddressIntegration();

  const [userMnemonic, setUserMnemonic] = useState<string[]>(
    new Array(WALLET_VALIDATION.MNEMONIC_WORD_COUNT).fill('')
  );
  const [isImporting, setIsImporting] = useState(false);
  const [showBackgroundWarning, setShowBackgroundWarning] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  const { showModal, hideModal } = useNotificationStore();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleInputChange = useCallback(
    (text: string, index: number) => {
      const newMnemonic = [...userMnemonic];
      newMnemonic[index] = text.trim().toLowerCase();
      setUserMnemonic(newMnemonic);
      setImportError(null);
    },
    [userMnemonic]
  );

  const focusNextInput = useCallback((index: number) => {
    if (inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    } else {
      Keyboard.dismiss();
    }
  }, []);

  const setRef = useCallback((el: TextInput | null, index: number) => {
    inputRefs.current[index] = el;
  }, []);

  const handleImportWallet = useCallback(async () => {
    try {
      setIsImporting(true);
      setImportError(null);

      manualImportLogger.operation('Starting manual seed phrase import');

      // Start modal immediately
      hideModal();
      showModal(tWallet('saveSeedPhrase.creatingWallet'));

      // Responsive logic: subscribe quickly and fallback on 10s
      let unsub: null | (() => void) = null;
      unsub = useBreezSdkStore.subscribe(
        (s) => s.backgroundCreationStatus,
        (status) => {
          if (status === 'COMPLETE') {
            const { generatedAddress } = useBreezSdkStore
              .getState()
              .getBackgroundCreationStatus();
            if (generatedAddress) {
              unsub?.();
              if (timerRef.current) {
                clearTimeout(timerRef.current);
                timerRef.current = null as any;
              }
              hideModal();
              router.replace('/(wallet)/(import)/import-success');
            }
          }
        }
      );

      if (timerRef.current) clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        const { status } = useBreezSdkStore
          .getState()
          .getBackgroundCreationStatus();
        if (status === 'COMPLETE') return;

        showModal(tWallet('creationOverlay.takingLonger'), {
          bottomButtonLabel: tCommon('common.continue'),
          onBottomButtonPress: () => {
            unsub?.();
            hideModal();
            setShowBackgroundWarning(true);
          },
        });
      }, 10_000);

      const mnemonicString = userMnemonic
        .map((word) => word.trim().toLowerCase())
        .filter((word) => word.length > 0)
        .join(' ');

      if (!bip39.validateMnemonic(mnemonicString)) {
        throw new Error(t('errors.invalidSeedPhrase'));
      }

      const mnemonic = mnemonicString;
      manualImportLogger.debug('Seed phrase validation successful');

      await setItemAsync(SecureStorageKeys.MNEMONIC, mnemonic);
      manualImportLogger.debug('Mnemonic stored in secure storage');

      manualImportLogger.operation('Starting background wallet creation');

      let creationErrorCaught: any = null;
      await startBackgroundWalletCreation(mnemonic, {
        storeWalletAddress: async (
          address: string,
          type?: string,
          label?: string
        ) => {
          try {
            await storeWalletAddress(address, type as 'liquid', label);
            return true;
          } catch (error) {
            manualImportLogger.warn('API address storage failed', error);
            return false;
          }
        },
      }).catch((err) => {
        creationErrorCaught = err;
      });

      if (creationErrorCaught) {
        hideModal();
        throw new Error('Failed to start wallet creation process');
      }

      // Remainder handled by subscription or 10s fallback timer set above
    } catch (error) {
      manualImportLogger.error('Failed to import wallet manually', { error });
      setIsImporting(false);

      const errorMessage =
        error instanceof Error ? error.message : t('errors.generic');
      setImportError(errorMessage);
    }
  }, [
    userMnemonic,
    startBackgroundWalletCreation,
    storeWalletAddress,
    router,
    t,
    tCommon,
    tWallet,
    showModal,
    hideModal,
  ]);

  const isFormValid = userMnemonic.every((word) => word.trim().length > 0);

  return (
    <PageLayout
      buttonProps={{
        onPress: handleImportWallet,
        disabled: !isFormValid || isImporting,
        loading: isImporting,
      }}
      buttonText={isImporting ? t('importing') : t('importWallet')}
      description={
        <Trans
          components={{ bold: <Text weight="semibold" /> }}
          i18nKey="wallet:import.manualSeedPhrase.description"
        />
      }
      title={t('title')}
    >
      <Box gap={6}>
        <Box>
          <Text color="grey.600" mb={4} size="lg" weight="medium">
            {t('seedPhraseLabel')}
          </Text>

          <SeedPhraseGrid
            asInput
            focusNextInput={focusNextInput}
            handleInputChange={handleInputChange}
            phrases={new Array(WALLET_VALIDATION.MNEMONIC_WORD_COUNT).fill('')}
            setRef={setRef}
            userMnemonic={userMnemonic}
          />
        </Box>

        {/** biome-ignore lint/complexity/noUselessFragments: . */}
        {importError && <Alert message={<>{importError}</>} type="error" />}

        <Box bgColor="blue.50" p={4} radius={12}>
          <Text color="blue.700" size="sm" textAlign="center">
            {t('securityNote')}
          </Text>
        </Box>
      </Box>

      <BackgroundProcessWarningBottomSheet
        isVisible={showBackgroundWarning}
        onConfirm={() => {
          setShowBackgroundWarning(false);
          hideModal();
          router.replace('/(wallet)/wallet');
        }}
        onDismiss={() => setShowBackgroundWarning(false)}
      />
    </PageLayout>
  );
};

export default memo(ManualSeedPhraseImportScreen);
