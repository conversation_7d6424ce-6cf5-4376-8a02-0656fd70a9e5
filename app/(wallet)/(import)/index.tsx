import { useTranslations } from '@hooks';
import { PageLayout, RadioCard, RadioGroup, Text } from '@ui';
import { useGetLatestRecoveryBlob } from '@utils/wallet/wallet-api';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';

type ImportMethod = 'cloud' | 'manual';

interface ImportOption {
  id: ImportMethod;
  title: string;
  description: string;
  available: boolean;
  unavailableReason?: string;
}

const WalletImportScreen = () => {
  const t = useTranslations('wallet', { keyPrefix: 'import' });
  const tCommon = useTranslations('common');

  const [selectedMethod, setSelectedMethod] = useState<ImportMethod | null>();

  const {
    data: recoveryBlobData,
    isLoading: isCheckingRecoveryBlob,
    error: recoveryBlobError,
  } = useGetLatestRecoveryBlob({
    enabled: true,
    staleTime: 0,
  });

  const hasCloudRecovery = !!recoveryBlobData?.recovery_blob;

  const importOptions: ImportOption[] = useMemo(
    () =>
      [
        {
          id: 'cloud',
          title: t('cloudRecovery.title'),
          description: 'wallet:import.cloudRecovery.description',
          available: hasCloudRecovery,
          unavailableReason: hasCloudRecovery
            ? undefined
            : t('cloudRecovery.unavailable'),
        },
        {
          id: 'manual',
          title: t('manualSeedPhrase.title'),
          description: 'wallet:import.manualSeedPhrase.description',
          available: true,
        },
      ] as ImportOption[],
    [hasCloudRecovery, t]
  );

  const handleSelectMethod = useCallback(
    (methodId: string) => {
      const method = methodId as ImportMethod;
      const option = importOptions.find((opt) => opt.id === method);

      if (option?.available) {
        setSelectedMethod(method);
      }
    },
    [importOptions]
  );

  useEffect(() => {
    if (!(isCheckingRecoveryBlob || hasCloudRecovery || selectedMethod)) {
      setSelectedMethod('manual');
    }
  }, [isCheckingRecoveryBlob, hasCloudRecovery, selectedMethod]);

  const getNextHref = (): string => {
    switch (selectedMethod) {
      case 'cloud':
        return '/(wallet)/(import)/cloud-recovery';
      case 'manual':
        return '/(wallet)/(import)/manual-seed-phrase';
      default:
        return '';
    }
  };

  return (
    <PageLayout
      buttonProps={{
        href: getNextHref(),
        disabled: !selectedMethod,
        hrefType: 'replace',
      }}
      buttonText={tCommon('common.continue')}
      contentMarginTop={4}
      description={t('description')}
      title={t('title')}
    >
      {isCheckingRecoveryBlob ? (
        <Text color="grey.600" size="md" textAlign="center">
          {t('checkingCloudRecovery')}
        </Text>
      ) : (
        <RadioGroup
          onValueChange={handleSelectMethod}
          value={selectedMethod || ''}
        >
          {importOptions.map((option) => (
            <RadioCard
              disabled={!option.available}
              icon={null}
              key={option.id}
              notSelectedContent={
                !option.available && (
                  <Text size="sm">{option.unavailableReason}</Text>
                )
              }
              title={option.title}
              value={option.id}
            >
              <Text
                color={option.available ? 'grey.600' : 'grey.400'}
                size="sm"
              >
                <Trans
                  components={{
                    bold: (
                      <Text
                        color={option.available ? 'grey.600' : 'grey.400'}
                        size="sm"
                        weight="semibold"
                      />
                    ),
                  }}
                  i18nKey={option.description}
                />
              </Text>
              {!option.available && option.unavailableReason && (
                <Text color="red" mt={2} size="xs">
                  {option.unavailableReason}
                </Text>
              )}
            </RadioCard>
          ))}
        </RadioGroup>
      )}

      {recoveryBlobError && (
        <Text color="red" mt={4} size="sm" textAlign="center">
          {t('errorCheckingCloudRecovery')}
        </Text>
      )}
    </PageLayout>
  );
};

export default memo(WalletImportScreen);
