// biome-ignore-all lint: .

/**
 * @fileoverview Bitcoin Distribution Testing Interface (DEV)
 *
 * Development-only testing tool for the /distribute-btc-among-users/ API endpoint.
 * Internationalized; language selection is now a separate page.
 */

import type {
  BitcoinDestination,
  DistributeBtcRequest,
} from '@endpoints/wallet.endpoints';
import { useTranslations } from '@hooks';
import useAuthStore from '@store/auth.store';
import { Box, Button, PageLayout, Text, TextInput } from '@ui';
import { createLogger } from '@utils/logger';
import { useDistributeBtc } from '@utils/wallet/wallet-api';
import { router } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, TouchableOpacity } from 'react-native';
import { AuthState } from 'types/auth.types';

const distributionLogger = createLogger('BitcoinDistribution');

interface DestinationEntry {
  id: string;
  address: string;
  amount_sat: string;
  addressError?: string;
  amountError?: string;
}

export default function BitcoinDistributionTestingScreen() {
  const t = useTranslations('wallet');
  const { authenticated } = useAuthStore();
  const isAuthenticated = authenticated === AuthState.Authenticated;

  const [destinations, setDestinations] = useState<DestinationEntry[]>([
    { id: '1', address: '', amount_sat: '' },
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastResponse, setLastResponse] = useState<any>(null);

  const distributeBtcMutation = useDistributeBtc();

  const validateAddress = (address: string): string | undefined => {
    if (!address.trim()) {
      return t('bitcoinDistribution.validation.addressRequired');
    }
    return;
  };

  const validateAmount = (amount: string): string | undefined => {
    if (!amount.trim()) {
      return t('bitcoinDistribution.validation.amountRequired');
    }
    const numAmount = Number.parseInt(amount.trim(), 10);
    if (Number.isNaN(numAmount) || numAmount <= 0) {
      return t('bitcoinDistribution.validation.amountInvalid');
    }
    if (numAmount < 1) {
      return t('bitcoinDistribution.validation.amountMinimum');
    }
    return;
  };

  const addDestination = useCallback(() => {
    const newId = (destinations.length + 1).toString();
    setDestinations((prev) => [
      ...prev,
      { id: newId, address: '', amount_sat: '' },
    ]);
  }, [destinations.length]);

  const removeDestination = useCallback(
    (id: string) => {
      if (destinations.length > 1) {
        setDestinations((prev) => prev.filter((dest) => dest.id !== id));
      }
    },
    [destinations.length]
  );

  const updateDestination = useCallback(
    (id: string, field: 'address' | 'amount_sat', value: string) => {
      setDestinations((prev) =>
        prev.map((dest) => {
          if (dest.id === id) {
            const updated = { ...dest, [field]: value };

            if (field === 'address') {
              updated.addressError = validateAddress(value);
            } else if (field === 'amount_sat') {
              updated.amountError = validateAmount(value);
            }

            return updated;
          }
          return dest;
        })
      );
    },
    []
  );

  const clearAll = useCallback(() => {
    setDestinations([{ id: '1', address: '', amount_sat: '' }]);
    setLastResponse(null);
  }, []);

  const validateAllDestinations = useCallback(() => {
    const validatedDestinations = destinations.map((dest) => ({
      ...dest,
      addressError: validateAddress(dest.address),
      amountError: validateAmount(dest.amount_sat),
    }));

    setDestinations(validatedDestinations);

    return validatedDestinations.every(
      (dest) =>
        !(dest.addressError || dest.amountError) &&
        dest.address &&
        dest.amount_sat
    );
  }, [destinations]);

  const totals = useMemo(() => {
    const validDestinations = destinations.filter(
      (dest) =>
        dest.address &&
        dest.amount_sat &&
        !validateAddress(dest.address) &&
        !validateAmount(dest.amount_sat)
    );

    const totalSatoshis = validDestinations.reduce((sum, dest) => {
      const amount = Number.parseInt(dest.amount_sat, 10);
      return sum + (Number.isNaN(amount) ? 0 : amount);
    }, 0);

    return {
      count: validDestinations.length,
      totalSatoshis,
      totalBtc: (totalSatoshis / 100_000_000).toFixed(8),
    };
  }, [destinations]);

  const isFormValid = useMemo(() => {
    return destinations.some(
      (dest) =>
        dest.address.trim() &&
        dest.amount_sat.trim() &&
        !validateAddress(dest.address) &&
        !validateAmount(dest.amount_sat)
    );
  }, [destinations]);

  const handleSubmit = useCallback(async () => {
    if (!validateAllDestinations()) {
      Alert.alert(
        t('bitcoinDistribution.alerts.validationError'),
        t('bitcoinDistribution.alerts.validationErrorMessage')
      );
      return;
    }

    const validDestinations: BitcoinDestination[] = destinations
      .filter((dest) => dest.address && dest.amount_sat)
      .map((dest) => ({
        address: dest.address.trim(),
        amount_sat: Number.parseInt(dest.amount_sat.trim(), 10),
      }));

    if (validDestinations.length === 0) {
      Alert.alert(
        t('bitcoinDistribution.alerts.noDestinations'),
        t('bitcoinDistribution.alerts.noDestinationsMessage')
      );
      return;
    }

    Alert.alert(
      t('bitcoinDistribution.alerts.confirmTitle'),
      t('bitcoinDistribution.alerts.confirmMessage', {
        btc: totals.totalBtc,
        sats: totals.totalSatoshis,
        count: totals.count,
      }),
      [
        { text: t('bitcoinDistribution.alerts.cancel'), style: 'cancel' },
        {
          text: t('bitcoinDistribution.alerts.send'),
          style: 'destructive',
          onPress: async () => {
            setIsSubmitting(true);
            try {
              const request: DistributeBtcRequest = {
                destinations: validDestinations,
              };

              distributionLogger.operation('Submitting Bitcoin distribution', {
                destinationCount: validDestinations.length,
                totalSatoshis: totals.totalSatoshis,
              });

              const response = await distributeBtcMutation.mutateAsync(request);
              setLastResponse(response);

              distributionLogger.success(
                'Bitcoin distribution submitted successfully',
                {
                  taskId: response.task_id,
                  totalTransactions: response.total_transactions,
                }
              );

              Alert.alert(
                t('bitcoinDistribution.alerts.successTitle'),
                t('bitcoinDistribution.alerts.successMessage', {
                  taskId: response.task_id,
                  transactions: response.total_transactions,
                })
              );
            } catch (error) {
              distributionLogger.error('Bitcoin distribution failed', error);
              Alert.alert(
                t('bitcoinDistribution.alerts.errorTitle'),
                t('bitcoinDistribution.alerts.errorMessage', {
                  error:
                    error instanceof Error ? error.message : 'Unknown error',
                })
              );
            } finally {
              setIsSubmitting(false);
            }
          },
        },
      ]
    );
  }, [destinations, totals, validateAllDestinations, distributeBtcMutation, t]);

  if (!isAuthenticated) {
    return (
      <PageLayout title={t('bitcoinDistribution.title')}>
        <Box align="center" flex={1} justify="center">
          <Text>{t('bitcoinDistribution.authRequired')}</Text>
        </Box>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      buttonProps={{
        disabled: !isFormValid || isSubmitting,
        loading: isSubmitting,
        onPress: handleSubmit,
      }}
      buttonText={
        isSubmitting
          ? t('bitcoinDistribution.form.submittingButton')
          : t('bitcoinDistribution.form.submitButton')
      }
      contentMarginTop={4}
      title={t('bitcoinDistribution.title')}
    >
      <Box gap={6}>
        <Box
          bgColor="blue.50"
          borderColor="blue.200"
          borderWidth={1}
          p={4}
          radius={8}
        >
          <Text color="blue.800" mb={2} size="md" weight="medium">
            {t('bitcoinDistribution.summary.title')}
          </Text>
          <Text color="blue.700" size="sm">
            {t('bitcoinDistribution.summary.destinations', {
              count: totals.count,
            })}
          </Text>
          <Text color="blue.700" size="sm">
            {t('bitcoinDistribution.summary.totalAmount', {
              btc: totals.totalBtc,
              sats: totals.totalSatoshis.toLocaleString(),
            })}
          </Text>
        </Box>

        <Box gap={4}>
          <Box align="center" flexDirection="row" justify="between">
            <Text size="lg" weight="medium">
              {t('bitcoinDistribution.form.title')}
            </Text>
            <Box flexDirection="row" gap={2}>
              <Button onPress={addDestination} size="xs">
                {t('bitcoinDistribution.form.addButton')}
              </Button>
              <Button onPress={clearAll} size="xs" variant="outlined">
                {t('bitcoinDistribution.form.clearButton')}
              </Button>
            </Box>
          </Box>

          <Box gap={4}>
            {destinations.map((destination, index) => (
              <Box
                borderColor="grey.200"
                borderWidth={1}
                key={destination.id}
                p={4}
                radius={8}
              >
                <Box
                  align="center"
                  flexDirection="row"
                  justify="between"
                  mb={3}
                >
                  <Text size="md" weight="medium">
                    {t('bitcoinDistribution.form.destinationTitle', {
                      number: index + 1,
                    })}
                  </Text>
                  {destinations.length > 1 && (
                    <TouchableOpacity
                      onPress={() => removeDestination(destination.id)}
                    >
                      <Text color="red" size="sm">
                        {t('bitcoinDistribution.form.removeButton')}
                      </Text>
                    </TouchableOpacity>
                  )}
                </Box>

                <Box gap={3}>
                  <TextInput
                    errorMessage={destination.addressError}
                    label={t('bitcoinDistribution.form.addressLabel')}
                    onChangeText={(value) =>
                      updateDestination(destination.id, 'address', value)
                    }
                    placeholder={t(
                      'bitcoinDistribution.form.addressPlaceholder'
                    )}
                    value={destination.address}
                  />

                  <TextInput
                    errorMessage={destination.amountError}
                    keyboardType="numeric"
                    label={t('bitcoinDistribution.form.amountLabel')}
                    onChangeText={(value) =>
                      updateDestination(destination.id, 'amount_sat', value)
                    }
                    placeholder={t(
                      'bitcoinDistribution.form.amountPlaceholder'
                    )}
                    value={destination.amount_sat}
                  />
                </Box>
              </Box>
            ))}
          </Box>
        </Box>

        {!isFormValid &&
          destinations.some((d) => d.address || d.amount_sat) && (
            <Text color="red.500" mt={2} size="sm" textAlign="center">
              {t('bitcoinDistribution.form.validationError')}
            </Text>
          )}

        {lastResponse && (
          <Box
            bgColor="green.50"
            borderColor="green.200"
            borderWidth={1}
            p={4}
            radius={8}
          >
            <Text color="green.800" mb={2} size="md" weight="medium">
              {t('bitcoinDistribution.response.title')}
            </Text>
            <Box gap={1}>
              <Text color="green.700" size="sm">
                {t('bitcoinDistribution.response.taskId', {
                  taskId: lastResponse.task_id,
                })}
              </Text>
              <Text color="green.700" size="sm">
                {t('bitcoinDistribution.response.status', {
                  status: lastResponse.status,
                })}
              </Text>
              <Text color="green.700" size="sm">
                {t('bitcoinDistribution.response.totalTransactions', {
                  count: lastResponse.total_transactions,
                })}
              </Text>
            </Box>
          </Box>
        )}
      </Box>
    </PageLayout>
  );
}
