import { ScreenContainer, Title } from '@ui';
import { useGlobalSearchParams } from 'expo-router';
import { useEffect } from 'react';
import { Keyboard } from 'react-native';
import Animated from 'react-native-reanimated';

export default () => {
  const params = useGlobalSearchParams();

  useEffect(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <ScreenContainer>
      <Animated.View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Animated.View>
          {params?.message && (
            <Title color="primary.600">{params.message as string}</Title>
          )}
        </Animated.View>
      </Animated.View>
    </ScreenContainer>
  );
};
