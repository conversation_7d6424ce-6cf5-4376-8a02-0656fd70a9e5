import { useTranslations, useValidator } from '@hooks';
import useRegistrationStore from '@store/registration.store';
import { Box, PageLayout, TextInput } from '@ui';
import { emailSchema, passwordConfirmSchema, passwordSchema } from '@utils';
import { RegisterErrorContainer } from 'components/sign-up-form/register-error-container';
import { VerificationList } from 'components/sign-up-form/verification-list';
import { useGlobalSearchParams } from 'expo-router';
import { useCallback, useState } from 'react';

const SignUp = () => {
  const t = useTranslations();

  const params = useGlobalSearchParams();
  const [username, setUsername] = useState<string>(
    (params.email as string) || ''
  );
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setIsLoading] = useState(false);

  const { setLoginAndPassword } = useRegistrationStore();

  const [emailValidatorMessage, validateEmail] = useValidator(emailSchema);

  const [passwordValidatorMessage, validatePassword] =
    useValidator(passwordSchema);

  const [passwordConfirmValidatorMessage, validateConfirmPassword] =
    useValidator(passwordConfirmSchema);

  const onUsernameChange = useCallback(
    (newUsername: string) => {
      validateEmail(newUsername);
      setUsername(newUsername);
    },
    [validateEmail]
  );

  const onPasswordChange = useCallback(
    (newPassword: string) => {
      setPassword(newPassword);
      validatePassword(newPassword);
      validateConfirmPassword({
        password: newPassword,
        passwordConfirm: confirmPassword,
      });
    },
    [confirmPassword, validateConfirmPassword, validatePassword]
  );

  const onConfirmPasswordChange = useCallback(
    (passwordConfirm: string) => {
      setConfirmPassword(passwordConfirm);
      validateConfirmPassword({ password, passwordConfirm });
    },
    [password, validateConfirmPassword]
  );

  const isFormValid = !(
    emailValidatorMessage ||
    passwordValidatorMessage ||
    passwordConfirmValidatorMessage
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: !isFormValid,
        loading,
        href: '/(registration-terms)',
        onPress: () => {
          setLoginAndPassword(username, password);
        },
      }}
      buttonText={t('signUp.buttons.moveForward.label')}
      contentGap={5}
      contentMarginTop={5}
      headerTitleSpacing={8}
      title={t('common.signUp')}
    >
      <RegisterErrorContainer
        callback={() => {
          setIsLoading(false);
        }}
      />
      <TextInput
        autoCapitalize="none"
        autoComplete="email"
        errorMessage={emailValidatorMessage}
        keyboardType="email-address"
        label={t('signUp.inputs.username.label')}
        onChangeText={onUsernameChange}
        value={username}
      />
      <TextInput
        autoCapitalize="none"
        errorMessage={passwordValidatorMessage}
        isPassword
        label={t('signUp.inputs.password.label')}
        onChangeText={onPasswordChange}
        value={password}
      />
      <TextInput
        autoCapitalize="none"
        errorMessage={passwordConfirmValidatorMessage}
        isPassword
        label={t('signUp.inputs.confirmPassword.label')}
        onChangeText={onConfirmPasswordChange}
        value={confirmPassword}
      />
      <Box mt={2}>
        <VerificationList password={password} />
      </Box>
    </PageLayout>
  );
};

export default SignUp;
