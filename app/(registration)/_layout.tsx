import { Stack } from 'expo-router';

const RegistrationLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        title: '',
      }}
    >
      <Stack.Screen
        name="sign-up"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="index"
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="activation-code"
        options={{
          title: '',
        }}
      />
    </Stack>
  );
};

export default RegistrationLayout;
