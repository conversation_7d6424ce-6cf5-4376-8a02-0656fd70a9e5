import { fetchAPI } from '@api';
import { ErrorContainer, ResendCode } from '@components';
import { confirmRegistration } from '@endpoints/registration.endpoints';
import { useTranslations } from '@hooks';
import useErrorStore from '@store/error.store';
import { useMutation } from '@tanstack/react-query';
import { Box, PageLayout } from '@ui';
import { styles as textInputStyles } from 'components/ui/inputs/text-input.styles';
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { MaskedTextInput } from 'react-native-mask-text';
import { StyleSheet } from 'react-native-unistyles';
import { ErrorKey } from 'types/error.types';

export default () => {
  const t = useTranslations();

  const maskedInputRef = useRef<React.ElementRef<typeof MaskedTextInput>>(null);
  const [text, setText] = useState('');
  const router = useRouter();
  const params = useGlobalSearchParams();
  const { addError, clearError } = useErrorStore();

  const onResend = useCallback(() => {
    setText('');
    clearError(ErrorKey.RegistrationError);
  }, [clearError]);

  useFocusEffect(
    useCallback(() => {
      clearError(ErrorKey.RegistrationError);
    }, [clearError])
  );

  const { mutate: sendRegistrationCode, isPending: loading } = useMutation({
    mutationFn: () =>
      fetchAPI(confirmRegistration, {
        data: { code: text, username: params?.username as string },
      }),
    onSuccess: () => {
      router.push({
        pathname: '/activation-success',
        params: { username: params?.username as string },
      });
    },
    onError: (data: { error: string; status: number }) => {
      addError(ErrorKey.RegistrationError, {
        key: ErrorKey.RegistrationError,
        errorText: data.error,
        status: data?.status,
        translate: false,
      });
    },
  });

  useEffect(() => {
    if (text.length === 6) {
      maskedInputRef.current?.blur();
    }
  }, [text]);

  return (
    <PageLayout
      buttonProps={{
        disabled: text.length !== 6,
        loading,
      }}
      buttonText={t('signUp.buttons.moveForward.label')}
      onButtonPress={() => sendRegistrationCode()}
      title={t('activationCode.title')}
    >
      <View>
        <ErrorContainer
          callback={() => {
            // TODO: handle error
          }}
          errorKey={ErrorKey.RegistrationError}
        />
      </View>
      <Box flex={3}>
        <MaskedTextInput
          keyboardType="numeric"
          mask="999-999"
          onChangeText={(_, unmasked) => setText(unmasked)}
          ref={maskedInputRef}
          style={[textInputStyles.inputContainer, styles.maskTextInput]}
          value={text}
        />
        <ResendCode onResend={onResend} username={params.username as string} />
      </Box>
    </PageLayout>
  );
};

const styles = StyleSheet.create((theme) => ({
  maskTextInput: {
    height: 58,
    minHeight: 58,
    marginVertical: 10,
    fontSize: 24,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.padding(3),
    letterSpacing: 10,
    borderRadius: 12,
  },
}));
