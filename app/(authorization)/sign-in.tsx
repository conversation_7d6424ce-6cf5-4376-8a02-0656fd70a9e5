import { fetchAP<PERSON> } from '@api';
import { signIn } from '@endpoints/authorization.endpoints';
import { resendCode } from '@endpoints/registration.endpoints';
import { useOnKeyboardHide, useTranslations, useValidator } from '@hooks';
import useAuthStore from '@store/auth.store';
import useErrorStore from '@store/error.store';
import useNotificationStore from '@store/notification.store';
import { useMutation } from '@tanstack/react-query';
import { Box, PageLayout, Text, TextInput } from '@ui';
import { passwordSignInSchema } from '@utils';
import type { AxiosError } from 'axios';
import { AuthErrorContainer } from 'components/sign-in-form/auth-error-container';
import type { TextInputType } from 'components/ui/inputs/text-input';
import { Link, useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AuthState } from 'types/auth.types';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'types/error.types';
import { saveRefreshToken, saveToken } from 'utils/token';
import { emailSchema } from 'utils/validators/email.schema';

const SignIn = () => {
  const t = useTranslations();

  const passwordRef = useRef<TextInputType>(null);
  const params = useGlobalSearchParams();
  const [email, setEmail] = useState(
    (params?.email as string) || (params?.username as string) || ''
  );
  const [emailValidatorMessage, validateEmail, isValidEmail] =
    useValidator(emailSchema);
  const [passwordValidatorMessage, validatePassword, isValidPassword] =
    useValidator(passwordSignInSchema);

  const [password, setPassword] = useState('');
  const router = useRouter();
  const { setAuthState } = useAuthStore();
  const { hideModal } = useNotificationStore();
  const { addError, clearError } = useErrorStore();

  useEffect(() => {
    passwordRef.current?.focus();
  }, []);

  const onFieldFocus = useCallback(() => {
    clearError(ErrorKey.AuthError);
  }, [clearError]);

  const handleEmailChange = (text: string) => {
    validateEmail(text);
    setEmail(text);
    clearError(ErrorKey.AuthError);
  };

  const handlePasswordChange = (text: string) => {
    validatePassword(text);
    setPassword(text);
    clearError(ErrorKey.AuthError);
  };

  const { mutate: submitForm, isPending: loading } = useMutation({
    mutationFn: () =>
      fetchAPI<{ access: string; refresh: string }>(signIn, {
        data: { username: email, password },
      }),
    onSuccess: (response) => {
      setAuthState(AuthState.Authenticated);
      saveToken(response.access);
      saveRefreshToken(response.refresh);
      hideModal();
    },
    onError: (error: AxiosError<{ error_id: string }>) => {
      if (
        error.response?.status === 401 &&
        error?.response?.data?.error_id === '001'
      ) {
        fetchAPI(resendCode, { data: { username: email } }).then(() => {
          router.push({
            pathname: '/activation-code',
            params: { username: email },
          });
        });
      } else if (error.response?.data) {
        addError(ErrorKey.AuthError, {
          key: ErrorKey.AuthError,
          errorText:
            (Object.values(error?.response?.data)?.[0] as string) ||
            'unknown error',
          status: error?.status,
          translate: false,
        });
      }
      hideModal();
    },
  });

  useOnKeyboardHide(() => {
    passwordRef.current?.blur();
    clearError(ErrorKey.AuthError);
  });

  const isButtonEnabled = useMemo(
    () => isValidEmail(email) && isValidPassword(password),
    [email, isValidEmail, isValidPassword, password]
  );

  return (
    <PageLayout
      buttonProps={{
        disabled: !isButtonEnabled,
        loading,
        onPress: () => submitForm(),
      }}
      buttonText={t('signIn.buttons.signIn.label')}
      contentGap={5}
      contentMarginTop={5}
      headerTitleSpacing={8}
      title={t('common.signIn')}
    >
      <AuthErrorContainer errorKey={ErrorKey.AuthError} />
      <TextInput
        autoCapitalize="none"
        autoComplete="email"
        errorMessage={emailValidatorMessage}
        keyboardType="email-address"
        label={t('signIn.inputs.email.label')}
        onChangeText={handleEmailChange}
        onFocus={onFieldFocus}
        onSubmitEditing={() => {
          passwordRef.current?.focus();
        }}
        placeholder={t('signIn.inputs.email.placeholder')}
        returnKeyType="next"
        value={email}
      />
      <Box gap={2}>
        <TextInput
          errorMessage={passwordValidatorMessage}
          isPassword
          label={t('signIn.inputs.password.label')}
          onChangeText={handlePasswordChange}
          onFocus={onFieldFocus}
          placeholder={t('signIn.inputs.password.placeholder')}
          ref={passwordRef}
          value={password}
        />
        <Link href={{ pathname: '/(password-reset)', params: { email } }}>
          <Text color="primary.500" size="sm" textAlign="right">
            {t('common.passwordForgotten')}
          </Text>
        </Link>
      </Box>
    </PageLayout>
  );
};

export default SignIn;
