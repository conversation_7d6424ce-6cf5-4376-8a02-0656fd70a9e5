import { EmailForm, SignWithGoogleButton } from '@components';
import { useTranslations } from '@hooks';
import { Divider, PageLayout } from '@ui';

export default () => {
  const t = useTranslations();

  return (
    <PageLayout
      contentGap={5}
      contentMarginTop={5}
      headerTitleSpacing={8}
      title={t('common.signIn')}
    >
      <EmailForm redirectPath="/sign-in" />
      <Divider label="common.or" />
      <SignWithGoogleButton />
    </PageLayout>
  );
};
