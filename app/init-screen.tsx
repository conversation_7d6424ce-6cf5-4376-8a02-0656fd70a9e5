import { useTranslations } from '@hooks';
import { Box, Button, Logo, ScreenContainer, Text } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useRouter } from 'expo-router';
import { useCallback, useEffect } from 'react';
import { Dimensions, View } from 'react-native';
import Animated, {
  Easing,
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet } from 'react-native-unistyles';

const { width } = Dimensions.get('window');

export default () => {
  const router = useRouter();
  const t = useTranslations();

  const onSignUpPress = useCallback(() => {
    router.push('/(registration)');
  }, [router]);

  const onSignInPress = useCallback(() => {
    router.push('/(authorization)');
  }, [router]);

  const animatedHeight = useSharedValue(500);

  useEffect(() => {
    animatedHeight.value = withDelay(
      200,
      withTiming(0, {
        duration: 250,
        easing: Easing.bezier(0.25, 0.46, 0.45, 0.94),
      })
    );
  }, [animatedHeight]);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      top: animatedHeight.value,
    };
  });

  return (
    <ScreenContainer styles={styles.screenContainer}>
      <Animated.View
        entering={FadeIn.delay(800).duration(600)}
        style={styles.splashImageContainer}
      >
        <Image
          contentFit="cover"
          source={require('@assets/splash-image.png')}
          style={styles.splashImage}
        />
      </Animated.View>
      <Box flex={1} />
      <Box flex={1}>
        <Animated.View style={[styles.card, animatedStyles]}>
          <View>
            <Logo />
            <Text my={6} size="xl" textAlign="center" weight="medium">
              {t('screens.home.title')}
            </Text>
          </View>
          <Box gap={2} style={styles.buttonContainer}>
            <Button onPress={onSignInPress} variant="outlined">
              {t('common.signIn')}
            </Button>
            <Button onPress={onSignUpPress}>{t('common.signUp')}</Button>
          </Box>
        </Animated.View>
      </Box>
    </ScreenContainer>
  );
};

const styles = StyleSheet.create((theme) => ({
  card: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: theme.padding(4),
    paddingVertical: theme.padding(8),
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    width: '100%',
    backgroundColor: theme.colors.white,
  },
  screenContainer: {
    backgroundColor: theme.colors.primary[600],
    marginHorizontal: 0,
    paddingHorizontal: 0,
  },
  splashImageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '85%',
  },
  splashImage: {
    top: 0,
    width,
    aspectRatio: 3 / 4,
    position: 'absolute',
    height: undefined,
  },
  buttonContainer: {
    width: '100%',
  },
}));
