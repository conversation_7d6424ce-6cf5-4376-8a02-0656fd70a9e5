import { ConfirmationScreen } from '@components';
import { useFocusEffect, useRouter } from 'expo-router';
import { useCallback } from 'react';

const REDIRECT_TIMEOUT = 4000;

export default () => {
  const router = useRouter();

  const onButtonPress = useCallback(() => {
    router.replace({ pathname: '/(saving-plan)/plan-summary' });
  }, [router]);

  useFocusEffect(() => {
    const timeout = setTimeout(() => {
      onButtonPress();
    }, REDIRECT_TIMEOUT);

    return () => clearTimeout(timeout);
  });

  return (
    <ConfirmationScreen
      buttonTitle="common.continue"
      headerBackHref="(navigation)"
      message="messages.planCreated"
      onButtonPress={onButtonPress}
    />
  );
};
