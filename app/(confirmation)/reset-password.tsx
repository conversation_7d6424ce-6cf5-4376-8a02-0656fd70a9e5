import { fetchAPI } from '@api';
import { ConfirmationScreen } from '@components';
import { resetPassword } from '@endpoints/authorization.endpoints';
import { useMutation } from '@tanstack/react-query';
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback } from 'react';

export default () => {
  const router = useRouter();
  const params = useGlobalSearchParams();

  const { mutate: sendResetPassword } = useMutation({
    mutationFn: () =>
      fetchAPI(resetPassword, { data: { email: params.email as string } }),
  });

  const onButtonPress = useCallback(() => {
    router.replace({ pathname: '/sign-in', params: { email: params.email } });
  }, [params.email, router]);

  useFocusEffect(
    useCallback(() => {
      if (params?.email) {
        sendResetPassword();
      }
    }, [params.email, sendResetPassword])
  );

  return (
    <ConfirmationScreen
      buttonTitle="common.signIn"
      message="messages.activationLinkSent"
      onButtonPress={onButtonPress}
    />
  );
};
