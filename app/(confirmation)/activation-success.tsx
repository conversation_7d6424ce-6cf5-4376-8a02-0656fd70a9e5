import { ConfirmationScreen } from '@components';
import { useTranslations } from '@hooks';
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback, useRef } from 'react';

const REDIRECT_TIMEOUT = 4000;

export default () => {
  const router = useRouter();
  const params = useGlobalSearchParams();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const t = useTranslations();
  const onButtonPress = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    router.replace({
      pathname: '/sign-in',
      params: { username: params?.username },
    });
  }, [router, params]);

  useFocusEffect(() => {
    timeoutRef.current = setTimeout(() => {
      onButtonPress();
    }, REDIRECT_TIMEOUT);
  });

  return (
    <ConfirmationScreen
      buttonTitle={t('common.signIn')}
      message={t('messages.accountActivated')}
      onButtonPress={onButtonPress}
    />
  );
};
