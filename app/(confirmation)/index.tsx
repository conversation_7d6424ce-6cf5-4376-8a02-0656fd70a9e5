import { ConfirmationScreen } from '@components';
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback, useEffect, useRef } from 'react';

const REDIRECT_TIMEOUT = 4000;

export default () => {
  const router = useRouter();
  const params = useGlobalSearchParams();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const onButtonPress = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    router.push({
      pathname: '/activation-code',
      params: { username: params?.username },
    });
  }, [router, params]);

  useFocusEffect(() => {
    timeoutRef.current = setTimeout(() => {
      onButtonPress();
    }, REDIRECT_TIMEOUT);
  });

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <ConfirmationScreen
      buttonTitle="common.continue"
      message="messages.activationLinkSent"
      onButtonPress={onButtonPress}
    />
  );
};
