import { useTranslations } from '@hooks';
import { Box, HeaderBackButton, PageLayout, Text } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import type React from 'react';
import { useCallback, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { Dimensions } from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import Carousel, {
  type ICarouselInstance,
} from 'react-native-reanimated-carousel';
import { StyleSheet } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

interface DataSecuritySlide {
  id: string;
  titleKey: string;
  descriptionKey?: string;
  image: string;
}

const slides: DataSecuritySlide[] = [
  {
    id: 'intro',
    titleKey: 'dataSecurity.slides.intro.title',
    image: require('@assets/safe-data-1.png'),
  },
  {
    id: 'encryption',
    titleKey: 'dataSecurity.slides.encryption.title',
    descriptionKey: 'dataSecurity.slides.encryption.description',
    image: require('@assets/safe-data-2.png'),
  },
  {
    id: 'secureLogin',
    titleKey: 'dataSecurity.slides.secureLogin.title',
    descriptionKey: 'dataSecurity.slides.secureLogin.description',
    image: require('@assets/security-image.png'),
  },
];

const CarouselIndicator: React.FC<{
  index: number;
  length: number;
  animValue: Animated.SharedValue<number>;
}> = (props) => {
  const { animValue, index } = props;

  const animStyle = useAnimatedStyle(() => {
    const value = animValue.value;

    const inputRange = [index - 1, index, index + 1];
    const outputColorRange = ['#414E49', '#F9FAFA', '#414E49'];

    const backgroundColor = interpolateColor(
      value,
      inputRange,
      outputColorRange
    );

    return {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor,
    };
  }, [animValue, index]);

  return <Animated.View style={[styles.indicator, animStyle]} />;
};

const IntroSlide: React.FC<{ item: DataSecuritySlide }> = ({ item }) => {
  const t = useTranslations();

  return (
    <Box flex={1} px={4}>
      <Box align="start" flex={1} justify="center" pt={8}>
        <Text style={styles.introTitle}>{t(item.titleKey)}</Text>
        <Box align="center" flex={1} justify="end" pb={20} w="100%">
          <Image
            contentFit="contain"
            source={item.image}
            style={styles.introImage}
          />
        </Box>
      </Box>
    </Box>
  );
};

const ContentSlide: React.FC<{ item: DataSecuritySlide }> = ({ item }) => {
  const t = useTranslations();

  return (
    <Box flex={1} px={4}>
      <Box align="center" mb={12.5} mt={10}>
        <Image
          contentFit="contain"
          source={item.image}
          style={styles.contentImage}
        />
      </Box>

      <Box px={0}>
        <Text style={styles.contentTitle}>{t(item.titleKey)}</Text>
        <Text style={styles.contentDescription}>
          <Trans
            components={{ bold: <Text color="grey.100" weight="semibold" /> }}
            i18nKey={item.descriptionKey ?? ''}
          />
        </Text>
      </Box>
    </Box>
  );
};

const CarouselIndicators: React.FC<{
  slides: DataSecuritySlide[];
  progressValue: Animated.SharedValue<number>;
}> = ({ slides: slidesData, progressValue }) => (
  <Box
    align="center"
    bgColor="grey.800"
    flexDirection="row"
    gap={2}
    px={2}
    py={2}
    radius={100}
  >
    {slidesData.map((slide, index) => (
      <CarouselIndicator
        animValue={progressValue}
        index={index}
        key={slide.id}
        length={slidesData.length}
      />
    ))}
  </Box>
);

export default function DataSecurityScreen() {
  const t = useTranslations();
  const router = useRouter();
  const carouselRef = useRef<ICarouselInstance>(null);
  const progressValue = useSharedValue<number>(0);
  const [currentSlideIndex, setCurrentSlideIndex] = useState<number>(0);

  const handleNext = useCallback(() => {
    const currentIndex = Math.round(progressValue.value);
    const nextIndex = currentIndex + 1;
    if (nextIndex < slides.length) {
      carouselRef.current?.scrollTo({ index: nextIndex, animated: true });
    } else {
      router.back();
    }
  }, [router, progressValue.value]);

  const renderSlide = ({
    item,
    index,
  }: {
    item: DataSecuritySlide;
    index: number;
  }) => {
    const isFirstSlide = index === 0;

    if (isFirstSlide) {
      return <IntroSlide item={item} />;
    }
    return <ContentSlide item={item} />;
  };

  const getButtonText = () => {
    return currentSlideIndex === 0
      ? 'dataSecurity.buttons.continue'
      : 'dataSecurity.buttons.next';
  };

  const bottomGradientStyle = useAnimatedStyle(() => {
    const progress = progressValue.value;

    const opacity = interpolate(
      progress,
      [0, 0.8, 1],
      [1, 0, 0],
      Extrapolate.CLAMP
    );

    return {
      opacity,
    };
  });

  const topGradientStyle = useAnimatedStyle(() => {
    const progress = progressValue.value;

    const opacity = interpolate(
      progress,
      [0, 0.2, 1],
      [0, 1, 1],
      Extrapolate.CLAMP
    );

    return {
      opacity,
    };
  });

  const customGradients = (
    <>
      <Animated.View
        pointerEvents="none"
        style={[StyleSheet.absoluteFill, bottomGradientStyle]}
      >
        <LinearGradient
          colors={['rgba(64, 197, 185, 0)', 'rgba(64, 197, 185, 0.15)']}
          locations={[0.4, 1]}
          style={StyleSheet.absoluteFill}
        />
      </Animated.View>

      <Animated.View
        pointerEvents="none"
        style={[StyleSheet.absoluteFill, topGradientStyle]}
      >
        <LinearGradient
          colors={['rgba(64, 197, 185, 0.15)', 'rgba(64, 197, 185, 0)']}
          locations={[0, 0.6]}
          style={StyleSheet.absoluteFill}
        />
      </Animated.View>
    </>
  );

  return (
    <PageLayout
      bgColor="grey.900"
      buttonGradientVariant="dark"
      buttonText={t(getButtonText())}
      customBackgroundGradients={customGradients}
      leftComponent={<HeaderBackButton />}
      noButtonBlur={true}
      noPadding={true}
      noScroll={true}
      onButtonPress={handleNext}
      rightComponent={
        slides.length > 1 ? (
          <CarouselIndicators progressValue={progressValue} slides={slides} />
        ) : undefined
      }
      rightComponentContainerProps={{
        width: 'auto',
        minWidth: 60,
        paddingRight: 16,
      }}
      statusBarOptions={{
        backgroundColor: '#131615',
        barStyle: 'light-content',
      }}
    >
      <Carousel
        data={slides}
        loop={false}
        onConfigurePanGesture={(gesture) => {
          'worklet';
          const currentIndex = Math.round(progressValue.value);
          if (currentIndex === 0) {
            gesture.activeOffsetX([-15, Number.POSITIVE_INFINITY]);
          } else {
            gesture.activeOffsetX([-15, 15]);
          }
        }}
        onProgressChange={(_, absoluteProgress) => {
          progressValue.value = absoluteProgress;
          setCurrentSlideIndex(Math.round(absoluteProgress));
        }}
        pagingEnabled
        ref={carouselRef}
        renderItem={renderSlide}
        scrollAnimationDuration={450}
        snapEnabled
        width={screenWidth}
      />
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.grey[500],
  },
  introTitle: {
    fontSize: 40,
    fontWeight: '500',
    color: theme.colors.white,
    lineHeight: 40,
    textAlign: 'left',
    letterSpacing: 0,
    marginBottom: 32,
  },
  introImage: {
    width: Math.min(500, screenWidth + 20),
    height: Math.min(500, screenWidth + 20),
  },
  contentImage: {
    width: Math.min(329, screenWidth - 64),
    height: Math.min(329, screenWidth - 64),
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: '500',
    color: theme.colors.white,
    lineHeight: 28.8,
    marginBottom: 8,
  },
  contentDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.grey[100],
    lineHeight: 24,
  },
}));
