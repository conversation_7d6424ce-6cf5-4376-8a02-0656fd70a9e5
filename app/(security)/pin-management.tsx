// biome-ignore-all lint: .

import { useTranslations } from '@hooks';
import { Al<PERSON>, Box, Button, PageLayout, Text, Title } from '@ui';
import { PinInput } from 'components/ui/inputs/pin-input';
import { router } from 'expo-router';
import { memo, useCallback, useEffect, useState } from 'react';
import { Alert as RNAlert, Switch } from 'react-native';
import { useUnistyles } from 'react-native-unistyles';
import { authenticationService } from 'services/authentication.service';
import type { PinValidationResult } from 'types/authentication.types';
import { WEAK_PIN_PATTERNS } from 'types/authentication.types';

enum PinSetupStep {
  OVERVIEW = 'overview',
  CREATE_PIN = 'create_pin',
  CONFIRM_PIN = 'confirm_pin',
  SUCCESS = 'success',
}

const PinManagementScreen = memo(() => {
  const t = useTranslations('security');
  const tCommon = useTranslations('common');
  const { theme } = useUnistyles();

  const [currentStep, setCurrentStep] = useState<PinSetupStep>(
    PinSetupStep.OVERVIEW
  );
  const [isPinEnabled, setIsPinEnabled] = useState(false);
  const [isPinConfigured, setIsPinConfigured] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [pinError, setPinError] = useState('');

  useEffect(() => {
    loadPinConfiguration();
  }, []);

  const loadPinConfiguration = useCallback(async () => {
    try {
      setIsLoading(true);
      await authenticationService.initialize();
      const config = await authenticationService.getConfig();
      setIsPinConfigured(config.pin.isConfigured);
      setIsPinEnabled(config.pin.isEnabled);
    } catch (error) {
      console.error('Failed to load PIN configuration:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const validatePin = useCallback(
    (pin: string): PinValidationResult => {
      const errors: string[] = [];

      const hasValidLength = pin.length === 6;
      if (!hasValidLength) {
        errors.push(t('pin.validation.invalidLength'));
      }

      const hasOnlyDigits = /^\d+$/.test(pin);
      if (!hasOnlyDigits) {
        errors.push(t('pin.validation.onlyDigits'));
      }

      const isNotSimplePattern = !WEAK_PIN_PATTERNS.includes(pin);
      if (!isNotSimplePattern) {
        errors.push(t('pin.validation.tooSimple'));
      }

      return {
        isValid: errors.length === 0,
        errors,
        hasValidLength,
        hasOnlyDigits,
        isNotSimplePattern,
      };
    },
    [t]
  );

  const handlePinToggle = useCallback(
    async (enabled: boolean) => {
      if (!isPinConfigured && enabled) {
        setCurrentStep(PinSetupStep.CREATE_PIN);
        return;
      }

      try {
        setIsLoading(true);
        await authenticationService.setPinEnabled(enabled);
        setIsPinEnabled(enabled);
      } catch (error) {
        console.error('Failed to toggle PIN:', error);
        RNAlert.alert(
          t('pin.errors.toggleFailed.title'),
          t('pin.errors.toggleFailed.message')
        );
      } finally {
        setIsLoading(false);
      }
    },
    [isPinConfigured, t]
  );

  const handleCreatePin = useCallback(
    (pin: string) => {
      const validation = validatePin(pin);
      if (!validation.isValid) {
        setPinError(validation.errors[0]);
        return;
      }

      setNewPin(pin);
      setPinError('');
      setCurrentStep(PinSetupStep.CONFIRM_PIN);
    },
    [validatePin]
  );

  const handleConfirmPin = useCallback(
    async (pin: string) => {
      if (pin !== newPin) {
        setPinError(t('pin.validation.mismatch'));
        return;
      }

      try {
        setIsLoading(true);
        const success = await authenticationService.setupPin(pin);

        if (success) {
          setIsPinConfigured(true);
          setIsPinEnabled(true);
          setCurrentStep(PinSetupStep.SUCCESS);
        } else {
          setPinError(t('pin.errors.setupFailed'));
        }
      } catch (error) {
        console.error('Failed to setup PIN:', error);
        setPinError(t('pin.errors.setupFailed'));
      } finally {
        setIsLoading(false);
      }
    },
    [newPin, t]
  );

  const handleChangePin = useCallback(() => {
    setNewPin('');
    setConfirmPin('');
    setPinError('');
    setCurrentStep(PinSetupStep.CREATE_PIN);
  }, []);

  const handleBackToOverview = useCallback(() => {
    setNewPin('');
    setConfirmPin('');
    setPinError('');
    setCurrentStep(PinSetupStep.OVERVIEW);
  }, []);

  const handleGoBack = useCallback(() => {
    if (currentStep === PinSetupStep.OVERVIEW) {
      router.back();
    } else {
      handleBackToOverview();
    }
  }, [currentStep, handleBackToOverview]);

  const renderOverview = () => (
    <Box gap={6}>
      <Box
        bgColor="white"
        borderColor="grey.200"
        borderWidth={1}
        gap={4}
        p={4}
        radius={12}
      >
        <Box align="center" flexDirection="row" justify="between">
          <Box flex={1}>
            <Text size="lg" weight="medium">
              {t('pin.overview.enablePin')}
            </Text>
            <Text color="grey.600" size="sm">
              {isPinConfigured
                ? t('pin.overview.pinConfigured')
                : t('pin.overview.pinNotConfigured')}
            </Text>
          </Box>
          <Switch
            disabled={isLoading}
            onValueChange={handlePinToggle}
            thumbColor={theme.colors.white}
            trackColor={{
              false: theme.colors.grey[200],
              true: theme.colors.primary[500],
            }}
            value={isPinEnabled}
          />
        </Box>

        {isPinConfigured && (
          <Button
            disabled={isLoading}
            onPress={handleChangePin}
            variant="outlined"
          >
            {t('pin.overview.changePin')}
          </Button>
        )}
      </Box>

      <Alert message="security:pin.overview.securityNote" type="info" />
    </Box>
  );

  const renderCreatePin = () => (
    <Box gap={6}>
      <PinInput
        autoFocus
        disabled={isLoading}
        errorMessage={pinError}
        hasError={!!pinError}
        onChange={setNewPin}
        onComplete={handleCreatePin}
        value={newPin}
      />

      <Box gap={2}>
        <Text color="grey.700" size="sm" weight="medium">
          {t('pin.create.requirements.title')}
        </Text>
        <Text color="grey.600" size="sm">
          • {t('pin.create.requirements.length')}
        </Text>
        <Text color="grey.600" size="sm">
          • {t('pin.create.requirements.digits')}
        </Text>
        <Text color="grey.600" size="sm">
          • {t('pin.create.requirements.notSimple')}
        </Text>
      </Box>
    </Box>
  );

  const renderConfirmPin = () => (
    <PinInput
      autoFocus
      disabled={isLoading}
      errorMessage={pinError}
      hasError={!!pinError}
      onChange={setConfirmPin}
      onComplete={handleConfirmPin}
      value={confirmPin}
    />
  );

  const getStepContent = () => {
    switch (currentStep) {
      case PinSetupStep.OVERVIEW:
        return renderOverview();
      case PinSetupStep.CREATE_PIN:
        return renderCreatePin();
      case PinSetupStep.CONFIRM_PIN:
        return renderConfirmPin();
      case PinSetupStep.SUCCESS:
        return null;
      default:
        return renderOverview();
    }
  };

  const getTitle = () => {
    switch (currentStep) {
      case PinSetupStep.CREATE_PIN:
        return t('pin.create.pageTitle');
      case PinSetupStep.CONFIRM_PIN:
        return t('pin.confirm.pageTitle');
      case PinSetupStep.SUCCESS:
        return t('pin.success.pageTitle');
      default:
        return t('pin.management.title');
    }
  };

  const getDescription = () => {
    switch (currentStep) {
      case PinSetupStep.CREATE_PIN:
        return t('pin.create.description');
      case PinSetupStep.CONFIRM_PIN:
        return t('pin.confirm.description');
      case PinSetupStep.SUCCESS:
        return t('pin.success.description');
      default:
        return t('pin.overview.description');
    }
  };

  const getButtonText = () => {
    switch (currentStep) {
      case PinSetupStep.CREATE_PIN:
      case PinSetupStep.CONFIRM_PIN:
        return tCommon('common.back');
      case PinSetupStep.SUCCESS:
        return tCommon('common.finish');
      default:
        return undefined;
    }
  };

  const getButtonAction = () => {
    switch (currentStep) {
      case PinSetupStep.CREATE_PIN:
      case PinSetupStep.CONFIRM_PIN:
        return handleBackToOverview;
      case PinSetupStep.SUCCESS:
        return () => router.push('(authenticated)');
      default:
        return undefined;
    }
  };

  return (
    <PageLayout
      buttonText={getButtonText()}
      contentGap={4}
      contentMarginTop={4}
      description={getDescription()}
      onBackPress={handleGoBack}
      onButtonPress={getButtonAction()}
      title={getTitle()}
    >
      {getStepContent()}
    </PageLayout>
  );
});

PinManagementScreen.displayName = 'PinManagementScreen';

export default PinManagementScreen;
