import { useTranslations } from '@hooks';
import { <PERSON><PERSON>, HeaderBackButton } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { router } from 'expo-router';
import { Text, View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

export default function SecretPhraseScreen() {
  const t = useTranslations();

  const handleIntroductionPress = () => {
    router.push('/security/data-security');
  };

  const handleContinuePress = () => {
    router.push('/security/data-security');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <HeaderBackButton />
        <Button onPress={handleIntroductionPress}>
          {t('common.introduction')}
        </Button>
      </View>
      <Image
        contentFit="contain"
        source={require('@assets/security-image.png')}
        style={styles.image}
      />
      <View style={styles.textContainer}>
        <Text style={styles.title}>{t('security.title')}</Text>
        <Text style={styles.description}>{t('security.description')}</Text>
      </View>
      <Button mt={6} onPress={handleContinuePress}>
        {t('common.continue')}
      </Button>
    </View>
  );
}

const styles = StyleSheet.create((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.grey[900],
    paddingHorizontal: 24,
    paddingTop: 48,
    paddingBottom: 32,
    justifyContent: 'space-between',
  },
  header: {
    position: 'absolute',
    top: 47,
    left: 16,
    right: 16,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 10,
  },
  image: {
    width: 370,
    height: 370,
    alignSelf: 'center',
    marginTop: 87,
  },
  textContainer: {
    marginTop: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.white,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400',
    color: theme.colors.grey[100],
  },
}));
