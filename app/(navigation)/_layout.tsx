import { Stack } from 'expo-router';

const NavigationLayout = () => {
  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="user-details" options={{ headerShown: false }} />
      <Stack.Screen name="about-app" options={{ headerShown: false }} />
      <Stack.Screen
        name="terms-and-conditions"
        options={{ headerShown: false }}
      />
      <Stack.Screen name="language" options={{ headerShown: false }} />
    </Stack>
  );
};

export default NavigationLayout;
