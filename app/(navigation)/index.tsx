/** biome-ignore-all lint/correctness/noUndeclaredVariables: global dev flag */
/** biome-ignore-all lint/suspicious/noConsole: debug logging */

import { SecureStorageKeys } from '@const/secure-storage-keys';
import {
  getSavingPlanSettings,
  type SavingPlanProps,
} from '@endpoints/saving-plan.endpoints';
import {
  getProfileInfo,
  type ProfileInfoResponse,
} from '@endpoints/user.endpoints';
import { useTranslations } from '@hooks';

import useVeriffStore, { VerificationStatus } from '@store/veriff.store';
import { useQuery } from '@tanstack/react-query';
import {
  AmbassadorBox,
  Badge,
  Box,
  ConfirmationDialog,
  PageLayout,
  PlanBox,
  Skeleton,
  Text,
  Title,
} from '@ui';
import { getQueryData } from '@utils/api';
import toTitleCase from '@utils/capitalize';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';
import { CachedImage as Image } from 'components/ui/cached-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { getItem as getItemSecureStore } from 'expo-secure-store';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { List } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import { performLogout } from 'services/logout.service';

export default function ProfileScreen() {
  const router = useRouter();
  const t = useTranslations('navigation');
  const tCommon = useTranslations('common');
  const { theme } = useUnistyles();
  const { verificationStatus, pollVerificationStatus, loadInitialState } =
    useVeriffStore();
  // Breez SDK store available if needed: useBreezSdkStore();
  const { state: walletState } = useSimpleWallet();
  const { data: profileInfo, isLoading: isLoadingProfileInfo } = useQuery(
    getQueryData<ProfileInfoResponse>(getProfileInfo)
  );
  const { data: existingPlan, isSuccess: isPlanLoaded } = useQuery(
    getQueryData<SavingPlanProps>(getSavingPlanSettings, {
      queryOptions: { retry: false, staleTime: 60_000 },
    })
  );
  const hasSavingPlan = useMemo(
    () => Boolean(isPlanLoaded && existingPlan && existingPlan.name),
    [existingPlan, isPlanLoaded]
  );

  const [isCheckingWallet, setIsCheckingWallet] = useState(false);
  const [isLogoutDialogVisible, setIsLogoutDialogVisible] = useState(false);

  useEffect(() => {
    loadInitialState();
  }, [loadInitialState]);

  // TODO this seems like a hack, we should have a better way to handle this
  useEffect(() => {
    if (verificationStatus === VerificationStatus.PENDING) {
      pollVerificationStatus();
    }
  }, [verificationStatus, pollVerificationStatus]);

  useEffect(() => {
    console.log({
      existingPlan,
      isPlanLoaded,
      hasSavingPlan,
    });
  }, [existingPlan, isPlanLoaded, hasSavingPlan]);

  const handleWalletNavigation = useCallback(() => {
    setIsCheckingWallet(true);

    try {
      const storedMnemonic = getItemSecureStore(SecureStorageKeys.MNEMONIC);

      // If we have a wallet detected (simple wallet) or stored mnemonic, allow navigation immediately
      if (walletState.hasWallet || storedMnemonic) {
        router.push('/(wallet)/wallet');
        return;
      }

      // Fallback: no wallet detected
      router.push('/(wallet)');
    } catch (_error) {
      router.push('/(wallet)');
    } finally {
      setIsCheckingWallet(false);
    }
  }, [router, walletState.hasWallet]);

  const getVerificationBadge = () => {
    switch (verificationStatus) {
      case VerificationStatus.LOADING:
        return (
          <Badge mt={0.7} size="md" variant="info">
            {tCommon('kyc.status.loading')}
          </Badge>
        );
      case VerificationStatus.COMPLETED:
        return (
          <Badge mt={0.7} size="md" variant="success">
            {tCommon('kyc.status.completed')}
          </Badge>
        );
      case VerificationStatus.PENDING:
        return (
          <Badge mt={0.7} size="md" variant="info">
            {tCommon('kyc.status.submitted')}
          </Badge>
        );
      case VerificationStatus.FAILED:
      case VerificationStatus.TIMEOUT:
        return (
          <Badge mt={0.7} size="md" variant="error">
            {tCommon('kyc.status.failed')}
          </Badge>
        );
      case VerificationStatus.CANCELLED:
        return (
          <Badge mt={0.7} size="md" variant="warning">
            {tCommon('kyc.status.cancelled')}
          </Badge>
        );
      default:
        return (
          <Badge mt={0.7} size="md" variant="info">
            {tCommon('kyc.status.notStarted')}
          </Badge>
        );
    }
  };

  return (
    <PageLayout
      bgColor="grey.50"
      headerAnimationEndY={140}
      headerAnimationStartY={100}
      headerBlurOptions={{
        intensity: 40,
        tint: 'systemThinMaterialLight',
      }}
      headerOverlayContent
      noLargeTitle
      noPadding
      title={t('title')}
    >
      <View style={styles.backgroundContainer}>
        <Image
          blurRadius={50}
          source={require('@assets/test-profile-picture.png')}
          style={styles.backgroundAvatar}
        />
        <LinearGradient
          colors={['rgba(242, 244, 243, 0)', theme.colors.grey[50]]}
          locations={[0, 1]}
          style={styles.gradientOverlay}
        />
      </View>

      <Box align="center" mb={6} mt={10} px={4}>
        <Image
          source={require('@assets/test-profile-picture.png')}
          style={styles.avatar}
        />
        {isLoadingProfileInfo ? (
          <Skeleton style={{ width: 100, height: 24 }} />
        ) : (
          <TouchableOpacity
            accessibilityRole="link"
            accessible
            activeOpacity={0.7}
            disabled={verificationStatus === VerificationStatus.LOADING}
            onPress={() => {
              if (
                verificationStatus === VerificationStatus.NOT_STARTED ||
                verificationStatus === VerificationStatus.FAILED ||
                verificationStatus === VerificationStatus.TIMEOUT ||
                verificationStatus === VerificationStatus.CANCELLED
              ) {
                router.push('/(id-verification)/verification-entry');
              } else {
                router.push('/(navigation)/user-details');
              }
            }}
            style={{ alignItems: 'center' }}
          >
            <Title color="dark" size="sm">
              {profileInfo?.profile?.first_and_middle_name
                ? toTitleCase(
                    `${profileInfo?.profile?.first_and_middle_name} ${profileInfo?.profile?.last_name}`
                  )
                : profileInfo?.username.split('@')[0]}
            </Title>
          </TouchableOpacity>
        )}
      </Box>

      <Box
        flexDirection="row"
        justify="between"
        mb={4}
        px={4}
        style={styles.infoBoxesContainer}
      >
        <Box flex={1}>
          <PlanBox />
        </Box>
        <Box flex={1}>
          <AmbassadorBox />
        </Box>
      </Box>

      <Box px={4} style={{ flex: 1 }}>
        <Box style={styles.section}>
          <List.Item
            disabled={verificationStatus === VerificationStatus.LOADING}
            left={(props) => <List.Icon {...props} icon="account-outline" />}
            onPress={() => {
              if (
                verificationStatus === VerificationStatus.NOT_STARTED ||
                verificationStatus === VerificationStatus.FAILED ||
                verificationStatus === VerificationStatus.TIMEOUT ||
                verificationStatus === VerificationStatus.CANCELLED
              ) {
                router.push('/(id-verification)/verification-entry');
              } else {
                router.push('/(navigation)/user-details');
              }
            }}
            right={getVerificationBadge}
            title={t('navigation.myData')}
          />
          <List.Item
            left={(props) => <List.Icon {...props} icon="piggy-bank-outline" />}
            onPress={() =>
              router.push(
                hasSavingPlan ? '/(saving-plan)/plan-summary' : '/(saving-plan)'
              )
            }
            title={t('navigation.savingPlan')}
          />
          <List.Item
            left={(props) => <List.Icon {...props} icon="bank-outline" />}
            onPress={() => router.push('(bank-connection)')}
            title={t('banking.connectedAccounts')}
          />
          <List.Item
            disabled
            left={(props) => (
              <List.Icon {...props} icon="credit-card-outline" />
            )}
            onPress={() => router.push('(navigation)')}
            title={t('banking.cardPayment')}
            titleStyle={{ opacity: 0.5 }}
          />
          <List.Item
            disabled={isCheckingWallet}
            left={(props) =>
              isCheckingWallet ? (
                <ActivityIndicator
                  {...props}
                  color={theme.colors.grey[600]}
                  size="small"
                />
              ) : (
                <List.Icon {...props} icon="wallet-outline" />
              )
            }
            onPress={handleWalletNavigation}
            title={t('wallet.web3')}
          />
          {!(walletState.isLoading || walletState.hasWallet) && (
            <List.Item
              left={(props) => <List.Icon {...props} icon="wallet-plus" />}
              onPress={() => router.push('(wallet)/(import)')}
              title={t('wallet.import.title')}
            />
          )}
        </Box>

        <Box style={styles.section}>
          <List.Item
            disabled
            left={(props) => <List.Icon {...props} icon="fingerprint" />}
            onPress={() => router.push('(security)')}
            style={{ opacity: 0.5 }}
            title={t('security.biometric')}
          />
          <List.Item
            left={(props) => <List.Icon {...props} icon="numeric" />}
            onPress={() => router.push('(security)/pin-management')}
            title={t('security.pinManagement')}
          />
          <List.Item
            left={(props) => <List.Icon {...props} icon="lock-reset" />}
            onPress={() =>
              router.push({
                pathname: '/password-reset',
                params: profileInfo?.username
                  ? { email: profileInfo.username }
                  : undefined,
              })
            }
            title={t('security.changePassword')}
          />
          <List.Item
            left={(props) => (
              <List.Icon {...props} icon="shield-check-outline" />
            )}
            onPress={() => router.push('(security)/data-security')}
            title={t('security.secureData')}
          />
        </Box>

        <Box style={styles.section}>
          <List.Item
            left={(props) => <List.Icon {...props} icon="translate" />}
            onPress={() => router.push('(navigation)/language')}
            title={t('account.language')}
          />
          <List.Item
            left={(props) => (
              <List.Icon {...props} icon="file-document-outline" />
            )}
            onPress={() => router.push('(navigation)/terms-and-conditions')}
            title={t('account.policy')}
          />
          <List.Item
            disabled
            left={(props) => <List.Icon {...props} icon="bug-outline" />}
            onPress={() => router.push('(navigation)')}
            style={{ opacity: 0.5 }}
            title={t('account.reportBug')}
          />
          <List.Item
            left={(props) => (
              <List.Icon {...props} icon="information-outline" />
            )}
            onPress={() => router.push('(navigation)/about-app')}
            title={t('account.about')}
          />
          <List.Item
            left={(props) => (
              <List.Icon {...props} color={theme.colors.red} icon="logout" />
            )}
            onPress={() => setIsLogoutDialogVisible(true)}
            title={t('account.logout')}
            titleStyle={{ color: theme.colors.red }}
          />
        </Box>

        <ConfirmationDialog
          cancelButtonVariant="outlined"
          cancelLabel={t('logout.alert.cancel')}
          confirmButtonVariant="destructive"
          confirmLabel={t('logout.alert.confirm')}
          fixedHeight
          message={t('logout.alert.message')}
          onCancel={() => setIsLogoutDialogVisible(false)}
          onConfirm={async () => {
            setIsLogoutDialogVisible(false);
            try {
              await performLogout();
            } catch (_e) {
              // ignore
            }
          }}
          title={t('logout.alert.title')}
          visible={isLogoutDialogVisible}
        />

        <Text style={styles.footer}>Wersja 1.0.0</Text>
      </Box>
    </PageLayout>
  );
}

const styles = StyleSheet.create((theme) => ({
  backgroundContainer: {
    position: 'absolute',
    top: -240,
    left: 0,
    right: 0,
    height: '35%',
  },
  backgroundAvatar: {
    width: '100%',
    height: '100%',
    opacity: 0.6,
  },
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '100%',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 100,
    marginBottom: theme.margin(3),
    borderWidth: 4,
    borderColor: theme.colors.white,
    marginTop: theme.margin(8),
  },
  infoBoxesContainer: {
    gap: theme.gap(4),
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: theme.padding(2),
    marginBottom: theme.margin(8),
    boxShadow: '0px 4px 24px -8px rgba(13, 61, 50, 0.10)',
  },
  footer: {
    textAlign: 'center',
    marginTop: theme.margin(3),
    fontSize: 12,
    color: theme.colors.grey[500],
  },
}));
