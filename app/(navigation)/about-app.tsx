import SvgFacebookIcon from '@assets/facebook-icon.svg';
import SvgInstagramIcon from '@assets/instagram-icon.svg';
import SvgLinkedinIcon from '@assets/linkedin-icon.svg';
import SvgLogoRetiTextOnly from '@assets/logo-reti-text-only.svg';
import SvgTwitterIcon from '@assets/twitter-icon.svg';
import { useTranslations } from '@hooks';
import { Box, PageLayout, Text } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import type React from 'react';
import { Dimensions, Linking, TouchableOpacity, View } from 'react-native';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

const SocialIcon: React.FC<{
  children: React.ReactNode;
  onPress: () => void;
}> = ({ children, onPress }) => (
  <TouchableOpacity onPress={onPress} style={styles.socialIcon}>
    {children}
  </TouchableOpacity>
);

export default function AboutAppScreen() {
  const t = useTranslations('about-app');
  const { theme } = useUnistyles();
  const router = useRouter();

  const handleEmailPress = () => {
    Linking.openURL(`mailto:${t('contact.email')}`);
  };

  const handleTermsPress = () => {
    router.push('/(navigation)/terms-and-conditions');
  };

  const handlePrivacyPress = () => {
    router.push('/(navigation)/terms-and-conditions/term');
  };

  const handleReleaseNotesPress = () => {
    // TODO: Implement release notes functionality
  };

  const handleSocialPress = (_platform: string) => {
    // TODO: Implement social media links
  };

  const customGradients = (
    <>
      <View pointerEvents="none" style={StyleSheet.absoluteFill}>
        <Image
          source={require('@assets/about-app-bg.png')}
          style={styles.backgroundImage}
        />
      </View>
      <View pointerEvents="none" style={StyleSheet.absoluteFill}>
        <LinearGradient
          colors={['rgba(64, 197, 185, 0)', 'rgba(64, 197, 185, 0.15)']}
          locations={[0.4, 1]}
          style={StyleSheet.absoluteFill}
        />
      </View>
    </>
  );

  return (
    <PageLayout
      bgColor="grey.900"
      customBackgroundGradients={customGradients}
      noButtonBlur={true}
      noPadding
      noScroll={true}
      smallTitleAlwaysVisible
      statusBarOptions={{
        backgroundColor: '#131615',
        barStyle: 'light-content',
      }}
      title={t('title')}
      titleColor="white"
    >
      <Box align="center" mb={8} mt={20}>
        <View style={styles.logo}>
          <SvgLogoRetiTextOnly />
        </View>
        <Text color="white" mb={1} mt={4} size="md">
          {t('appInfo.version')}
        </Text>
        <Text color="grey.300" mb={12} size="sm">
          {t('appInfo.publisher')}
        </Text>
      </Box>

      <Box mb={10} px={4}>
        <View style={styles.versionContainer}>
          <Text color="white" size="lg" weight="medium">
            {t('version.label')}
          </Text>
          <TouchableOpacity onPress={handleReleaseNotesPress}>
            <Text
              color="primary.500"
              size="md"
              style={styles.underline}
              weight="medium"
            >
              {t('version.releaseNotes')}
            </Text>
          </TouchableOpacity>
        </View>
      </Box>

      <Box align="center" mb={4}>
        <Text color="grey.600" size="sm" textAlign="center">
          {t('copyright')}
        </Text>
      </Box>

      <Box align="center" mb={10}>
        <Box flexDirection="row" gap={6}>
          <SocialIcon onPress={() => handleSocialPress('facebook')}>
            <SvgFacebookIcon color={theme.colors.grey[500]} />
          </SocialIcon>
          <SocialIcon onPress={() => handleSocialPress('twitter')}>
            <SvgTwitterIcon color={theme.colors.grey[500]} />
          </SocialIcon>
          <SocialIcon onPress={() => handleSocialPress('instagram')}>
            <SvgInstagramIcon color={theme.colors.grey[500]} />
          </SocialIcon>
          <SocialIcon onPress={() => handleSocialPress('linkedin')}>
            <SvgLinkedinIcon color={theme.colors.grey[500]} />
          </SocialIcon>
        </Box>
      </Box>

      <Box align="center" mb={4}>
        <TouchableOpacity onPress={handleEmailPress}>
          <Text
            color="primary.500"
            mb={4}
            size="md"
            style={styles.underline}
            textAlign="center"
            weight="medium"
          >
            {t('contact.email')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleTermsPress}>
          <Text
            color="primary.500"
            mb={4}
            size="md"
            style={styles.underline}
            textAlign="center"
            weight="medium"
          >
            {t('contact.terms')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handlePrivacyPress}>
          <Text
            color="primary.500"
            size="md"
            style={styles.underline}
            textAlign="center"
            weight="medium"
          >
            {t('contact.privacy')}
          </Text>
        </TouchableOpacity>
      </Box>
    </PageLayout>
  );
}

const styles = StyleSheet.create(() => ({
  backgroundImage: {
    width: screenWidth,
    height: 144,
    marginTop: 88,
  },
  logo: {
    width: 130,
    height: 40,
    marginTop: 64,
    resizeMode: 'contain',
  },
  versionContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: 'rgba(13, 61, 50, 0.1)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 1,
    shadowRadius: 24,
    elevation: 8,
  },
  underline: {
    textDecorationLine: 'underline',
  },
  socialIcon: {
    width: 32,
    height: 32,
    marginHorizontal: 8,
    opacity: 0.7,
  },
}));
