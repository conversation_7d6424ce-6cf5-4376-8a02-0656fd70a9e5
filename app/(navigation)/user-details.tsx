import CameraIcon from '@assets/camera-icon.svg';
import HourglassIcon from '@assets/hourglass-icon.svg';
import { VeriffAlert } from '@components';
import {
  getProfileInfo,
  type ProfileInfoResponse,
} from '@endpoints/user.endpoints';
import { useTranslations } from '@hooks';
import useVeriffStore, { VerificationStatus } from '@store/veriff.store';
import { useQuery } from '@tanstack/react-query';
import { Box, PageLayout, Skeleton, Text, TextInput } from '@ui';
import { getQueryData } from '@utils/api';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useRouter } from 'expo-router';
import { useEffect, useMemo } from 'react';
import { StatusBar, TouchableOpacity } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

const UserDetails = () => {
  const router = useRouter();
  const t = useTranslations();
  const { verificationStatus, pollVerificationStatus } = useVeriffStore();
  const { data: profileInfo, isLoading: isLoadingProfileInfo } = useQuery(
    getQueryData<ProfileInfoResponse>(getProfileInfo)
  );

  useEffect(() => {
    if (verificationStatus === VerificationStatus.PENDING) {
      pollVerificationStatus();
    }
  }, [verificationStatus, pollVerificationStatus]);

  const verificationAlert = useMemo(() => {
    switch (verificationStatus) {
      case VerificationStatus.LOADING:
        return (
          <VeriffAlert
            icon={<HourglassIcon height={17} width={17} />}
            title={t('kyc.status.loading')}
            type="info"
          />
        );
      case VerificationStatus.PENDING:
        return (
          <VeriffAlert
            icon={<HourglassIcon height={17} width={17} />}
            subtitle={t('kyc.status.submitted')}
            title={t('kyc.status.inProgress')}
            type="info"
          />
        );
      case VerificationStatus.FAILED:
        return <VeriffAlert title={t('kyc.status.failed')} type="error" />;
      case VerificationStatus.TIMEOUT:
        return <VeriffAlert title={t('kyc.status.failed')} type="warning" />;
      case VerificationStatus.CANCELLED:
        return <VeriffAlert title={t('kyc.status.cancelled')} type="warning" />;
      default:
        return null;
    }
  }, [verificationStatus, t]);

  const handleVerificationPress = () => {
    if (
      verificationStatus === VerificationStatus.COMPLETED ||
      verificationStatus === VerificationStatus.PENDING
    ) {
      return;
    }

    router.push('/(id-verification)/verification-entry');
  };

  return (
    <PageLayout
      bgColor="white"
      description={t('userDetails.subtitle')}
      title={t('userDetails.title')}
    >
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      <Box gap={6} mt={4}>
        {verificationStatus !== VerificationStatus.NOT_STARTED && (
          <TouchableOpacity
            activeOpacity={
              verificationStatus === VerificationStatus.COMPLETED ? 1 : 0.7
            }
            disabled={
              verificationStatus === VerificationStatus.COMPLETED ||
              verificationStatus === VerificationStatus.LOADING
            }
            onPress={handleVerificationPress}
          >
            {verificationAlert}
          </TouchableOpacity>
        )}
        <Box style={styles.profilePictureContainer}>
          <Image
            source={require('@assets/test-profile-picture.png')}
            style={styles.profilePicture}
          />
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              // TODO: Implement camera functionality
            }}
            style={styles.cameraButton}
          >
            <CameraIcon height={12} width={12} />
          </TouchableOpacity>
        </Box>
        {isLoadingProfileInfo ? (
          [...new Array(3)].map((_, index) => (
            <Skeleton key={index} style={{ width: '100%', height: 24 }} />
          ))
        ) : (
          <Box gap={4}>
            {profileInfo?.profile ? (
              <>
                <TextInput
                  editable={false}
                  inputContainerStyle={styles.readOnlyInput}
                  label={t('userDetails.firstName')}
                  value={profileInfo.profile.first_and_middle_name}
                />

                <TextInput
                  editable={false}
                  inputContainerStyle={styles.readOnlyInput}
                  label={t('userDetails.lastName')}
                  value={profileInfo.profile.last_name}
                />

                <TextInput
                  editable={false}
                  inputContainerStyle={styles.readOnlyInput}
                  label={t('userDetails.pesel')}
                  value={profileInfo.profile.personal_number}
                />

                {/* <TextInput
                editable={false}
                inputContainerStyle={styles.readOnlyInput}
                label={t('userDetails.phone')}
                value={profileInfo.profile.phone}
              /> */}

                <TextInput
                  editable={false}
                  inputContainerStyle={styles.readOnlyInput}
                  label={t('userDetails.birthDate')}
                  value={new Date(
                    profileInfo.profile.date_of_birth
                  ).toLocaleDateString()}
                />
              </>
            ) : (
              <>
                <TextInput
                  editable={false}
                  inputContainerStyle={styles.readOnlyInput}
                  label={t('userDetails.email')}
                  value={profileInfo?.username}
                />

                <Text color="grey.500" size="lg" textAlign="center">
                  {t('userDetails.noProfile')}
                </Text>
              </>
            )}
          </Box>
        )}
      </Box>
    </PageLayout>
  );
};

const styles = StyleSheet.create((theme) => ({
  profilePictureContainer: {
    position: 'relative',
    width: 100,
    height: 100,
  },
  profilePicture: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    backgroundColor: theme.colors.grey[50],
    borderColor: theme.colors.grey[100],
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.grey[50],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.grey[100],
  },

  readOnlyInput: {
    backgroundColor: theme.colors.grey[50],
  },
}));

export default UserDetails;
