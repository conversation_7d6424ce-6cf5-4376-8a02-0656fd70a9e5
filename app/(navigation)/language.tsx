import FlagEN from '@assets/flags/flag_en.svg';
import FlagPL from '@assets/flags/flag_pl.svg';
import { useTranslations } from '@hooks';
import { Box, ConfirmationDialog, PageLayout, Text } from '@ui';
import i18n from '@utils/i18next';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { Pressable } from 'react-native';
import cacheService from 'services/cache.service';
import { CACHE_KEYS, CACHE_TTL } from 'types/cache.types';

export default function LanguageSelectionScreen() {
  const tNav = useTranslations('navigation');
  const tCommon = useTranslations('common');

  type LanguageCode = 'en' | 'pl';
  type LanguageItem = {
    code: LanguageCode;
    name: string;
    Flag: React.ComponentType<{ width?: number; height?: number }>;
  };

  const languages: LanguageItem[] = useMemo(
    () => [
      {
        code: 'en',
        name: tCommon('languages.en'),
        Flag: FlagEN,
      },
      {
        code: 'pl',
        name: tCommon('languages.pl'),
        Flag: FlagPL,
      },
    ],
    [tCommon]
  );

  const currentLanguage = (
    i18n.language === 'pl' ? 'pl' : 'en'
  ) as LanguageCode;
  const [selected, setSelected] = useState<LanguageCode>(currentLanguage);

  const [pendingLang, setPendingLang] = useState<LanguageCode | null>(null);
  const [isDialogVisible, setDialogVisible] = useState(false);

  useEffect(() => {
    setSelected(currentLanguage);
  }, [currentLanguage]);

  const startChange = useCallback(
    (code: LanguageCode) => {
      setSelected(code);
      if (code !== currentLanguage) {
        setPendingLang(code);
        setDialogVisible(true);
      }
    },
    [currentLanguage]
  );

  const onConfirmChange = useCallback(async () => {
    if (!pendingLang || pendingLang === currentLanguage) {
      setDialogVisible(false);
      return;
    }

    try {
      await i18n.changeLanguage(pendingLang);
      const existing =
        (await cacheService.get<any>(CACHE_KEYS.USER_PREFERENCES)) || {};
      await cacheService.set(
        CACHE_KEYS.USER_PREFERENCES,
        { ...existing, language: pendingLang },
        CACHE_TTL.USER_PREFERENCES
      );
    } finally {
      setDialogVisible(false);
    }
  }, [pendingLang, currentLanguage]);

  const onCancelChange = useCallback(() => {
    setSelected(currentLanguage);
    setDialogVisible(false);
  }, [currentLanguage]);

  return (
    <PageLayout
      contentMarginTop={4}
      description={tNav('languageSubtitle')}
      title={tNav('account.language')}
    >
      <Box gap={4}>
        {languages.map((lang) => {
          const isActive = lang.code === currentLanguage;
          const isSelected = lang.code === selected;
          const Flag = lang.Flag;
          return (
            <Pressable
              accessibilityRole="radio"
              accessibilityState={{ checked: isSelected }}
              key={lang.code}
              onPress={() => startChange(lang.code)}
            >
              <Box
                align="center"
                flexDirection="row"
                justify="between"
                p={3}
                radius={12}
              >
                <Box
                  align="center"
                  flexDirection="row"
                  gap={3}
                  style={{ flex: 1 }}
                >
                  <Flag height={28} width={28} />
                  <Text flex={1} size="lg">
                    {lang.name}
                  </Text>
                  {isActive && (
                    <Box bgColor="dark" mr={2} px={3} py={1} radius={100}>
                      <Text color="white" size="sm">
                        {tCommon('inUse', {
                          defaultValue: 'Aktualnie w użyciu',
                        })}
                      </Text>
                    </Box>
                  )}
                </Box>
                <Box
                  align="center"
                  bgColor={isSelected ? 'primary.100' : 'white'}
                  borderColor="grey.200"
                  borderWidth={1}
                  h={24}
                  justify="center"
                  radius={12}
                  w={24}
                >
                  <Box
                    bgColor={isSelected ? 'primary.600' : 'white'}
                    h={12}
                    radius={6}
                    w={12}
                  />
                </Box>
              </Box>
            </Pressable>
          );
        })}
      </Box>

      <ConfirmationDialog
        message={tCommon('languageChange.description', {
          language:
            selected === 'en'
              ? tCommon('languages.en')
              : tCommon('languages.pl'),
        })}
        onCancel={onCancelChange}
        onConfirm={onConfirmChange}
        title={tCommon('languageChange.title', {
          language:
            selected === 'en'
              ? tCommon('languages.en')
              : tCommon('languages.pl'),
        })}
        visible={isDialogVisible}
      />
    </PageLayout>
  );
}
