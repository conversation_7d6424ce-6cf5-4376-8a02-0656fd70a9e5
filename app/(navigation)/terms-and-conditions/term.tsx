import { getQueryData } from '@api';
import { getTerms, type TermsOfUseResponse } from '@endpoints/terms.endpoints';
import { useTranslations } from '@hooks';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@ui';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback } from 'react';
import Markdown from 'react-native-markdown-display';

export default function TermsAndConditionsViewScreen() {
  const tCommon = useTranslations('common');
  const router = useRouter();
  const params = useGlobalSearchParams();

  const { data: termsData } = useQuery(
    getQueryData<TermsOfUseResponse>(getTerms, {
      fetcherOptions: { ignoreError: 403 },
      queryOptions: { staleTime: 60_000 },
    })
  );

  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  const agreement = termsData?.agreements?.find(
    (element) =>
      element.id === Number.parseInt(params.agreementId as string, 10)
  );

  return (
    <PageLayout
      bgColor="background"
      buttonProps={{
        variant: 'muted',
      }}
      buttonText={tCommon('common.back')}
      contentMarginTop={6}
      onButtonPress={handleBackPress}
      title={agreement?.name || ''}
    >
      <Markdown style={{}}>{agreement?.content}</Markdown>
    </PageLayout>
  );
}
