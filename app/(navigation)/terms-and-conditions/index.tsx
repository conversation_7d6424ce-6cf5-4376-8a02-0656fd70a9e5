import { getQueryData } from '@api';
import { TermsList } from '@components';
import { getTerms, type TermsOfUseResponse } from '@endpoints/terms.endpoints';
import { useTranslations } from '@hooks';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@ui';
import { useRouter } from 'expo-router';
import { useCallback } from 'react';

export default function TermsAndConditionsScreen() {
  const t = useTranslations('navigation');
  const router = useRouter();

  const { data: termsData } = useQuery(
    getQueryData<TermsOfUseResponse>(getTerms, {
      fetcherOptions: { ignoreError: 403 },
      queryOptions: { staleTime: 60_000 },
    })
  );

  const handleAgreementPress = useCallback(
    (agreement: any) => {
      router.push({
        pathname: '/(navigation)/terms-and-conditions/term',
        params: { agreementId: agreement.id },
      });
    },
    [router]
  );

  return (
    <PageLayout
      bgColor="background"
      contentGap={6}
      description={t('termsAndConditions.description')}
      title={t('termsAndConditions.title')}
    >
      {termsData?.agreements && (
        <TermsList
          agreements={termsData.agreements}
          onAgreementPress={handleAgreementPress}
        />
      )}
    </PageLayout>
  );
}
