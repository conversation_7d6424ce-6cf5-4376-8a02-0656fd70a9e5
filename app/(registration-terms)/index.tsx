import { fetchAPI, getQueryData } from '@api';
import { TermsList } from '@components';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import { createAccount, resendCode } from '@endpoints/registration.endpoints';
import {
  type Agreement,
  getTerms,
  postTerms,
  type TermsOfUseResponse,
} from '@endpoints/terms.endpoints';
import { useTranslations } from '@hooks';
import useErrorStore from '@store/error.store';
import useRegistrationStore from '@store/registration.store';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Box, Checkbox, Divider, PageLayout } from '@ui';
import { useRouter } from 'expo-router';
import { setItem as setItemSecureStore } from 'expo-secure-store';
import { useCallback, useEffect } from 'react';
import { ErrorKey } from 'types/error.types';

export default () => {
  const router = useRouter();
  const t = useTranslations();

  const {
    username,
    password,
    setAgreements,
    setAgreement,
    setAllChecked,
    agreements,
    allChecked,
  } = useRegistrationStore();
  const { addError } = useErrorStore();

  const { data: termsData } = useQuery(
    getQueryData<TermsOfUseResponse>(getTerms, {
      fetcherOptions: { ignoreError: 403 },
      queryOptions: { staleTime: 60_000 },
    })
  );

  useEffect(() => {
    if (termsData) {
      setItemSecureStore(SecureStorageKeys.TERMS_VERSION, termsData.version);
      if (termsData?.agreements) {
        setAgreements(termsData.agreements);
      }
    }
  }, [setAgreements, termsData]);

  const setAllCheckedFields = useCallback(() => {
    setAllChecked();
  }, [setAllChecked]);

  const requiredChecked = agreements?.length
    ? agreements?.every((agreement) =>
        agreement.required ? agreement.checked === true : true
      )
    : true;

  const { mutate: registerAccount, isPending: loading } = useMutation({
    mutationFn: () =>
      fetchAPI(createAccount, {
        data: { username, password },
        fetcherOptions: { fullResponse: true },
      }),
    onSuccess: () => {
      const agreementsToPost = (agreements || []).map((agreement) => ({
        id: agreement.id,
        accepted: !!agreement.checked,
      }));

      fetchAPI(postTerms, {
        data: { agreements: agreementsToPost, username },
        fetcherOptions: {},
      });
      fetchAPI(resendCode, { data: { username } }).finally(() => {
        setTimeout(() => {
          router.replace({
            pathname: '(confirmation)',
            params: { username },
          });
        }, 500);
      });
    },
    onError: (error: unknown) => {
      const err = error as {
        response?: { data?: Record<string, unknown> };
        status?: number;
        username?: any;
      };
      const errorMessage: string | undefined = Object.values(
        err?.response?.data ?? {}
      )?.[0]?.toString();

      if (errorMessage) {
        addError(ErrorKey.RegistrationError, {
          key: ErrorKey.RegistrationError,
          errorText: errorMessage,
          status: err?.status,
          translate: false,
        });
      } else {
        addError(ErrorKey.RegistrationError, {
          key: ErrorKey.RegistrationError,
          errorText: 'messages.registrationError',
          status: err?.status,
          translate: true,
        });
      }

      if (err?.username) {
        return router.replace({
          pathname: '/activation-code',
          params: { username },
        });
      }

      setTimeout(() => {
        router.replace({
          pathname: '/init-screen',
          params: { username },
        });
      }, 3500);
    },
  });

  const handleAgreementPress = useCallback(
    (agreement: Agreement) => {
      router.push({
        pathname: '/(registration-terms)/term',
        params: { agreementId: agreement.id },
      });
    },
    [router]
  );

  return (
    <PageLayout
      bgColor="background"
      buttonProps={{
        disabled: !requiredChecked,
        loading,
      }}
      buttonText={t('signUp.buttons.signUp.label')}
      contentGap={6}
      onButtonPress={() => registerAccount()}
      title={t('termsOfUse.title')}
    >
      {termsData?.agreements && (
        <TermsList
          agreements={termsData.agreements}
          onAgreementPress={handleAgreementPress}
        />
      )}
      <Box flex={3} mt={7.5}>
        <Checkbox
          initialChecked={false}
          label="Zaznacz wszystkie zgody"
          onPress={setAllCheckedFields}
          value={allChecked}
        />
        <Divider />
        {agreements && (
          <Box ml={4}>
            {agreements?.map((agreement) => {
              return (
                <Checkbox
                  initialChecked={agreement.checked ?? false}
                  key={`${agreement.id}-${agreement.name}`}
                  label={`${agreement.label}`}
                  onPress={(val) => {
                    setAgreement(agreement.name, val);
                  }}
                  required={agreement.required}
                />
              );
            })}
          </Box>
        )}
      </Box>
    </PageLayout>
  );
};
