import { useTranslations } from '@hooks';
import useRegistrationStore from '@store/registration.store';
import { PageLayout } from '@ui';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback } from 'react';
import Markdown from 'react-native-markdown-display';

export default () => {
  const router = useRouter();
  const t = useTranslations();

  const onBack = useCallback(() => {
    router.back();
  }, [router]);

  const params = useGlobalSearchParams();

  const { agreements } = useRegistrationStore();

  const agreement = agreements?.find(
    (element) =>
      element.id === Number.parseInt(params.agreementId as string, 10)
  );

  return (
    <PageLayout
      bgColor="background"
      buttonProps={{
        variant: 'muted',
      }}
      buttonText={t('common.back')}
      contentMarginTop={6}
      onButtonPress={onBack}
      title={agreement?.name || ''}
    >
      <Markdown>{agreement?.content}</Markdown>
    </PageLayout>
  );
};
