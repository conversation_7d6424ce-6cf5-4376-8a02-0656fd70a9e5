<a name="readme-top"></a>

<div align="center">
   <img src="https://avatars.githubusercontent.com/u/120906660?s=200&v=4" alt="Logo" width='200' style="border-radius:50px">
   <h2 align="center">RETI</h1>
   <br />
</div>

<details open="open">
  <summary>Table of Contents</summary>
  <ol>
   <li><a href="#prerequisites">Prerequisites</a></li>
   <li><a href="#installation">Installation</a></li>
   <li><a href="#running-the-app">Running the App</a></li> 
  </ol>
</details>

 <br />

## Prerequisites

Before starting, ensure you have the following installed:
- **Node.js** installed on your machine.
- **npm** or **yarn** (comes with Node.js)
- **Expo CLI** (command-line tool for Expo)
- **Android Studio** (for being able to use Android Emulators) OR **Xcode** (for being able to use iOS Simulators)
- **Expo Go App** (installed on your physical Android/iOS device)


<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Installation

1. Clone the repository:

   ```
   git clone https://github.com/RETI-DevTeam/reti-frontend-native.git
   ```

2. Navigate to the project directory:

   ```
   cd reti-frontend-native
   ```

3. Install the dependencies:

   ```
   npm install
   ```

4. Start the development server:

   ```
   npm start
   ```

<p align="right">(<a href="#readme-top">back to top</a>)</p>


## Running the App

1. Running on a Physical Android/iOS Device:

   - Install Expo Go from the Google Play Store or Apple App Store.
   - Connect your device to the same Wi-Fi network as your computer.
   - Open Expo Go and scan the QR code shown in the terminal (after running npm start command)
   - Your app should now load and run on your device.
 <br />

2. Running on Android Emulator:

   **Setup Android Emulator (AVD)**
   - Install Android Studio
   - Open Android Studio and go to Tools > AVD Manager > Create Virtual Device
   - Choose a device model and install the necessary system image (API 30+ recommended)
   - Start the emulator


   To run the app on the emulator, open a new terminal and run:
   ```
   npm start
   ```
   Then press a to open it on the Android emulator.
 <br />

3. Running on iOS Simulator:

   **Setup iOS Simulator (Mac only)**
   - Install Xcode from the Mac App Store
   - Open Xcode and go to Preferences > Components to install iOS simulators
   - Start the simulator


   To run the app on the simmulator, open a new terminal and run:
   ```
   npm start
   ```
   Then press i to open it on the iOS simulator.


<p align="right">(<a href="#readme-top">back to top</a>)</p>


