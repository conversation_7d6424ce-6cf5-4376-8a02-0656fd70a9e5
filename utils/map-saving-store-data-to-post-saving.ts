import { PlanType, type SavingPlanStoreProps } from "@store/saving-plan.store";
import { mapFrequencyToPurchaseInterval } from "./map-frequency-to-purchase-interval";

export const mapSavingStoreDataToPostSaving = (
  savingPlanData: SavingPlanStoreProps
) => {
  const initialData = {
    name: savingPlanData.planName,
    plan_type: savingPlanData.selectedPlan,
    time_horizon: savingPlanData.timeHorizon,
    saving_enabled: true,
    target_id: savingPlanData.selectedTarget?.id,
  };

  let postData: Record<string, unknown>;

  switch (savingPlanData.selectedPlan) {
    case PlanType.Amount:
      postData = {
        ...initialData,
        percentage_value: undefined,
        amount_value: savingPlanData.fixedPlanValue,
        ignore_transaction_above: savingPlanData.ignoreTransactionsAbove,
      };
      break;
    case PlanType.Percentage:
      postData = {
        ...initialData,
        percentage_value: savingPlanData.adjustedPlanPercentage,
        ignore_transaction_above: savingPlanData.ignoreTransactionsAbove,
      };
      break;
    case PlanType.ReccuringPurchase:
      postData = {
        ...initialData,
        amount_value: savingPlanData.savingAmountAndFrequency.amount,
        purchase_interval: mapFrequencyToPurchaseInterval(
          savingPlanData?.savingAmountAndFrequency?.frequency ?? "W"
        ),
        percentage_value: undefined,
      };
      break;
    default:
      throw new Error("Invalid plan type");
  }
  return postData;
};
