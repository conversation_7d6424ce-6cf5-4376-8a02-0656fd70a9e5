import type { UseQueryOptions } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import { type FetcherOptions, fetcher } from './fetcher';
import { setParams, setSlugs } from './helpers';
import type { Endpoint } from './types';

export type PrepareDataOptions<T> = {
  slugs?: { [key: string]: string | undefined };
  dynamicKeys?: unknown[];
  params?: { [key: string]: string | undefined };
  data?: unknown;
  fetcherOptions?: FetcherOptions & {
    dependencies?: { slugs?: string[]; params?: string[] };
  };
  queryOptions?: Omit<Omit<UseQueryOptions<T>, 'queryKey'>, 'queryFn'>;
};

/**
 * Prepares data for an API request by constructing the route and keys.
 *
 * @template T The expected type of the data.
 * @param {string} key The base key for the request.
 * @param {string} route The base route for the request.
 * @param {PrepareDataOptions<T>} [options] The options for preparing the data.
 * @returns {{ keys: string[]; route: string }} An object containing the prepared keys and route.
 */
const prepareData = <T>(
  key: string,
  route: string,
  options?: PrepareDataOptions<T>
) => {
  const { slugs, dynamicKeys, params } = options || {};
  const keys: string[] = [key];

  let newRoute = route;

  if (slugs) {
    newRoute = setSlugs(newRoute, slugs);
  }
  if (dynamicKeys) {
    keys.push(...(dynamicKeys as string[]));
  }
  if (params) {
    newRoute = setParams(newRoute, params);
  }

  return {
    keys,
    route: newRoute,
  };
};

function checkDependencies<T>(
  options: Omit<PrepareDataOptions<T>, 'queryOptions'> | undefined,
  dependencies: { slugs?: string[]; params?: string[] }
) {
  const error = { error: 'Missing dependency', status: 400 };
  for (const depKey of Object.keys(dependencies)) {
    const key = depKey as 'params' | 'slugs';
    const dep = dependencies[key] as string[];
    if (dep) {
      for (const depValue of dep) {
        if (!options?.[key]?.[depValue]) {
          throw error;
        }
      }
    }
  }
}

/**
 * Fetches data from a specified endpoint.
 *
 * @template T The expected type of the response data.
 * @param {Endpoint} endpoint The endpoint to fetch data from.
 * @param {Omit<PrepareDataOptions<T>, "queryOptions"> & { fetcherOptions: FetcherOptions & { fullResponse: true }; }} options The options for the request.
 * @returns {Promise<AxiosResponse<T>>} A promise that resolves to the full Axios response.
 */
export function fetchAPI<T = unknown>(
  endpoint: Endpoint,
  options: Omit<PrepareDataOptions<T>, 'queryOptions'> & {
    fetcherOptions: FetcherOptions & { fullResponse: true };
  }
): Promise<AxiosResponse<T>>;
/**
 * Fetches data from a specified endpoint.
 *
 * @template T The expected type of the response data.
 * @param {Endpoint} endpoint The endpoint to fetch data from.
 * @param {Omit<Omit<PrepareDataOptions<T>, "queryOptions">, "fetcherOptions"> & { fetcherOptions?: Omit<FetcherOptions, "fullResponse"> | (FetcherOptions & { fullResponse?: false | undefined }); }} [options] The options for the request.
 * @returns {Promise<T>} A promise that resolves to the response data.
 */
export function fetchAPI<T = unknown>(
  endpoint: Endpoint,
  options?: Omit<
    Omit<PrepareDataOptions<T>, 'queryOptions'>,
    'fetcherOptions'
  > & {
    fetcherOptions?:
      | Omit<FetcherOptions, 'fullResponse'>
      | (FetcherOptions & { fullResponse?: false | undefined });
  }
): Promise<T>;
/**
 * Fetches data from a specified endpoint.
 *
 * @template T The expected type of the response data.
 * @param {Endpoint} endpoint The endpoint to fetch data from.
 * @param {Omit<PrepareDataOptions<T>, "queryOptions">} [options] The options for the request.
 * @returns {Promise<T | AxiosResponse<T>>} A promise that resolves to the response data or the full Axios response.
 */
export function fetchAPI<T = unknown>(
  endpoint: Endpoint,
  options?: Omit<PrepareDataOptions<T>, 'queryOptions'>
): Promise<T | AxiosResponse<T>> {
  const { route } = prepareData(endpoint.key, endpoint.route, options);
  const { data, fetcherOptions } = options || {};
  const { dependencies } = fetcherOptions || {};

  if (dependencies) {
    checkDependencies(options, dependencies);
  }

  return fetcher<T>(endpoint.method, fetcherOptions, route, data);
}

/**
 * Prepares query data for use with a query library like React Query.
 *
 * @template T The expected type of the data.
 * @template D The expected type of the query options.
 * @param {Endpoint} endpoint The endpoint to fetch data from.
 * @param {PrepareDataOptions<T>} [options] The options for preparing the data.
 * @returns {D} The prepared query data.
 */
export function getQueryData<T, D = UseQueryOptions<T, AxiosError, T>>(
  endpoint: Endpoint,
  options?: PrepareDataOptions<T>
): D {
  const { keys, route } = prepareData(endpoint.key, endpoint.route, options);
  const { data, fetcherOptions, queryOptions } = options || {};

  return {
    ...queryOptions,
    queryKey: keys,
    queryFn: () => fetcher<T>(endpoint.method, fetcherOptions, route, data),
  } as D;
}
