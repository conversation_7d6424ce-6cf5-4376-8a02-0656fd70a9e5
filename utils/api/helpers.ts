import type { Method } from 'axios';
import type { Endpoint } from './types';

export const setSlugs = (
  url: string,
  data: Record<string, string | number | boolean | undefined>
): string =>
  Object.keys(data).reduce(
    (acc, key) =>
      acc.replace(
        `:${key}`,
        encodeURIComponent(data[key] as string | number | boolean)
      ),
    url
  );

export const setParams = (
  url: string,
  data?:
    | Record<string, string | undefined>
    | string[][]
    | URLSearchParams
    | string
): string =>
  `${url}?${new URLSearchParams(data as Record<string, string>).toString()}`;

export const createEndpoint = ({
  method,
  route,
}: {
  method: Method;
  route: string;
}): Endpoint => ({
  key: `${method}-${route.replaceAll('/', '')}`,
  method,
  route,
});
