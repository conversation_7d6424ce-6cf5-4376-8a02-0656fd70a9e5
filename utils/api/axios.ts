import useAuthStore from "@store/auth.store";
import axios, { type AxiosError, type AxiosRequestConfig } from "axios";
import { router } from "expo-router";
import { AuthState } from "types/auth.types";
import { clearRefreshToken, clearToken, loadToken } from "../token";
import { refreshToken } from "./refresh-token";

const BASE_URL = process.env.EXPO_PUBLIC_BASE_URL;
// const BASE_URL = "https://dev.reti.tech";
const API_PREFIX = process.env.EXPO_PUBLIC_API_PREFIX;

const NO_AUTH_ENDPOINTS = [
  { path: "/token/" },
  { path: "/refresh/" },
  { path: "/register/" },
  { path: "/confirm-registration-code/" },
  { path: "/reset-password/" },
  { path: "/reset-password-confirm/" },
  { path: "/token/refresh/" },
  { path: "/terms-and-conditions/", method: "get" },
];
const compareMethod = (method1?: string, method2?: string) => {
  return method1?.toLowerCase() === method2?.toLowerCase();
};

const DEFAULT_HEADERS = {
  language: "PL",
};

const axiosInstance = axios.create({
  baseURL: `${BASE_URL}/${API_PREFIX}`,
  headers: DEFAULT_HEADERS,
});

axiosInstance.interceptors.request.use(
  async (config) => {
    // Skip token validation for endpoints that don't require authentication
    const isNoAuthEndpoint = NO_AUTH_ENDPOINTS.some(
      (endpoint) =>
        config.url?.includes(endpoint.path) &&
        compareMethod(config.method, endpoint.method)
    );

    if (!isNoAuthEndpoint) {
      const token = await loadToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      } else {
        // No token available for protected endpoint - this will likely result in 401/418
        // biome-ignore lint/suspicious/noConsole: debug logging for missing token
        console.warn("No token available for protected endpoint:", config.url);
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * Helper function to handle authentication failures and redirect to init screen
 */
const handleAuthenticationFailure = async (error: AxiosError) => {
  // biome-ignore lint/suspicious/noConsole: debug logging for auth failures
  console.error("Authentication failure detected:", {
    status: error.response?.status,
    data: error.response?.data,
    url: error.config?.url,
  });

  // Clear all authentication data
  await clearToken();
  await clearRefreshToken();

  // Update auth store state
  const { setAuthState, setToken } = useAuthStore.getState();
  setAuthState(AuthState.Unauthenticated);
  setToken(null);

  // Redirect to init screen
  // TODO figure out wth happens here
  try {
    router.replace("/sign-in");
  } catch (routerError) {
    // biome-ignore lint/suspicious/noConsole: fallback error logging
    console.error("Failed to redirect to init screen:", routerError);
  }
};

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
      url?: string;
      headers?: Record<string, string>;
    };

    // Handle missing token header (418)
    if (
      error.response?.status === 418 &&
      originalRequest.url &&
      !NO_AUTH_ENDPOINTS.some(
        (endpoint) =>
          originalRequest.url!.includes(endpoint.path) &&
          compareMethod(originalRequest.method, endpoint.method)
      )
    ) {
      await handleAuthenticationFailure(error);
      return Promise.reject(error);
    }

    // Handle standard 401 unauthorized with token refresh
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      originalRequest.url &&
      !NO_AUTH_ENDPOINTS.some(
        (endpoint) =>
          originalRequest.url!.includes(endpoint.path) &&
          compareMethod(originalRequest.method, endpoint.method)
      )
    ) {
      originalRequest._retry = true;

      try {
        const refreshedTokenResponse = await refreshToken();

        if (!refreshedTokenResponse) {
          // Token refresh failed, handle as authentication failure
          await handleAuthenticationFailure(error);
          return Promise.reject(error);
        }

        originalRequest.headers!.Authorization = `Bearer ${refreshedTokenResponse.access}`;

        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // Token refresh failed, handle as authentication failure
        await handleAuthenticationFailure(error);
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
