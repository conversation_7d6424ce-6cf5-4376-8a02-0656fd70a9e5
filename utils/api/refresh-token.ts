import axios from 'axios';
import type { ITokenResponse } from 'types/auth.types';
import {
  clearRefreshToken,
  clearToken,
  loadRefreshToken,
  saveRefreshToken,
  saveToken,
} from '../token';

const BASE_URL = process.env.EXPO_PUBLIC_BASE_URL;

export async function refreshToken(): Promise<ITokenResponse> {
  const refreshTokenValue = await loadRefreshToken();
  if (!refreshTokenValue) {
    await clearToken();
    await clearRefreshToken();
    return undefined as unknown as ITokenResponse;
  }

  const refreshInstance = axios.create();

  try {
    const response = await refreshInstance.post<ITokenResponse>(
      `${BASE_URL}/api/token/refresh/`,
      {
        refresh: refreshTokenValue,
      }
    );
    await saveToken(response.data.access);
    await saveRefreshToken(response.data.refresh);
    return response.data;
  } catch (error) {
    await clearToken();
    await clearRefreshToken();
    // biome-ignore lint/suspicious/noConsole: debug
    console.error('Error refreshing token:', error);
    throw error;
  }
}
