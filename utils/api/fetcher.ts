// biome-ignore-all lint: .
import axios, { type AxiosError, type AxiosResponse } from "axios";

import axiosInstance from "./axios";

const DEVELOPMENT_DEBUG = false;

export interface FetcherOptions {
  baseURL?: string;
  fullResponse?: boolean;
  timeout?: number;
  isBlob?: boolean;
  headers?: { [key: string]: string };
  ignoreError?: number[] | number;
}

/**
 * A generic data fetching function that uses Axios to make HTTP requests.
 * It can return either the full Axios response or just the data part of it.
 *
 * @template T The expected type of the response data.
 * @param method The HTTP method (e.g., 'GET', 'POST').
 * @param options Configuration for the fetcher.
 * @param options.fullResponse If true, returns the full Axios response. Otherwise, returns only the response data.
 * @param url The URL for the request.
 * @param data The data to be sent with the request body.
 * @returns A promise that resolves to the response data or the full Axios response.
 */
export function fetcher<T = any>(
  method: string,
  options: FetcherOptions & { fullResponse: true },
  url?: string,
  data?: any
): Promise<AxiosResponse<T>>;
/**
 * A generic data fetching function that uses Axios to make HTTP requests.
 * It can return either the full Axios response or just the data part of it.
 *
 * @template T The expected type of the response data.
 * @param method The HTTP method (e.g., 'GET', 'POST').
 * @param options Configuration for the fetcher.
 * @param url The URL for the request.
 * @param data The data to be sent with the request body.
 * @returns A promise that resolves to the response data or the full Axios response.
 */
export function fetcher<T = any>(
  method: string,
  options?:
    | Omit<FetcherOptions, "fullResponse">
    | (FetcherOptions & { fullResponse?: false | undefined }),
  url?: string,
  data?: any
): Promise<T>;
/**
 * A generic data fetching function that uses Axios to make HTTP requests.
 * It can return either the full Axios response or just the data part of it.
 *
 * @template T The expected type of the response data.
 * @param method The HTTP method (e.g., 'GET', 'POST').
 * @param options Configuration for the fetcher.
 * @param url The URL for the request.
 * @param data The data to be sent with the request body.
 * @returns A promise that resolves to the response data or the full Axios response.
 */
export async function fetcher<T = any>(
  method: string,
  options?: FetcherOptions,
  url?: string,
  data?: any
): Promise<T | AxiosResponse<T>> {
  const { baseURL, fullResponse, timeout, isBlob, headers } = options || {};

  const config: any = {
    method,
    url,
    data,
    timeout: timeout || -1,
    headers: headers || undefined,
  };

  if (isBlob) {
    config.headers = {
      ...config.headers,
      language: "PL",
      "Content-Type": "multipart/form-data",
    };
  }

  if (baseURL) {
    config.baseURL = baseURL;
  }

  try {
    const response = await axiosInstance.request<T>(config);

    if (DEVELOPMENT_DEBUG) {
      console.log(
        "\n\n===============================\n        FETCHER CONFIG\n",
        JSON.stringify(config, null, 2),
        "\n-------------------------------\n        RESPONSE HEADERS\n",
        JSON.stringify(response.headers, null, 2),
        "\n===============================\n\n"
      );
    }

    return fullResponse ? response : response.data;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (options?.ignoreError) {
      const response = axiosError.response;

      if (Array.isArray(options.ignoreError)) {
        if (options.ignoreError.includes(axiosError.status || 0)) {
          return fullResponse
            ? (response as AxiosResponse<T>)
            : (response?.data as T);
        }
      } else if (options.ignoreError === (axiosError.status || 0)) {
        return fullResponse
          ? (response as AxiosResponse<T>)
          : (response?.data as T);
      }
    }

    console.error("Fetcher error:", error);

    if (axios.isAxiosError(error)) {
      if (axiosError.response) {
        console.error("API error response:", axiosError.response.data);
        throw axiosError.response.data;
      }

      if (axiosError.code === "ECONNABORTED") {
        throw new Error("Request timeout - server took too long to respond");
      }

      if (axiosError.code === "ERR_NETWORK") {
        throw new Error("Network error - unable to connect to server");
      }
    }

    throw error;
  }
}
