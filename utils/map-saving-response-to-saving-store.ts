import type { SavingPlanProps } from '@endpoints/saving-plan.endpoints';
import type { SavingPlanStoreProps } from '@store/saving-plan.store';
import { mapPurchaseIntervalToFrequency } from './map-frequency-to-purchase-interval';

export const mapSavingResponseToSavingStore = (
  savingPlan: SavingPlanProps,
  savingStore: SavingPlanStoreProps
) => {
  savingStore.setSelectedPlan(savingPlan.plan_type);
  savingStore.setTimeHorizon(savingPlan.time_horizon);

  if (savingPlan.purchase_interval) {
    savingStore.setSavingAmountAndFrequency({
      amount: savingPlan.amount_value,
      frequency: mapPurchaseIntervalToFrequency(savingPlan.purchase_interval),
    });
  }

  if (savingPlan.amount_value) {
    savingStore.setFixedPlanValue(savingPlan.amount_value);
  }

  if (savingPlan.target) {
    savingStore.setTarget(savingPlan.target);
  }

  if (savingPlan.name) {
    savingStore.setPlanName(savingPlan.name);
  }
};
