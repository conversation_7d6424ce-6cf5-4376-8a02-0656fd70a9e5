/**
 * Progressive Retry Manager for Breez SDK
 * Implements exponential backoff and intelligent retry strategies
 */
/** biome-ignore-all lint/nursery/noAwaitInLoop: . */
/** biome-ignore-all lint/suspicious/useAwait: . */

import { breezServiceLogger } from '@utils/logger';
import {
  NON_RETRYABLE_ERRORS,
  RETRY_ERROR_MESSAGES,
  RETRYABLE_ERRORS,
} from '@utils/wallet/constants';

export interface RetryConfig {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  jitterMs: number;
}

export interface RetryAttempt {
  attemptNumber: number;
  delayMs: number;
  timestamp: number;
  error?: string;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  attempts: RetryAttempt[];
  totalDuration: number;
}

export class ProgressiveRetryManager {
  private static instance: ProgressiveRetryManager;
  private activeRetries = new Map<string, boolean>();

  private constructor() { }

  static getInstance(): ProgressiveRetryManager {
    if (!ProgressiveRetryManager.instance) {
      ProgressiveRetryManager.instance = new ProgressiveRetryManager();
    }
    return ProgressiveRetryManager.instance;
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationId: string,
    config: Partial<RetryConfig> = {}
  ): Promise<RetryResult<T>> {
    const finalConfig: RetryConfig = {
      maxAttempts: 5,
      baseDelayMs: 1000,
      maxDelayMs: 30_000,
      backoffMultiplier: 2,
      jitterMs: 500,
      ...config,
    };

    // Prevent concurrent retries for the same operation
    if (this.activeRetries.get(operationId)) {
      breezServiceLogger.warn(
        `Retry already in progress for operation: ${operationId}`
      );
      return {
        success: false,
        error: RETRY_ERROR_MESSAGES.RETRY_IN_PROGRESS,
        attempts: [],
        totalDuration: 0,
      };
    }

    this.activeRetries.set(operationId, true);
    const startTime = Date.now();
    const attempts: RetryAttempt[] = [];

    try {
      for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
        const attemptStart = Date.now();

        try {
          const result = await operation();

          attempts.push({
            attemptNumber: attempt,
            delayMs: 0,
            timestamp: attemptStart,
          });

          return {
            success: true,
            data: result,
            attempts,
            totalDuration: Date.now() - startTime,
          };
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);

          attempts.push({
            attemptNumber: attempt,
            delayMs: 0,
            timestamp: attemptStart,
            error: errorMessage,
          });

          breezServiceLogger.warn(
            `Attempt ${attempt} failed for ${operationId}`,
            { error: errorMessage }
          );

          // Check if we should retry based on error type
          if (
            !this.shouldRetryError(errorMessage) ||
            attempt === finalConfig.maxAttempts
          ) {
            breezServiceLogger.error(
              `Operation ${operationId} failed permanently`,
              {
                error: errorMessage,
                attempts: attempt,
              }
            );

            return {
              success: false,
              error: errorMessage,
              attempts,
              totalDuration: Date.now() - startTime,
            };
          }

          // Calculate delay for next attempt
          const delay = this.calculateDelay(attempt, finalConfig);
          attempts.at(-1)!.delayMs = delay;

          breezServiceLogger.debug(
            `Waiting ${delay}ms before retry ${attempt + 1} for ${operationId}`
          );
          await this.sleep(delay);
        }
      }

      return {
        success: false,
        error: RETRY_ERROR_MESSAGES.MAX_ATTEMPTS_EXCEEDED,
        attempts,
        totalDuration: Date.now() - startTime,
      };
    } finally {
      this.activeRetries.delete(operationId);
    }
  }

  /**
   * Execute connection operation with specialized retry logic
   */
  async executeConnectionRetry<T>(
    operation: () => Promise<T>,
    operationId: string
  ): Promise<RetryResult<T>> {
    return this.executeWithRetry(operation, operationId, {
      maxAttempts: 3,
      baseDelayMs: 2000,
      maxDelayMs: 10_000,
      backoffMultiplier: 1.5,
      jitterMs: 1000,
    });
  }

  /**
   * Execute initialization operation with specialized retry logic
   */
  async executeInitializationRetry<T>(
    operation: () => Promise<T>,
    operationId: string
  ): Promise<RetryResult<T>> {
    return this.executeWithRetry(operation, operationId, {
      maxAttempts: 2,
      baseDelayMs: 1000,
      maxDelayMs: 5000,
      backoffMultiplier: 2,
      jitterMs: 500,
    });
  }

  private calculateDelay(attempt: number, config: RetryConfig): number {
    const exponentialDelay =
      config.baseDelayMs * config.backoffMultiplier ** (attempt - 1);
    const jitter = Math.random() * config.jitterMs;
    const totalDelay = Math.min(exponentialDelay + jitter, config.maxDelayMs);

    return Math.floor(totalDelay);
  }

  private shouldRetryError(errorMessage: string): boolean {
    const nonRetryableErrors = NON_RETRYABLE_ERRORS;

    const retryableErrors = RETRYABLE_ERRORS;

    // Check for non-retryable errors first
    for (const nonRetryable of nonRetryableErrors) {
      if (errorMessage.toLowerCase().includes(nonRetryable.toLowerCase())) {
        return false;
      }
    }

    // Check for explicitly retryable errors
    for (const retryable of retryableErrors) {
      if (errorMessage.toLowerCase().includes(retryable.toLowerCase())) {
        return true;
      }
    }

    return true;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  isRetryInProgress(operationId: string): boolean {
    return this.activeRetries.get(operationId) ?? false;
  }

  cancelRetry(operationId: string): void {
    this.activeRetries.delete(operationId);
    breezServiceLogger.debug(`Retry cancelled for operation: ${operationId}`);
  }

  getActiveRetries(): string[] {
    return Array.from(this.activeRetries.keys());
  }

  clearAllRetries(): void {
    this.activeRetries.clear();
    breezServiceLogger.debug('All active retries cleared');
  }
}

export const progressiveRetryManager = ProgressiveRetryManager.getInstance();
export default progressiveRetryManager;
