/* biome-ignore-all lint: . */

import useErrorStore from '@store/error.store';
import type {
  BreezConnectionInfo,
  BreezServiceResponse,
  BreezConnectionState as ConnectionState,
} from 'types/breez-sdk.types';
import type { Error<PERSON>ey } from 'types/error.types';
import { createLogger } from 'utils/logger';

const commonLogger = createLogger('BreezCommon');

export interface OperationConfig {
  operationName: string;
  context?: string;
  showUserError?: boolean;
  errorKey?: ErrorKey;
  loadingMessage?: string;
  successMessage?: string;
}

export const extractErrorMessage = (
  error: unknown,
  fallback: string
): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return fallback;
};

export const createServiceResponse = (
  success: boolean,
  error?: string,
  data?: any
): BreezServiceResponse => {
  if (success) {
    return { success: true, data };
  }
  return { success: false, error: error || 'Operation failed' };
};

export const handleOperationError = (
  error: unknown,
  config: OperationConfig
): string => {
  const errorMessage = extractErrorMessage(
    error,
    `${config.operationName} failed`
  );

  commonLogger.error(`${config.operationName} failed`, error);

  if (config.showUserError && config.errorKey) {
    useErrorStore.getState().addError(config.errorKey, {
      key: config.errorKey,
      errorText: errorMessage,
      translate: false,
    });
  }

  return errorMessage;
};

export const executeOperation = async <T>(
  operation: () => Promise<T>,
  config: OperationConfig
): Promise<BreezServiceResponse<T>> => {
  try {
    commonLogger.operation(`Starting ${config.operationName}`);

    const result = await operation();

    if (config.successMessage) {
      commonLogger.success(config.successMessage);
    } else {
      commonLogger.success(`${config.operationName} completed successfully`);
    }

    return createServiceResponse(true, undefined, result) as BreezServiceResponse<T>;
  } catch (error) {
    const errorMessage = handleOperationError(error, config);
    return createServiceResponse(false, errorMessage) as BreezServiceResponse<T>;
  }
};

export const executeBooleanOperation = async (
  operation: () => Promise<boolean>,
  config: OperationConfig
): Promise<boolean> => {
  try {
    commonLogger.operation(`Starting ${config.operationName}`);

    const result = await operation();

    if (result) {
      if (config.successMessage) {
        commonLogger.success(config.successMessage);
      } else {
        commonLogger.success(`${config.operationName} completed successfully`);
      }
    } else {
      commonLogger.warn(`${config.operationName} returned false`);
    }

    return result;
  } catch (error) {
    handleOperationError(error, config);
    return false;
  }
};

export const executeWithLoading = async <T>(
  operation: () => Promise<T>,
  setLoading: (loading: boolean, message?: string) => void,
  config: OperationConfig
): Promise<BreezServiceResponse<T>> => {
  if (config.loadingMessage) {
    setLoading(true, config.loadingMessage);
  }

  try {
    const result = await executeOperation(operation, config);
    return result;
  } finally {
    if (config.loadingMessage) {
      setLoading(false);
    }
  }
};

export const updateConnectionState = (
  connectionInfo: BreezConnectionInfo,
  newState: ConnectionState,
  error?: string,
  operationName?: string
): BreezConnectionInfo => {
  const previousState = connectionInfo.state;

  const updatedInfo: BreezConnectionInfo = {
    ...connectionInfo,
    state: newState,
    lastError: error,
    retryCount: error
      ? connectionInfo.retryCount + 1
      : connectionInfo.retryCount,
  };

  if (operationName) {
    commonLogger.debug(
      `${operationName}: Connection state changed from ${previousState} to ${newState}`,
      error ? { error } : undefined
    );
  }

  return updatedInfo;
};

export const validateServiceResponse = <T>(
  response: BreezServiceResponse<T>,
  operationName: string
): T | null => {
  if (response.success && response.data !== undefined) {
    return response.data;
  }

  if (!response.success) {
    commonLogger.warn(`${operationName} failed: ${response.error}`);
  }

  return null;
};

export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxAttempts = 3,
  delayMs = 1000,
  operationName = 'operation'
): Promise<T> => {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts) {
        commonLogger.error(
          `${operationName} failed after ${maxAttempts} attempts`,
          error
        );
        throw error;
      }

      commonLogger.warn(
        `${operationName} attempt ${attempt} failed, retrying in ${delayMs}ms`,
        error
      );
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }

  throw lastError;
};

export const safeStateUpdate = <T>(
  currentState: T,
  updates: Partial<T>,
  operationName?: string
): T => {
  try {
    const newState = { ...currentState, ...updates };

    if (operationName) {
      commonLogger.debug(`${operationName}: State updated`, { updates });
    }

    return newState;
  } catch (error) {
    commonLogger.error(`Failed to update state for ${operationName}`, error);
    return currentState;
  }
};

export const batchStateUpdates = <T>(
  currentState: T,
  updateFunctions: Array<(state: T) => Partial<T>>,
  operationName?: string
): T => {
  try {
    let newState = { ...currentState };

    for (const updateFn of updateFunctions) {
      const updates = updateFn(newState);
      newState = { ...newState, ...updates };
    }

    if (operationName) {
      commonLogger.debug(`${operationName}: Batch state update completed`);
    }

    return newState;
  } catch (error) {
    commonLogger.error(
      `Failed to batch update state for ${operationName}`,
      error
    );
    return currentState;
  }
};

export default {
  extractErrorMessage,
  createServiceResponse,
  handleOperationError,
  executeOperation,
  executeBooleanOperation,
  executeWithLoading,
  updateConnectionState,
  validateServiceResponse,
  withRetry,
  safeStateUpdate,
  batchStateUpdates,
};
