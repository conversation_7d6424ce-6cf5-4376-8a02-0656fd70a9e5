/** biome-ignore-all lint/suspicious/noConsole: debug logging */
/** biome-ignore-all lint/suspicious/noExplicitAny: . */
/** biome-ignore-all lint/correctness/noUndeclaredVariables: . */
/** biome-ignore-all lint/suspicious/useAwait: . */
import type { WalletInfo } from 'types/breez-sdk.types';
import { createLogger } from 'utils/logger';
import breezSdkService from '../../services/breez.service';
import {
  type OperationConfig,
  validateServiceResponse,
} from './common-operations';

const breezOpsLogger = createLogger('BreezOperations');

export const createBreezConfig = (
  operationName: string,
  options?: Partial<OperationConfig>
): OperationConfig => ({
  operationName,
  context: 'breez-sdk',
  showUserError: true,
  ...options,
});

export const refreshWalletInfo = async (
  updateState: (walletInfo: WalletInfo) => void,
  config?: Partial<OperationConfig>
): Promise<boolean> => {
  const operationConfig = createBreezConfig('refresh wallet info', config);

  try {
    const result = await breezSdkService.getWalletInfo();
    const walletInfo = validateServiceResponse(
      result,
      operationConfig.operationName
    );

    if (walletInfo) {
      updateState(walletInfo);
      return true;
    }

    return false;
  } catch (error) {
    breezOpsLogger.error('Failed to refresh wallet info', error);
    return false;
  }
};

export default {
  createBreezConfig,
  refreshWalletInfo,
};
