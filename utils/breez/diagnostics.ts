/**
 * @fileoverview Breez SDK Diagnostics and Health Monitoring
 *
 * Comprehensive diagnostic utilities for debugging Breez SDK initialization
 * and connection issues. Provides detailed health checks, state validation,
 * and recovery mechanisms.
 */

// biome-ignore-all lint: development flag
declare const __DEV__: boolean;

import { SecureStorageKeys } from '@const/secure-storage-keys';
import { createLogger } from '@utils/logger';
import {
  DIAGNOSTIC_CATEGORIES,
  DIAGNOSTIC_MESSAGES,
  DIAGNOSTIC_STATUS,
} from '@utils/wallet/constants';
import { getItem } from 'expo-secure-store';
import breezSdkService from '../../services/breez.service';

const diagnosticsLogger = createLogger('BreezDiagnostics');

export interface DiagnosticResult {
  category: string;
  status: (typeof DIAGNOSTIC_STATUS)[keyof typeof DIAGNOSTIC_STATUS];
  message: string;
  details?: Record<string, unknown>;
}

export interface ComprehensiveDiagnostic {
  timestamp: number;
  overallHealth: 'healthy' | 'warning' | 'error';
  results: DiagnosticResult[];
  recommendations: string[];
}

// Type for store state to avoid circular dependency
export interface BreezStoreState {
  isInitialized: boolean;
  isConnected: boolean;
  walletInfo: any;
  lastError: string | null;
  connectionInfo: any;
}

/**
 * Perform comprehensive Breez SDK diagnostics
 */
export const performComprehensiveDiagnostics = async (
  storeState: BreezStoreState
): Promise<ComprehensiveDiagnostic> => {
  const results: DiagnosticResult[] = [];
  const recommendations: string[] = [];

  diagnosticsLogger.operation('Starting comprehensive Breez SDK diagnostics');

  // 1. Service State Diagnostics
  try {
    const serviceInitialized = breezSdkService.isInitialized();
    const healthCheck = await breezSdkService.validateServiceHealth();
    const connectionInfo = breezSdkService.getConnectionInfo();
    const walletState = breezSdkService.getWalletState();

    results.push({
      category: DIAGNOSTIC_CATEGORIES.SERVICE_INITIALIZATION,
      status: serviceInitialized ? DIAGNOSTIC_STATUS.HEALTHY : DIAGNOSTIC_STATUS.ERROR,
      message: serviceInitialized
        ? DIAGNOSTIC_MESSAGES.SERVICE_INITIALIZED
        : DIAGNOSTIC_MESSAGES.SERVICE_NOT_INITIALIZED,
      details: {
        initialized: serviceInitialized,
        healthCheck,
        connectionState: connectionInfo.state,
        isConnected: walletState.isConnected,
      },
    });

    if (!serviceInitialized) {
      recommendations.push(
        'Call breezSdkService.initialize() with proper configuration'
      );
    }

    if (!healthCheck.isHealthy) {
      recommendations.push(
        `Resolve service health issues: ${healthCheck.issues.join(', ')}`
      );
    }
  } catch (error) {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.SERVICE_INITIALIZATION,
      status: DIAGNOSTIC_STATUS.ERROR,
      message: `Service diagnostics failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
    recommendations.push(
      'Service may be in corrupted state - consider force reset'
    );
  }

  // 2. Store State Diagnostics
  try {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.STORE_STATE,
      status:
        storeState.isInitialized && storeState.isConnected
          ? DIAGNOSTIC_STATUS.HEALTHY
          : DIAGNOSTIC_STATUS.WARNING,
      message: DIAGNOSTIC_MESSAGES.STORE_STATE_ANALYSIS,
      details: {
        storeInitialized: storeState.isInitialized,
        storeConnected: storeState.isConnected,
        hasWalletInfo: !!storeState.walletInfo,
        lastError: storeState.lastError,
      },
    });

    if (!storeState.isInitialized) {
      recommendations.push('Initialize Breez SDK store');
    }

    if (storeState.lastError) {
      recommendations.push(`Resolve store error: ${storeState.lastError}`);
    }
  } catch (error) {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.STORE_STATE,
      status: DIAGNOSTIC_STATUS.ERROR,
      message: `Store diagnostics failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
  }

  // 3. Secure Storage Diagnostics
  try {
    const storedMnemonic = getItem(SecureStorageKeys.MNEMONIC);
    const storedConfig = getItem(SecureStorageKeys.BREEZ_SERVICE_CONFIG);

    results.push({
      category: DIAGNOSTIC_CATEGORIES.SECURE_STORAGE,
      status: storedMnemonic ? DIAGNOSTIC_STATUS.HEALTHY : DIAGNOSTIC_STATUS.WARNING,
      message: DIAGNOSTIC_MESSAGES.SECURE_STORAGE_ANALYSIS,
      details: {
        hasMnemonic: !!storedMnemonic,
        hasConfig: !!storedConfig,
      },
    });

    if (!storedMnemonic) {
      recommendations.push(
        'No wallet mnemonic found - wallet creation may be required'
      );
    }
  } catch (error) {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.SECURE_STORAGE,
      status: DIAGNOSTIC_STATUS.ERROR,
      message: `Secure storage diagnostics failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
    recommendations.push(
      'Secure storage may be corrupted - consider clearing and recreating wallet'
    );
  }

  // 4. State Consistency Diagnostics
  try {
    const serviceInitialized = breezSdkService.isInitialized();
    const serviceConnected = breezSdkService.getWalletState().isConnected;

    const stateConsistent =
      serviceInitialized === storeState.isInitialized &&
      serviceConnected === storeState.isConnected;

    results.push({
      category: DIAGNOSTIC_CATEGORIES.STATE_CONSISTENCY,
      status: stateConsistent ? DIAGNOSTIC_STATUS.HEALTHY : DIAGNOSTIC_STATUS.ERROR,
      message: stateConsistent
        ? DIAGNOSTIC_MESSAGES.STATES_CONSISTENT
        : DIAGNOSTIC_MESSAGES.STATE_INCONSISTENCY,
      details: {
        serviceInitialized,
        storeInitialized: storeState.isInitialized,
        serviceConnected,
        storeConnected: storeState.isConnected,
        consistent: stateConsistent,
      },
    });

    if (!stateConsistent) {
      recommendations.push(
        'State inconsistency detected - consider force reset and re-initialization'
      );
    }
  } catch (error) {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.STATE_CONSISTENCY,
      status: DIAGNOSTIC_STATUS.ERROR,
      message: `State consistency check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
  }

  // 5. Environment Diagnostics
  try {
    const hasApiKey = !!process.env.BREEZ_API_KEY;

    results.push({
      category: DIAGNOSTIC_CATEGORIES.ENVIRONMENT,
      status: hasApiKey ? DIAGNOSTIC_STATUS.HEALTHY : DIAGNOSTIC_STATUS.ERROR,
      message: hasApiKey
        ? DIAGNOSTIC_MESSAGES.ENVIRONMENT_CONFIGURED
        : DIAGNOSTIC_MESSAGES.MISSING_ENV_VARS,
      details: {
        hasBreezApiKey: hasApiKey,
        isDevelopment: __DEV__,
      },
    });

    if (!hasApiKey) {
      recommendations.push('Set BREEZ_API_KEY environment variable');
    }
  } catch (error) {
    results.push({
      category: DIAGNOSTIC_CATEGORIES.ENVIRONMENT,
      status: DIAGNOSTIC_STATUS.ERROR,
      message: `Environment diagnostics failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
  }

  // Determine overall health
  const errorCount = results.filter((r) => r.status === 'error').length;
  const warningCount = results.filter((r) => r.status === 'warning').length;

  let overallHealth: 'healthy' | 'warning' | 'error';
  if (errorCount > 0) {
    overallHealth = 'error';
  } else if (warningCount > 0) {
    overallHealth = 'warning';
  } else {
    overallHealth = 'healthy';
  }

  const diagnostic: ComprehensiveDiagnostic = {
    timestamp: Date.now(),
    overallHealth,
    results,
    recommendations,
  };

  diagnosticsLogger.info('Comprehensive diagnostics completed', {
    overallHealth,
    errorCount,
    warningCount,
    recommendationCount: recommendations.length,
  });

  return diagnostic;
};

/**
 * Wrapper function that gets store state and performs diagnostics
 * This breaks the circular dependency by importing the store only when needed
 */
const getStoreStateAndPerformDiagnostics = async (): Promise<ComprehensiveDiagnostic> => {
  // Dynamic import to avoid circular dependency
  const { default: useBreezSdkStore } = await import('@store/breez-sdk.store');
  const storeState = useBreezSdkStore.getState();
  return performComprehensiveDiagnostics(storeState);
};

/**
 * Perform automatic recovery based on diagnostic results
 */
export const performAutomaticRecovery = async (): Promise<{
  success: boolean;
  actions: string[];
}> => {
  const actions: string[] = [];

  try {
    diagnosticsLogger.operation('Starting automatic recovery');

    // Run diagnostics first
    const diagnostic = await getStoreStateAndPerformDiagnostics();

    if (diagnostic.overallHealth === 'healthy') {
      actions.push('No recovery needed - system is healthy');
      return { success: true, actions };
    }

    // Attempt service recovery
    const serviceRecovery =
      await breezSdkService.detectAndRecoverInconsistencies();
    actions.push(...serviceRecovery.actions);

    if (!serviceRecovery.recovered) {
      actions.push('Service recovery failed - manual intervention required');
      return { success: false, actions };
    }

    // Re-run diagnostics to verify recovery
    const postRecoveryDiagnostic = await getStoreStateAndPerformDiagnostics();

    if (postRecoveryDiagnostic.overallHealth === 'healthy') {
      actions.push('Automatic recovery completed successfully');
      return { success: true, actions };
    }
    actions.push('Partial recovery - some issues remain');
    return { success: false, actions };
  } catch (error) {
    actions.push(
      `Recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { success: false, actions };
  }
};

/**
 * Log diagnostic summary for debugging
 */
export const logDiagnosticSummary = async (): Promise<void> => {
  try {
    const diagnostic = await getStoreStateAndPerformDiagnostics();

    diagnosticsLogger.info('=== BREEZ SDK DIAGNOSTIC SUMMARY ===');
    diagnosticsLogger.info(
      `Overall Health: ${diagnostic.overallHealth.toUpperCase()}`
    );

    for (const result of diagnostic.results) {
      const logLevel =
        result.status === 'error'
          ? 'error'
          : result.status === 'warning'
            ? 'warn'
            : 'info';
      diagnosticsLogger[logLevel](
        `${result.category}: ${result.message}`,
        result.details
      );
    }

    if (diagnostic.recommendations.length > 0) {
      diagnosticsLogger.info('Recommendations:', diagnostic.recommendations);
    }

    diagnosticsLogger.info('=== END DIAGNOSTIC SUMMARY ===');
  } catch (error) {
    diagnosticsLogger.error('Failed to generate diagnostic summary', error);
  }
};
