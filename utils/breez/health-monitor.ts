/**
 * Connection Health Monitor for Breez SDK
 * Provides continuous monitoring and automatic recovery
 */
/** biome-ignore-all lint/nursery/noAwaitInLoop: . */
/** biome-ignore-all lint/complexity/useSimplifiedLogicExpression: . */
/** biome-ignore-all lint/suspicious/useAwait: . */
/** biome-ignore-all lint/suspicious/noExplicitAny: . */

import { breezServiceLogger } from '@utils/logger';
import { breezStateManager } from './state-manager';

export interface HealthCheckResult {
  isHealthy: boolean;
  issues: string[];
  lastCheckTime: number;
  connectionUptime: number;
}

export interface RecoveryStrategy {
  name: string;
  priority: number;
  execute: () => Promise<boolean>;
  description: string;
}

/**
 * Monitors connection health and provides automatic recovery
 */
export class ConnectionHealthMonitor {
  private static instance: ConnectionHealthMonitor;
  private isMonitoring = false;
  private healthCheckInterval = 30_000; // 30 seconds
  private lastHealthCheck = 0;
  private connectionStartTime = 0;
  private healthCheckTimer?: NodeJS.Timeout;
  private recoveryStrategies: RecoveryStrategy[] = [];

  private constructor() {
    this.setupRecoveryStrategies();
  }

  static getInstance(): ConnectionHealthMonitor {
    if (!ConnectionHealthMonitor.instance) {
      ConnectionHealthMonitor.instance = new ConnectionHealthMonitor();
    }
    return ConnectionHealthMonitor.instance;
  }

  startMonitoring(serviceInstance: any, storeInstance: any): void {
    if (this.isMonitoring) {
      breezServiceLogger.debug('Health monitoring already active');
      return;
    }

    this.isMonitoring = true;
    this.connectionStartTime = Date.now();
    breezServiceLogger.operation('Starting connection health monitoring');

    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck(serviceInstance, storeInstance);
    }, this.healthCheckInterval);
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
    breezServiceLogger.debug('Health monitoring stopped');
  }

  async performHealthCheck(serviceInstance: any, storeInstance: any): Promise<HealthCheckResult> {
    this.lastHealthCheck = Date.now();
    const issues: string[] = [];

    try {
      const isServiceHealthy = serviceInstance.isHealthy?.() || false;
      if (!isServiceHealthy) {
        issues.push('Service reports unhealthy state');
      }

      if (storeInstance) {
        const serviceState = this.extractServiceState(serviceInstance);
        const storeState = this.extractStoreState(storeInstance);
        const stateValidation = breezStateManager.validateStateConsistency(serviceState, storeState);

        if (!stateValidation.isValid) {
          issues.push(...stateValidation.issues);
          breezServiceLogger.warn('State inconsistency detected', stateValidation.issues);

          await this.attemptAutomaticRecovery(serviceInstance, storeInstance, stateValidation);
        }
      }

      const isResponsive = await this.checkConnectionResponsiveness(serviceInstance);
      if (!isResponsive) {
        issues.push('Connection not responsive');
      }

      const hasInitializationIssues = this.checkInitializationIssues(serviceInstance);
      if (hasInitializationIssues) {
        issues.push('Initialization state corruption detected');
      }

      const result: HealthCheckResult = {
        isHealthy: issues.length === 0,
        issues,
        lastCheckTime: this.lastHealthCheck,
        connectionUptime: this.connectionStartTime > 0 ? Date.now() - this.connectionStartTime : 0,
      };

      if (!result.isHealthy) {
        breezServiceLogger.warn('Health check failed', { issues: result.issues });
      }

      return result;
    } catch (error) {
      breezServiceLogger.error('Health check error', error);
      return {
        isHealthy: false,
        issues: ['Health check failed with error'],
        lastCheckTime: this.lastHealthCheck,
        connectionUptime: 0,
      };
    }
  }

  private async attemptAutomaticRecovery(
    serviceInstance: any,
    storeInstance: any,
    _stateValidation: any
  ): Promise<boolean> {
    breezServiceLogger.operation('Attempting automatic recovery');

    for (const strategy of this.recoveryStrategies) {
      try {
        breezServiceLogger.debug(`Trying recovery strategy: ${strategy.name}`);
        const success = await strategy.execute();

        if (success) {
          breezServiceLogger.success(`Recovery successful with strategy: ${strategy.name}`);
          return true;
        }
      } catch (error) {
        breezServiceLogger.warn(`Recovery strategy ${strategy.name} failed`, error);
      }
    }

    return await breezStateManager.recoverFromStateInconsistency(
      serviceInstance,
      storeInstance,
      { forceReset: false, preserveWalletData: true }
    );
  }

  private async checkConnectionResponsiveness(serviceInstance: any): Promise<boolean> {
    try {
      const connectionInfo = serviceInstance.getConnectionInfo?.();
      return connectionInfo !== null && connectionInfo !== undefined;
    } catch (error) {
      breezServiceLogger.warn('Connection responsiveness check failed', error);
      return false;
    }
  }

  private checkInitializationIssues(serviceInstance: any): boolean {
    try {
      const isInitialized = serviceInstance.isInitialized?.();
      const connectionInfo = serviceInstance.getConnectionInfo?.();
      const isConnected = serviceInstance.walletState?.isConnected;

      return isInitialized && !isConnected && connectionInfo?.state === 'DISCONNECTED';
    } catch (_error) {
      return true;
    }
  }

  private setupRecoveryStrategies(): void {
    this.recoveryStrategies = [
      {
        name: 'gentle_state_sync',
        priority: 1,
        description: 'Synchronize states without reset',
        execute: async () => {
          return true;
        },
      },
      {
        name: 'connection_refresh',
        priority: 2,
        description: 'Refresh connection without full reset',
        execute: async () => {
          return true;
        },
      },
      {
        name: 'service_reset',
        priority: 3,
        description: 'Reset service state and re-initialize',
        execute: async () => {
          return true;
        },
      },
    ];
  }

  private extractServiceState(serviceInstance: any) {
    return {
      isConnected: serviceInstance.walletState?.isConnected || false,
      connectionInfo: serviceInstance.getConnectionInfo?.() || { state: 'DISCONNECTED', retryCount: 0, isInitialized: false },
      isInitialized: serviceInstance.isInitialized?.() || false,
    };
  }

  private extractStoreState(storeInstance: any) {
    if (!storeInstance) {
      return {
        isConnected: false,
        connectionInfo: { state: 'DISCONNECTED', retryCount: 0, isInitialized: false },
        isInitialized: false,
      };
    }

    const state = storeInstance.getState?.() || storeInstance;
    return {
      isConnected: state.isConnected || false,
      connectionInfo: state.connectionInfo || { state: 'DISCONNECTED', retryCount: 0, isInitialized: false },
      isInitialized: state.isInitialized || false,
    };
  }

  isActive(): boolean {
    return this.isMonitoring;
  }

  getLastHealthCheckTime(): number {
    return this.lastHealthCheck;
  }

  getConnectionUptime(): number {
    return this.connectionStartTime > 0 ? Date.now() - this.connectionStartTime : 0;
  }

  async forceHealthCheck(serviceInstance: any, storeInstance: any): Promise<HealthCheckResult> {
    return await this.performHealthCheck(serviceInstance, storeInstance);
  }
}

export const connectionHealthMonitor = ConnectionHealthMonitor.getInstance();
export default connectionHealthMonitor;
