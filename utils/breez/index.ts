export type {
  BreezConnectionInfo,
  BreezPaymentLimits,
  BreezSdkConfig,
  BreezServiceResponse,
  BreezWalletState,
  CreateInvoiceRequest,
  CreateInvoiceResponse,
  IBreezSdkService,
} from 'types/breez-sdk.types';

// Core Store (consolidated)
export { default as useBreezSdkStore } from '../../store/breez-sdk.store';

// App Lifecycle Management
export {
  breezAppLifecycleManager as appLifecycleManager,
  default as breezAppLifecycleManager,
} from './app-lifecycle-manager';

// Authentication Guard
export {
  breezAuthGuard as authGuard,
  default as breezAuthGuard,
} from './auth-guard';

// Essential utilities only
export { testApiKey } from './debug-utils';
