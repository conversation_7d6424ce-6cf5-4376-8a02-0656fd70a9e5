/** biome-ignore-all lint/suspicious/noConsole: debug logging */
/** biome-ignore-all lint/suspicious/noExplicitAny: . */

import { AppState, type AppStateStatus } from 'react-native';
import { createLogger } from 'utils/logger';

const lifecycleLogger = createLogger('BreezAppLifecycle');

// Type for the reconnection callback
type ReconnectionCallback = () => Promise<boolean>;

/**
 * Manages app lifecycle events and handles Breez SDK reconnection
 * when the app returns from background after extended periods
 */
class BreezAppLifecycleManager {
  private static instance: BreezAppLifecycleManager | null = null;
  private appStateSubscription: any = null;
  private backgroundTime: number | null = null;
  private isInitialized = false;
  private reconnectionCallback: ReconnectionCallback | null = null;

  private readonly BACKGROUND_THRESHOLD_MS = 1.5 * 60 * 1000;

  private constructor() { }

  static getInstance(): BreezAppLifecycleManager {
    if (!BreezAppLifecycleManager.instance) {
      BreezAppLifecycleManager.instance = new BreezAppLifecycleManager();
    }
    return BreezAppLifecycleManager.instance;
  }

  initialize(reconnectionCallback: ReconnectionCallback): void {
    if (this.isInitialized) {
      return;
    }

    this.reconnectionCallback = reconnectionCallback;
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );

    this.isInitialized = true;
  }

  cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    this.backgroundTime = null;
    this.reconnectionCallback = null;
    this.isInitialized = false;
  }

  private handleAppStateChange(nextAppState: AppStateStatus): void {
    const currentTime = Date.now();

    if (nextAppState === 'background' || nextAppState === 'inactive') {
      this.backgroundTime = currentTime;
    } else if (nextAppState === 'active') {
      this.handleAppForeground(currentTime);
    }
  }

  private async handleAppForeground(currentTime: number): Promise<void> {
    if (!this.backgroundTime) {
      return;
    }

    const backgroundDuration = currentTime - this.backgroundTime;
    this.backgroundTime = null;

    if (backgroundDuration > this.BACKGROUND_THRESHOLD_MS) {
      await this.handleExtendedBackground();
    }
  }

  private async handleExtendedBackground(): Promise<void> {
    try {
      if (!this.reconnectionCallback) {
        lifecycleLogger.warn('No reconnection callback available');
        return;
      }

      lifecycleLogger.debug('Attempting reconnection after extended background');
      const reconnectSuccess = await this.reconnectionCallback();

      if (reconnectSuccess) {
        lifecycleLogger.success(
          'Successfully reconnected after extended background'
        );
      } else {
        lifecycleLogger.warn(
          'Failed to reconnect after extended background'
        );
      }
    } catch (error) {
      lifecycleLogger.error(
        'Error handling extended background scenario:',
        error
      );
    }
  }

  getStatus(): {
    isInitialized: boolean;
    backgroundTime: number | null;
    backgroundDuration: number | null;
  } {
    return {
      isInitialized: this.isInitialized,
      backgroundTime: this.backgroundTime,
      backgroundDuration: this.backgroundTime
        ? Date.now() - this.backgroundTime
        : null,
    };
  }
}

export const breezAppLifecycleManager = BreezAppLifecycleManager.getInstance();
export default breezAppLifecycleManager;
