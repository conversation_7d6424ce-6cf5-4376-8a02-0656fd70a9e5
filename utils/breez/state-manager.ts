/** biome-ignore-all lint/suspicious/noExplicitAny: . */
/** biome-ignore-all lint/correctness/noUndeclaredVariables: . */
/** biome-ignore-all lint/suspicious/useAwait: . */

import { breezServiceLogger } from '@utils/logger';
import {
  STATE_RECOMMENDATIONS,
  STATE_SYNC_ACTIONS,
  STATE_SYNC_EVENTS,
} from '@utils/wallet/constants';
import type { BreezConnectionInfo } from 'types/breez-sdk.types';

export interface StateValidationResult {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
}

export interface StateRecoveryOptions {
  forceReset?: boolean;
  preserveWalletData?: boolean;
  retryConnection?: boolean;
}

export interface StateSyncEvent {
  type: keyof typeof STATE_SYNC_EVENTS;
  details: {
    serviceState?: any;
    storeState?: any;
    action?: string;
    timestamp: number;
  };
}

/**
 * Centralized state synchronization manager for Breez SDK
 * Provides state consistency and recovery mechanisms
 */
export class BreezStateSynchronizationManager {
  private static instance: BreezStateSynchronizationManager;
  private eventListeners: Array<(event: StateSyncEvent) => void> = [];
  private lastValidationTime = 0;
  private validationInterval = 5000; // 5 seconds
  private isRecovering = false;

  private constructor() {
    this.startPeriodicValidation();
  }

  static getInstance(): BreezStateSynchronizationManager {
    if (!BreezStateSynchronizationManager.instance) {
      BreezStateSynchronizationManager.instance =
        new BreezStateSynchronizationManager();
    }
    return BreezStateSynchronizationManager.instance;
  }

  /**
   * Validate state consistency between service and store
   */
  validateStateConsistency(
    serviceState: {
      isConnected: boolean;
      connectionInfo: BreezConnectionInfo;
      isInitialized: boolean;
    },
    storeState: {
      isConnected: boolean;
      connectionInfo: BreezConnectionInfo;
      isInitialized: boolean;
    }
  ): StateValidationResult {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check initialization consistency
    if (serviceState.isInitialized !== storeState.isInitialized) {
      issues.push(
        `Initialization state mismatch: service=${serviceState.isInitialized}, store=${storeState.isInitialized}`
      );
      recommendations.push(STATE_RECOMMENDATIONS.SYNC_INITIALIZATION);
    }

    // Check connection consistency
    if (serviceState.isConnected !== storeState.isConnected) {
      issues.push(
        `Connection state mismatch: service=${serviceState.isConnected}, store=${storeState.isConnected}`
      );
      recommendations.push(STATE_RECOMMENDATIONS.SYNC_CONNECTION);
    }

    // Check connection info consistency
    if (serviceState.connectionInfo.state !== storeState.connectionInfo.state) {
      issues.push(
        `Connection info state mismatch: service=${serviceState.connectionInfo.state}, store=${storeState.connectionInfo.state}`
      );
      recommendations.push(STATE_RECOMMENDATIONS.UPDATE_CONNECTION_INFO);
    }

    // Check for "Already initialized" scenario
    if (
      serviceState.isInitialized &&
      serviceState.connectionInfo.state === 'DISCONNECTED' &&
      !serviceState.isConnected
    ) {
      issues.push(
        'Service initialized but not connected - potential "Already initialized" state'
      );
      recommendations.push(STATE_RECOMMENDATIONS.RESET_AND_REINITIALIZE);
    }

    // Check for orphaned connections
    if (
      serviceState.isConnected &&
      storeState.connectionInfo.state === 'DISCONNECTED'
    ) {
      issues.push('Service connected but store shows disconnected');
      recommendations.push(STATE_RECOMMENDATIONS.UPDATE_STORE_CONNECTION);
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * Recover from state inconsistencies
   */
  async recoverFromStateInconsistency(
    serviceInstance: any,
    storeInstance: any,
    options: StateRecoveryOptions = {}
  ): Promise<boolean> {
    if (this.isRecovering) {
      breezServiceLogger.warn('State recovery already in progress');
      return false;
    }

    this.isRecovering = true;
    breezServiceLogger.operation('Starting state recovery', options as any);

    try {
      this.emitEvent({
        type: STATE_SYNC_EVENTS.STATE_CORRUPTED,
        details: {
          action: STATE_SYNC_ACTIONS.RECOVERY_STARTED,
          timestamp: Date.now(),
        },
      });

      if (options.forceReset) {
        breezServiceLogger.debug('Performing force reset');
        await this.performForceReset(
          serviceInstance,
          storeInstance,
          options.preserveWalletData
        );
      } else {
        breezServiceLogger.debug('Performing gentle recovery');
        await this.performGentleRecovery(serviceInstance, storeInstance);
      }

      // Validate recovery success
      const serviceState = this.extractServiceState(serviceInstance);
      const storeState = this.extractStoreState(storeInstance);
      const validation = this.validateStateConsistency(
        serviceState,
        storeState
      );

      if (validation.isValid) {
        breezServiceLogger.success('State recovery completed successfully');
        this.emitEvent({
          type: STATE_SYNC_EVENTS.STATE_RECOVERED,
          details: {
            action: STATE_SYNC_ACTIONS.RECOVERY_COMPLETED,
            timestamp: Date.now(),
          },
        });
        return true;
      }
      breezServiceLogger.error('State recovery failed', validation.issues);
      return false;
    } catch (error) {
      breezServiceLogger.error('State recovery error', error);
      return false;
    } finally {
      this.isRecovering = false;
    }
  }

  /**
   * Perform gentle recovery (sync states without reset)
   */
  private async performGentleRecovery(
    serviceInstance: any,
    storeInstance: any
  ): Promise<void> {
    const serviceState = this.extractServiceState(serviceInstance);
    const storeState = this.extractStoreState(storeInstance);

    // Sync initialization state
    if (serviceState.isInitialized !== storeState.isInitialized) {
      const targetState =
        serviceState.isInitialized || storeState.isInitialized;
      if (storeInstance.set) {
        storeInstance.set({ isInitialized: targetState });
      }
    }

    // Sync connection state
    if (
      serviceState.isConnected !== storeState.isConnected &&
      storeInstance.set
    ) {
      storeInstance.set({
        isConnected: serviceState.isConnected,
        connectionInfo: serviceState.connectionInfo,
      });
    }
  }

  /**
   * Perform force reset (complete state reset)
   */
  private async performForceReset(
    serviceInstance: any,
    storeInstance: any,
    preserveWalletData = true
  ): Promise<void> {
    breezServiceLogger.warn('Performing force reset of Breez SDK state');

    // Reset service state
    if (serviceInstance.resetForTesting && __DEV__) {
      serviceInstance.resetForTesting();
    }

    // Reset store state
    if (storeInstance.set) {
      const resetState: any = {
        isInitialized: false,
        isConnected: false,
        isConnecting: false,
        connectionInfo: {
          state: 'DISCONNECTED',
          retryCount: 0,
          isInitialized: false,
        },
        lastError: null,
      };

      if (!preserveWalletData) {
        resetState.walletInfo = null;
        resetState.recentPayments = [];
        resetState.pendingPayments = [];
      }

      storeInstance.set(resetState);
    }
  }

  /**
   * Extract service state for validation
   */
  private extractServiceState(serviceInstance: any) {
    return {
      isConnected: serviceInstance.walletState?.isConnected,
      connectionInfo: serviceInstance.getConnectionInfo?.() || {
        state: 'DISCONNECTED',
        retryCount: 0,
        isInitialized: false,
      },
      isInitialized: serviceInstance.isInitialized?.(),
    };
  }

  /**
   * Extract store state for validation
   */
  private extractStoreState(storeInstance: any) {
    const state = storeInstance.getState?.() || storeInstance;
    return {
      isConnected: state.isConnected,
      connectionInfo: state.connectionInfo || {
        state: 'DISCONNECTED',
        retryCount: 0,
        isInitialized: false,
      },
      isInitialized: state.isInitialized,
    };
  }

  /**
   * Start periodic state validation
   */
  private startPeriodicValidation(): void {
    if (typeof setInterval !== 'undefined') {
      setInterval(() => {
        this.lastValidationTime = Date.now();
        // Periodic validation will be triggered by external calls
      }, this.validationInterval);
    }
  }

  /**
   * Add event listener for state sync events
   */
  addEventListener(listener: (event: StateSyncEvent) => void): () => void {
    this.eventListeners.push(listener);
    return () => {
      const index = this.eventListeners.indexOf(listener);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  /**
   * Emit state sync event
   */
  private emitEvent(event: StateSyncEvent): void {
    for (const listener of this.eventListeners) {
      try {
        listener(event);
      } catch (error) {
        breezServiceLogger.error('Error in state sync event listener', error);
      }
    }
  }

  /**
   * Check if state validation is needed
   */
  shouldValidateState(): boolean {
    return Date.now() - this.lastValidationTime > this.validationInterval;
  }

  /**
   * Get recovery status
   */
  isRecoveryInProgress(): boolean {
    return this.isRecovering;
  }
}

// Export singleton instance
export const breezStateManager = BreezStateSynchronizationManager.getInstance();
export default breezStateManager;
