import useAuthStore from "@store/auth.store";
import { createLogger } from "@utils/logger";
import { AuthState } from "types/auth.types";

const authGuardLogger = createLogger("BreezAuthGuard");

/**
 * Authentication guard for Breez SDK operations
 * Ensures that Breez SDK operations only occur when user is properly authenticated
 */
export class BreezAuthGuard {
  private static instance: BreezAuthGuard | null = null;
  private authStateListeners: Array<(isAuthenticated: boolean) => void> = [];
  private isInitialized = false;

  private constructor() {}

  static getInstance(): BreezAuthGuard {
    if (!BreezAuthGuard.instance) {
      BreezAuthGuard.instance = new BreezAuthGuard();
    }
    return BreezAuthGuard.instance;
  }

  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    authGuardLogger.debug("Initializing Breez authentication guard");
    this.isInitialized = true;

    useAuthStore.subscribe((state, prevState) => {
      if (state.authenticated !== prevState.authenticated) {
        authGuardLogger.debug("Authentication state changed", {
          from: prevState.authenticated,
          to: state.authenticated,
        });

        const isAuthenticated = state.authenticated === AuthState.Authenticated;
        this.notifyAuthStateListeners(isAuthenticated);
      }
    });
  }

  isAuthenticated(): boolean {
    try {
      const authState = useAuthStore.getState().authenticated;
      const isAuth = authState === AuthState.Authenticated;

      authGuardLogger.debug("Authentication check", {
        authState,
        isAuthenticated: isAuth,
      });

      return isAuth;
    } catch (error) {
      authGuardLogger.warn("Error checking authentication state", error);
      return false;
    }
  }

  isAuthenticationPending(): boolean {
    try {
      const authState = useAuthStore.getState().authenticated;
      return authState === AuthState.Pending || authState === AuthState.Unknown;
    } catch (error) {
      authGuardLogger.warn(
        "Error checking authentication pending state",
        error
      );
      return true; // Assume pending if we can't check
    }
  }

  isUnauthenticated(): boolean {
    try {
      const authState = useAuthStore.getState().authenticated;
      return authState === AuthState.Unauthenticated;
    } catch (error) {
      authGuardLogger.warn("Error checking unauthenticated state", error);
      return false;
    }
  }

  waitForAuthResolution(timeoutMs = 10_000): Promise<boolean> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (this.isAuthenticated()) {
          authGuardLogger.debug("Authentication resolved: authenticated");
          resolve(true);
          return true;
        }

        if (this.isUnauthenticated()) {
          authGuardLogger.debug("Authentication resolved: unauthenticated");
          resolve(false);
          return true;
        }

        return false;
      };

      if (checkAuth()) {
        return;
      }

      const timeout = setTimeout(() => {
        authGuardLogger.warn("Authentication resolution timeout");
        resolve(false);
      }, timeoutMs);

      const unsubscribe = useAuthStore.subscribe(() => {
        if (checkAuth()) {
          clearTimeout(timeout);
          unsubscribe();
        }
      });
    });
  }

  addAuthStateListener(
    listener: (isAuthenticated: boolean) => void
  ): () => void {
    this.authStateListeners.push(listener);

    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  private notifyAuthStateListeners(isAuthenticated: boolean): void {
    authGuardLogger.debug("Notifying auth state listeners", {
      isAuthenticated,
      listenerCount: this.authStateListeners.length,
    });

    this.authStateListeners.forEach((listener) => {
      try {
        listener(isAuthenticated);
      } catch (error) {
        authGuardLogger.error("Error in auth state listener", error);
      }
    });
  }

  guardBreezOperation(operationName: string): boolean {
    const isAuth = this.isAuthenticated();

    // biome-ignore lint/style/noNegationElse: .
    if (!isAuth) {
      try {
        const authState = useAuthStore.getState().authenticated;
        authGuardLogger.warn(`Breez operation blocked: ${operationName}`, {
          authState,
          reason: "User not authenticated",
        });
      } catch (_error) {
        authGuardLogger.warn(`Breez operation blocked: ${operationName}`, {
          reason: "User not authenticated (auth state check failed)",
        });
      }
    } else {
      authGuardLogger.debug(`Breez operation allowed: ${operationName}`);
    }

    return isAuth;
  }

  cleanup(): void {
    this.authStateListeners = [];
    this.isInitialized = false;
    authGuardLogger.debug("Auth guard cleaned up");
  }
}

export const breezAuthGuard = BreezAuthGuard.getInstance();
export default breezAuthGuard;
