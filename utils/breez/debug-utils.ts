import { createLogger } from '@utils/logger';
import useBreezSdkStore from '../../store/breez-sdk.store';

const debugLogger = createLogger('BreezDebug');

/**
 * Test API key availability and format
 */
export const testApiKey = (): void => {
  debugLogger.info('=== API KEY DIAGNOSTIC ===');

  const envApiKey = process.env.BREEZ_API_KEY;

  debugLogger.info('Environment variable check', {
    hasBreezApiKey: !!envApiKey,
    type: typeof envApiKey,
    length: envApiKey ? envApiKey.length : 0,
    firstChars: envApiKey ? `${envApiKey.substring(0, 8)}...` : 'N/A',
    isString: typeof envApiKey === 'string',
    isEmpty: envApiKey === '',
    isUndefined: envApiKey === undefined,
    isNull: envApiKey === null,
  });

  // Test all environment variables that might be relevant
  const allEnvKeys = Object.keys(process.env).filter(key =>
    key.toLowerCase().includes('breez') ||
    key.toLowerCase().includes('api')
  );

  debugLogger.info('Related environment variables', {
    relevantKeys: allEnvKeys,
    values: allEnvKeys.reduce((acc, key) => {
      acc[key] = process.env[key] ? 'present' : 'missing';
      return acc;
    }, {} as Record<string, string>),
  });

  debugLogger.info('=== END API KEY DIAGNOSTIC ===');
};

/**
 * Quick fix attempt for common issues
 */
export const attemptQuickFix = async (): Promise<{ success: boolean; actions: string[] }> => {
  const actions: string[] = [];

  try {
    debugLogger.info('🔧 Attempting quick fix for common issues');

    // 1. Check API key
    const envApiKey = process.env.BREEZ_API_KEY;
    if (!envApiKey) {
      actions.push('❌ API key missing from environment');
      return { success: false, actions };
    }
    actions.push('✅ API key found in environment');

    // 2. Force reset and re-initialize
    actions.push('🔄 Forcing store reset...');
    const store = useBreezSdkStore.getState();
    await store.forceStateReset();

    actions.push('🔄 Forcing fresh initialization...');
    const initResult = await store.initialize();

    if (!initResult) {
      actions.push('❌ Initialization failed');
      return { success: false, actions };
    }
    actions.push('✅ Initialization successful');

    // 3. Validate final state
    const isHealthy = store.isHealthy();
    if (!isHealthy) {
      actions.push('❌ Store still unhealthy');
      return { success: false, actions };
    }
    actions.push('✅ Store is healthy');

    return { success: true, actions };

  } catch (error) {
    actions.push(`❌ Quick fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, actions };
  }
};
