import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/en';
import 'dayjs/locale/pl';
import i18n from './i18next';

dayjs.extend(localizedFormat);
dayjs.extend(localeData);
dayjs.extend(relativeTime);

const setDayjsLocale = () => {
  const currentLang = i18n.language;
  dayjs.locale(currentLang === 'pl' ? 'pl' : 'en');
};

setDayjsLocale();

i18n.on('languageChanged', setDayjsLocale);

export default dayjs;
