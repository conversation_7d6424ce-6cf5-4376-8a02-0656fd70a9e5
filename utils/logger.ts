// biome-ignore-all lint: .

const LOGGING_ENABLED_GLOBAL = true;
const LOGGING_LEVEL = __DEV__ ? 2 : 4;

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

interface LoggerConfig {
  enabled: boolean;
  level: LogLevel;
  prefix: string;
}

class Logger {
  private config: LoggerConfig;

  constructor(prefix: string, level: LogLevel = LogLevel.DEBUG) {
    this.config = {
      enabled: __DEV__,
      level,
      prefix: `[${prefix}]`,
    };
  }

  static create(prefix: string, level?: LogLevel): Logger {
    return new Logger(prefix, level || LOGGING_LEVEL);
  }

  debug(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(`${this.config.prefix} ${message}`, ...args);
    }
  }

  info(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.info(`${this.config.prefix} ${message}`, ...args);
    }
  }

  warn(message: string, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(`${this.config.prefix} ${message}`, ...args);
    }
  }

  error(message: string, error?: unknown, ...args: unknown[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      if (error) {
        console.error(`${this.config.prefix} ${message}:`, error, ...args);
      } else {
        console.error(`${this.config.prefix} ${message}`, ...args);
      }
    }
  }

  operation(operation: string, details?: Record<string, unknown>): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      if (details) {
        console.log(`${this.config.prefix} ${operation}`, details);
      } else {
        console.log(`${this.config.prefix} ${operation}`);
      }
    }
  }

  success(operation: string, details?: Record<string, unknown>): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      if (details) {
        console.log(`${this.config.prefix} ✅ ${operation}`, details);
      } else {
        console.log(`${this.config.prefix} ✅ ${operation}`);
      }
    }
  }

  failure(operation: string, error: unknown, details?: Record<string, unknown>): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const logDetails = details ? { ...details, error } : { error };
      console.error(`${this.config.prefix} ❌ ${operation}`, logDetails);
    }
  }

  scope(scopeName: string): Logger {
    return new Logger(`${this.config.prefix.slice(1, -1)}:${scopeName}`, this.config.level);
  }

  private shouldLog(level: LogLevel): boolean {
    return this.config.enabled && level >= this.config.level && LOGGING_ENABLED_GLOBAL;
  }

  disable(): void {
    this.config.enabled = false;
  }

  enable(): void {
    this.config.enabled = __DEV__;
  }

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }
}

export const breezServiceLogger = Logger.create('BreezSdkService');
export const breezStoreLogger = Logger.create('BreezSdkStore');
export const refreshLogger = Logger.create('RefreshStore');
export const pageLayoutLogger = Logger.create('PageLayout');
export const transactionsLogger = Logger.create('Transactions');

export const createLogger = (prefix: string, level?: LogLevel): Logger => {
  return Logger.create(prefix, level);
};

export default Logger;
