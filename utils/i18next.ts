import cacheService from '@services/cache.service';
import Localization from 'expo-localization';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { CACHE_KEYS } from 'types/cache.types';
import aboutAppEn from '../locales/en/about-app.json' with { type: 'json' };
import en from '../locales/en/common.json' with { type: 'json' };
import navigationEn from '../locales/en/navigation.json' with { type: 'json' };
import securityEn from '../locales/en/security.json' with { type: 'json' };
import walletEn from '../locales/en/wallet.json' with { type: 'json' };
import aboutAppPl from '../locales/pl/about-app.json' with { type: 'json' };
import pl from '../locales/pl/common.json' with { type: 'json' };
import navigationPl from '../locales/pl/navigation.json' with { type: 'json' };
import securityPl from '../locales/pl/security.json' with { type: 'json' };
import walletPl from '../locales/pl/wallet.json' with { type: 'json' };

const resources = {
  en: {
    common: en,
    navigation: navigationEn,
    wallet: walletEn,
    security: securityEn,
    'about-app': aboutAppEn,
  },
  pl: {
    common: pl,
    navigation: navigationPl,
    wallet: walletPl,
    security: securityPl,
    'about-app': aboutAppPl,
  },
};

const getDeviceLanguage = () => {
  try {
    if (
      Localization &&
      typeof Localization.getLocales === 'function' &&
      Array.isArray(Localization.getLocales()) &&
      Localization.getLocales()[0]?.languageCode
    ) {
      return Localization.getLocales()[0].languageCode;
    }
  } catch (e) {
    // biome-ignore lint/suspicious/noConsole: debug
    console.log(e);
  }
  return 'pl'; // fallback
};

i18n.use(initReactI18next).init({
  resources,
  lng: getDeviceLanguage() ?? 'pl',
  fallbackLng: 'pl',

  debug: true,
  ns: ['common', 'navigation', 'wallet', 'security', 'about-app'],

  defaultNS: 'common',
  interpolation: {
    escapeValue: false,
  },
});

// After init, try to load persisted language preference and apply it
(async () => {
  try {
    const prefs = await cacheService.get<{ language?: 'en' | 'pl' }>(
      CACHE_KEYS.USER_PREFERENCES
    );
    const saved = prefs?.language;
    if (saved && saved !== i18n.language) {
      await i18n.changeLanguage(saved);
    }
  } catch {
    // ignore
  }
})();

export default i18n;
