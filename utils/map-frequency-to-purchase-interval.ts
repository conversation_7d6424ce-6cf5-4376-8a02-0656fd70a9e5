import { PurchaseInterval } from "@endpoints/saving-plan.endpoints";
import type { Frequency } from "@store/saving-plan.store";

export const mapFrequencyToPurchaseInterval = (
  frequency?: Frequency
): PurchaseInterval => {
  switch (frequency) {
    case "M":
      return PurchaseInterval.Month;
    case "Q":
      return PurchaseInterval.Quarter;
    default:
      return PurchaseInterval.Week;
  }
};

export const mapPurchaseIntervalToFrequency = (
  interval: PurchaseInterval
): Frequency => {
  switch (interval) {
    case PurchaseInterval.Month:
      return "M";
    case PurchaseInterval.Quarter:
      return "Q";
    default:
      return "W";
  }
};
