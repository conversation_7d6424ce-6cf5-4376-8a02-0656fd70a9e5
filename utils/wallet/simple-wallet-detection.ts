import { fetchAP<PERSON> } from '@api';
import { SecureStorageKeys } from '@const/secure-storage-keys';
import {
  type GetLatestWalletAddressResponse,
  getLatestWalletAddress,
} from '@endpoints/wallet.endpoints';
import { createLogger } from '@utils/logger';
import { DEFAULT_NETWORK } from '@utils/wallet/constants';
import * as SecureStore from 'expo-secure-store';

const logger = createLogger('SimpleWalletDetection');

export interface SimpleWallet {
  id: string;
  hasLocalMnemonic: boolean;
  hasBackendAddress: boolean;
  mnemonic?: string;
  address?: string;
  network: typeof DEFAULT_NETWORK;
  createdAt: Date;
}

export interface WalletDetectionResult {
  hasWallet: boolean;
  wallet?: SimpleWallet;
  error?: string;
}

/**
 * Check if user has a wallet by examining:
 * 1. Secure storage for mnemonic
 * 2. Backend for addresses
 * 3. Sync status between them
 *
 * A wallet is considered valid if local mnemonic exists, even if backend address is pending.
 * This prevents the flash of "no wallet" screen during initial loading while address generation
 * is happening in the background. The wallet interface will show loading states for missing data.
 */
export const detectWallet = async (): Promise<WalletDetectionResult> => {
  try {
    logger.operation('Starting simple wallet detection');

    let foundMnemonic: string | null = null;
    let mnemonicSource: string | null = null;

    try {
      const primaryMnemonic = await SecureStore.getItemAsync('MNEMONIC');
      if (primaryMnemonic && primaryMnemonic.trim().length > 0) {
        foundMnemonic = primaryMnemonic;
        mnemonicSource = 'MNEMONIC';
        logger.debug('Found mnemonic in primary storage', {
          length: primaryMnemonic.length,
        });
      }
    } catch (error) {
      logger.warn('Failed to check mnemonic storage', { error });
    }

    let backendAddress: string | null = null;

    try {
      // Check if background wallet creation is in progress
      // If so, don't show backend address to avoid confusion
      const { default: useBreezSdkStore } = await import(
        '@store/breez-sdk.store'
      );
      const backgroundStatus = useBreezSdkStore
        .getState()
        .getBackgroundCreationStatus();

      const isGeneratingInBackground = [
        'MNEMONIC_STORED',
        'CONNECTING_BREEZ',
        'GENERATING_ADDRESS',
        'SYNCING_BACKEND',
      ].includes(backgroundStatus.status);

      if (isGeneratingInBackground) {
        logger.debug(
          'Background wallet generation in progress, skipping backend address fetch'
        );
      } else {
        logger.debug('Checking backend for user addresses');
        const addressResponse = await fetchAPI<GetLatestWalletAddressResponse>(
          getLatestWalletAddress
        );

        if (addressResponse?.address) {
          backendAddress = addressResponse.address;
          logger.debug('Found address on backend', {
            address: `${backendAddress.substring(0, 10)}...`,
          });
        } else {
          logger.debug('No address found on backend');
        }
      }
    } catch (error) {
      logger.warn('Failed to check backend addresses', { error });
    }

    // >> Show wallet interface if local mnemonic exists, even if backend address is pending
    // This prevents the flash of "no wallet" screen during initial loading/background sync
    const hasLocalMnemonic = !!foundMnemonic;
    const hasBackendAddress = !!backendAddress;

    // A wallet exists if we have a local mnemonic (address may be generating in background)
    const hasWallet = hasLocalMnemonic;

    if (!hasWallet) {
      if (!hasLocalMnemonic && hasBackendAddress) {
        logger.info(
          'Incomplete wallet: has backend address but no local mnemonic'
        );
      } else {
        logger.info('No wallet detected: missing local mnemonic');
      }
      return { hasWallet: false };
    }

    if (hasLocalMnemonic && !hasBackendAddress) {
      logger.info(
        'Wallet found with local mnemonic, backend address may be generating'
      );
    } else if (hasLocalMnemonic && hasBackendAddress) {
      logger.info(
        'Complete wallet found with both local mnemonic and backend address'
      );
    }

    const wallet: SimpleWallet = {
      id: `simple-wallet-${Date.now()}`,
      hasLocalMnemonic,
      hasBackendAddress,
      mnemonic: foundMnemonic || undefined,
      address: backendAddress || undefined,
      network: DEFAULT_NETWORK,
      createdAt: new Date(),
    };

    logger.success('Wallet detected successfully', {
      hasLocalMnemonic,
      hasBackendAddress,
      mnemonicSource,
      addressPrefix: backendAddress
        ? `${backendAddress.substring(0, 10)}...`
        : 'none',
    });

    return { hasWallet: true, wallet };
  } catch (error) {
    logger.error('Wallet detection failed', { error });
    return {
      hasWallet: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

export const debugSecureStorage = async (): Promise<void> => {
  try {
    logger.info('=== SECURE STORAGE DEBUG ===');

    const keysToCheck = [
      SecureStorageKeys.MNEMONIC,
      SecureStorageKeys.BREEZ_WALLET_INFO,
      SecureStorageKeys.BREEZ_LAST_SYNC_TIME,
      SecureStorageKeys.BREEZ_PAYMENT_LIMITS,
      SecureStorageKeys.BREEZ_SERVICE_CONFIG,
      SecureStorageKeys.BREEZ_LAST_BACKUP_TIME,
      SecureStorageKeys.BREEZ_CONNECTION_INFO,
      SecureStorageKeys.BREEZ_WALLET_ADDRESS,
    ];

    for (const key of keysToCheck) {
      try {
        const value = await SecureStore.getItemAsync(key);
        logger.info(`Storage key: ${key}`, {
          hasValue: !!value,
          length: value?.length || 0,
          preview: value ? `${value.substring(0, 20)}...` : 'null',
        });
      } catch {
        logger.info(`Storage key: ${key}`, { error: 'Failed to read' });
      }
    }

    logger.info('=== END STORAGE DEBUG ===');
  } catch (error) {
    logger.error('Storage debug failed', { error });
  }
};
