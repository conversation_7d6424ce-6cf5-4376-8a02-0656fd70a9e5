/**
 * @fileoverview Wallet and Breez SDK Constants
 *
 * Centralized constants for the wallet system and Breez SDK integration.
 * This file eliminates hardcoded strings throughout the codebase.
 */

declare const __DEV__: boolean;

// ============================================================================
// WALLET CONFIGURATION
// ============================================================================

/**
 * Supported wallet networks
 */
export const WALLET_NETWORKS = {
  TESTNET: 'testnet' as const,
  MAINNET: 'mainnet' as const,
} as const;

/**
 * Supported wallet types
 */
export const WALLET_TYPES = {
  CLOUD: 'cloud' as const,
  PRIVATE: 'private' as const,
} as const;

/**
 * Default network selection based on environment
 */
export const DEFAULT_NETWORK = __DEV__ ? WALLET_NETWORKS.TESTNET : WALLET_NETWORKS.MAINNET;

/**
 * Default wallet configuration
 */
export const DEFAULT_WALLET_CONFIG = {
  network: DEFAULT_NETWORK,
  type: WALLET_TYPES.PRIVATE,
} as const;

/**
 * Wallet validation constants
 */
export const WALLET_VALIDATION = {
  MNEMONIC_WORD_COUNT: 12,
  MIN_PASSWORD_LENGTH: 8,
  ADDRESS_MIN_LENGTH: 26,
} as const;

// ============================================================================
// BREEZ SDK CONNECTION STATES
// ============================================================================

/**
 * Breez SDK connection states
 */
export const BREEZ_CONNECTION_STATES = {
  CONNECTED: 'CONNECTED' as const,
  DISCONNECTED: 'DISCONNECTED' as const,
  CONNECTING: 'CONNECTING' as const,
  FAILED: 'FAILED' as const,
} as const;

/**
 * Wallet creation status constants
 */
export const WALLET_CREATION_STATUS = {
  IDLE: 'IDLE' as const,
  MNEMONIC_STORED: 'MNEMONIC_STORED' as const,
  CONNECTING_BREEZ: 'CONNECTING_BREEZ' as const,
  GENERATING_ADDRESS: 'GENERATING_ADDRESS' as const,
  SYNCING_BACKEND: 'SYNCING_BACKEND' as const,
  COMPLETE: 'COMPLETE' as const,
  ERROR: 'ERROR' as const,
} as const;

// ============================================================================
// PAYMENT CONSTANTS
// ============================================================================

/**
 * Payment status constants
 */
export const PAYMENT_STATUS = {
  PENDING: 'pending' as const,
  COMPLETE: 'complete' as const,
  FAILED: 'failed' as const,
} as const;

/**
 * Payment type constants
 */
export const PAYMENT_TYPES = {
  SEND: 'send' as const,
  RECEIVE: 'receive' as const,
} as const;

// ============================================================================
// BREEZ SDK OPERATION MESSAGES
// ============================================================================

/**
 * Loading messages for Breez SDK operations
 */
export const BREEZ_LOADING_MESSAGES = {
  INITIALIZING: 'Initializing Breez SDK' as const,
  CONNECTING: 'Connecting to Breez SDK' as const,
  DISCONNECTING: 'Disconnecting' as const,
  RECONNECTING: 'Reconnecting' as const,
  FORCE_RECONNECTING: 'Force reconnecting' as const,
  CREATING_INVOICE: 'Creating invoice' as const,
  GENERATING_ADDRESS: 'Generating secure address...' as const,
  SYNCING_BACKEND: 'Syncing with backend...' as const,
  SYNCING_WALLET: 'Syncing wallet data...' as const,
} as const;

/**
 * Error messages for Breez SDK operations
 */
export const BREEZ_ERROR_MESSAGES = {
  INITIALIZATION_FAILED: 'Initialization failed' as const,
  CONNECTION_FAILED: 'Connection failed' as const,
  DISCONNECT_FAILED: 'Disconnect failed' as const,
  RECONNECTION_FAILED: 'Reconnection failed' as const,
  FORCE_RECONNECTION_FAILED: 'Force reconnection failed' as const,
  FAST_REFRESH_FAILED: 'Fast refresh failed' as const,
  INVOICE_CREATION_FAILED: 'Failed to create invoice' as const,
  PAYMENT_LIMITS_FAILED: 'Failed to refresh payment limits' as const,
  PAYMENTS_LOAD_FAILED: 'Failed to load payments' as const,
  WALLET_INFO_FAILED: 'Failed to get wallet info' as const,
  ADDRESS_GENERATION_FAILED: 'Failed to generate wallet address' as const,
  NOT_CONNECTED: 'Not connected to Breez SDK' as const,
  NOT_INITIALIZED: 'Service not initialized' as const,
  WALLET_INFO_UNAVAILABLE: 'Wallet info not available' as const,
  PAYMENT_LIMITS_UNAVAILABLE: 'Payment limits not available' as const,
  NO_MNEMONIC: 'No mnemonic provided and none found in storage' as const,
  API_KEY_REQUIRED:
    'API key is required. Provide it in config or set BREEZ_API_KEY environment variable' as const,
  API_KEY_INVALID: 'API key must be a non-empty string' as const,
  SDK_CONFIG_FAILED:
    'Failed to create SDK configuration - SDK may not be properly loaded' as const,
} as const;

// ============================================================================
// BREEZ SDK OPERATION NAMES
// ============================================================================

/**
 * Operation names for Breez SDK operations (used in logging and retry management)
 */
export const BREEZ_OPERATIONS = {
  INITIALIZE: 'initialize Breez SDK' as const,
  CONNECT: 'breez_sdk_connect' as const,
  DISCONNECT: 'disconnect' as const,
  RECONNECT: 'reconnect' as const,
  REFRESH_WALLET_INFO: 'refresh wallet info' as const,
  CREATE_INVOICE: 'createInvoice' as const,
  GET_WALLET_INFO: 'getWalletInfo' as const,
  GET_PAYMENT_LIMITS: 'getPaymentLimits' as const,
  LIST_PAYMENTS: 'listPayments' as const,
  AUTO_INITIALIZE: 'auto_initialize' as const,
} as const;

// ============================================================================
// BREEZ SDK PROGRESS MESSAGES
// ============================================================================

/**
 * Progress messages for background wallet creation
 */
export const WALLET_CREATION_PROGRESS = {
  CONNECTING: 'Connecting to Breez SDK...' as const,
  CONNECTING_WALLET: 'Connecting to wallet...' as const,
  GENERATING_ADDRESS: 'Generating secure address...' as const,
  SYNCING_BACKEND: 'Syncing with backend...' as const,
  SYNCING_WALLET: 'Syncing wallet data...' as const,
  WALLET_READY: 'Wallet ready!' as const,
} as const;

// ============================================================================
// DIAGNOSTIC CONSTANTS
// ============================================================================

/**
 * Diagnostic categories
 */
export const DIAGNOSTIC_CATEGORIES = {
  SERVICE_INITIALIZATION: 'Service Initialization' as const,
  STORE_STATE: 'Store State' as const,
  SECURE_STORAGE: 'Secure Storage' as const,
  STATE_CONSISTENCY: 'State Consistency' as const,
  ENVIRONMENT: 'Environment' as const,
} as const;

/**
 * Diagnostic status levels
 */
export const DIAGNOSTIC_STATUS = {
  HEALTHY: 'healthy' as const,
  WARNING: 'warning' as const,
  ERROR: 'error' as const,
} as const;

/**
 * Diagnostic messages
 */
export const DIAGNOSTIC_MESSAGES = {
  SERVICE_INITIALIZED: 'Service is initialized' as const,
  SERVICE_NOT_INITIALIZED: 'Service not initialized' as const,
  STORE_STATE_ANALYSIS: 'Store state analysis' as const,
  SECURE_STORAGE_ANALYSIS: 'Secure storage analysis' as const,
  STATES_CONSISTENT: 'Service and store states are consistent' as const,
  STATE_INCONSISTENCY: 'State inconsistency detected' as const,
  ENVIRONMENT_CONFIGURED: 'Environment properly configured' as const,
  MISSING_ENV_VARS: 'Missing required environment variables' as const,
} as const;

// ============================================================================
// RETRY MANAGER CONSTANTS
// ============================================================================

/**
 * Retry error messages
 */
export const RETRY_ERROR_MESSAGES = {
  RETRY_IN_PROGRESS: 'Retry already in progress' as const,
  MAX_ATTEMPTS_EXCEEDED: 'Max retry attempts exceeded' as const,
} as const;

/**
 * Non-retryable error patterns
 */
export const NON_RETRYABLE_ERRORS = [
  'Invalid mnemonic',
  'Invalid API key',
  'Wallet already exists',
  'Invalid configuration',
  'Authentication failed',
] as const;

/**
 * Retryable error patterns
 */
export const RETRYABLE_ERRORS = [
  'Already initialized',
  'Connection failed',
  'Network error',
  'Timeout',
  'Too many retry',
  'Liquid tip not available',
  'Service unavailable',
] as const;

// ============================================================================
// STATE MANAGER CONSTANTS
// ============================================================================

/**
 * State sync event types
 */
export const STATE_SYNC_EVENTS = {
  STATE_MISMATCH: 'STATE_MISMATCH' as const,
  STATE_RECOVERED: 'STATE_RECOVERED' as const,
  STATE_CORRUPTED: 'STATE_CORRUPTED' as const,
} as const;

/**
 * State sync actions
 */
export const STATE_SYNC_ACTIONS = {
  RECOVERY_STARTED: 'recovery_started' as const,
  RECOVERY_COMPLETED: 'recovery_completed' as const,
} as const;

/**
 * State validation recommendations
 */
export const STATE_RECOMMENDATIONS = {
  SYNC_INITIALIZATION: 'Synchronize initialization state' as const,
  SYNC_CONNECTION: 'Synchronize connection state' as const,
  UPDATE_CONNECTION_INFO: 'Update connection info state' as const,
  RESET_AND_REINITIALIZE: 'Reset service state and re-initialize' as const,
  UPDATE_STORE_CONNECTION: 'Update store connection state' as const,
} as const;

// ============================================================================
// EXCHANGE RATE CONSTANTS
// ============================================================================

/**
 * Exchange rate event types
 */
export const EXCHANGE_RATE_EVENTS = {
  PRICE_UPDATED: 'PRICE_UPDATED' as const,
  ERROR_OCCURRED: 'ERROR_OCCURRED' as const,
} as const;

/**
 * Exchange rate error messages
 */
export const EXCHANGE_RATE_ERRORS = {
  INITIALIZATION_FAILED: 'Failed to initialize exchange rate service' as const,
  FETCH_FAILED: 'Failed to fetch exchange rates' as const,
  REFRESH_FAILED: 'Failed to refresh exchange rates' as const,
  SERVICE_INIT_FAILED: 'Exchange rate initialization failed' as const,
} as const;

// ============================================================================
// LIQUID NETWORK ADDRESS PREFIXES
// ============================================================================

/**
 * Liquid network address prefixes for validation
 */
export const LIQUID_ADDRESS_PREFIXES = {
  MAINNET_BECH32: 'lq1' as const,
  TESTNET_BECH32: 'tlq1' as const,
  MAINNET_P2SH: 'VJL' as const,
  MAINNET_P2PKH: 'VT' as const,
  MAINNET_P2SH_ALT: 'VG' as const,
  TESTNET_P2SH: 'XJL' as const,
  TESTNET_P2PKH: 'XT' as const,
  TESTNET_P2SH_ALT: 'XG' as const,
} as const;

/**
 * Liquid network URI prefix
 */
export const LIQUID_NETWORK_URI_PREFIX = 'liquidnetwork:' as const;

// ============================================================================
// TIMING CONSTANTS
// ============================================================================

/**
 * Timing constants for various operations
 */
export const TIMING_CONSTANTS = {
  CONNECTION_STABILIZATION_DELAY: 1000 as const, // 1 second
  POST_CONNECTION_DELAY: 2000 as const, // 2 seconds
  SYNC_INTERVAL: 60_000 as const, // 60 seconds
  DIAGNOSTIC_DELAY: 3000 as const, // 3 seconds
} as const;

// ============================================================================
// WALLET DESCRIPTIONS
// ============================================================================

/**
 * Default descriptions for wallet operations
 */
export const WALLET_DESCRIPTIONS = {
  RECEIVE_ADDRESS: 'Wallet receive address' as const,
  PRIMARY_WALLET: 'Primary Wallet Address' as const,
  LIQUID_NETWORK: 'liquid' as const,
} as const;
