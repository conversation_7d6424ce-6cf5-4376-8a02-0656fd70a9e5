/* biome-ignore-all lint: . */

import type {
  AddRecoveryBlobRequest,
  AddWalletAddressRequest,
} from '@endpoints/wallet.endpoints';
import { WalletType } from '@store/wallet.store';
import { createLogger } from '@utils/logger';
import { DEFAULT_NETWORK } from '@utils/wallet/constants';
import {
  useAddRecoveryBlob,
  useAddWalletAddress,
} from '@utils/wallet/wallet-api';
import 'react-native-get-random-values'; // Must be imported before crypto-js
import CryptoJS from 'crypto-js';

const walletIntegrationLogger = createLogger('WalletIntegration');

export const encryptMnemonicDeterministic = (
  mnemonic: string,
  password: string
): {
  encrypted_mnemonic: string;
  encryption_method: 'aes-256-gcm';
} => {
  try {
    const passwordHash = CryptoJS.SHA256(password).toString();
    const salt = passwordHash.substring(0, 64);
    const iv = passwordHash.substring(32, 64);

    walletIntegrationLogger.debug('Generated deterministic salt and IV from password', {
      saltLength: salt.length,
      ivLength: iv.length,
    });

    // Derive key from password using PBKDF2
    const key = CryptoJS.PBKDF2(password, salt, {
      keySize: 256 / 32,
      iterations: 10_000,
    });

    // Encrypt the mnemonic using AES-256-CBC
    const encrypted = CryptoJS.AES.encrypt(mnemonic, key, {
      iv: CryptoJS.enc.Hex.parse(iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    walletIntegrationLogger.debug('Mnemonic encrypted successfully with deterministic method');

    return {
      encrypted_mnemonic: encrypted.toString(),
      encryption_method: 'aes-256-gcm', // Note: Using CBC mode but keeping API contract
    };
  } catch (error) {
    walletIntegrationLogger.error('Failed to encrypt mnemonic deterministically', error);
    throw new Error('wallet:errors.walletCreation.encryptionFailed');
  }
};

/**
 * Decrypt mnemonic from cloud storage using deterministic IV and salt derivation
 * Reconstructs IV and salt from the password
 */
export const decryptMnemonicDeterministic = (
  encryptedMnemonic: string,
  password: string
): string => {
  try {
    walletIntegrationLogger.debug('Starting deterministic mnemonic decryption');

    // Derive the same deterministic salt and IV from password
    const passwordHash = CryptoJS.SHA256(password).toString();
    const salt = passwordHash.substring(0, 64); // First 32 bytes as hex
    const iv = passwordHash.substring(32, 64); // Next 16 bytes as hex

    walletIntegrationLogger.debug('Reconstructed deterministic salt and IV from password');

    // Derive key from password using same parameters as encryption
    const key = CryptoJS.PBKDF2(password, salt, {
      keySize: 256 / 32,
      iterations: 10_000,
    });

    // Decrypt the mnemonic using AES-256-CBC
    const decrypted = CryptoJS.AES.decrypt(encryptedMnemonic, key, {
      iv: CryptoJS.enc.Hex.parse(iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    const mnemonic = decrypted.toString(CryptoJS.enc.Utf8);

    if (!mnemonic || mnemonic.length === 0) {
      throw new Error('wallet:errors.walletCreation.invalidPasswordOrData');
    }

    walletIntegrationLogger.debug('Mnemonic decrypted successfully with deterministic method');
    return mnemonic;
  } catch (error) {
    walletIntegrationLogger.error('Failed to decrypt mnemonic deterministically', error);
    throw new Error('wallet:errors.walletCreation.decryptionFailed');
  }
};

export const useWalletAddressIntegration = () => {
  const addWalletAddressMutation = useAddWalletAddress();

  const storeWalletAddress = async (
    address: string,
    addressType?: 'bitcoin' | 'lightning' | 'liquid',
    label?: string
  ): Promise<boolean> => {
    try {
      walletIntegrationLogger.operation('Storing wallet address via API', {
        address,
        addressType: addressType || 'unknown',
        hasLabel: !!label,
      });

      const request: AddWalletAddressRequest = {
        address,
      };

      await addWalletAddressMutation.mutateAsync(request);

      walletIntegrationLogger.success(
        'Wallet address stored successfully via API'
      );
      return true;
    } catch (error) {
      walletIntegrationLogger.error(
        'Failed to store wallet address via API',
        error
      );
      return false;
    }
  };

  return {
    storeWalletAddress,
    isLoading: addWalletAddressMutation.isPending,
    error: addWalletAddressMutation.error,
  };
};

export const useRecoveryBlobIntegration = () => {
  const addRecoveryBlobMutation = useAddRecoveryBlob();

  const storeRecoveryBlob = async (
    mnemonic: string,
    password: string,
    walletType: WalletType,
    network = DEFAULT_NETWORK
  ): Promise<boolean> => {
    try {
      walletIntegrationLogger.operation('Storing recovery blob via API', {
        walletType,
        network,
      });

      const encryptedData = encryptMnemonicDeterministic(mnemonic, password);

      const request: AddRecoveryBlobRequest = {
        recovery_blob: encryptedData.encrypted_mnemonic,
      };

      await addRecoveryBlobMutation.mutateAsync(request);

      walletIntegrationLogger.success(
        'Recovery blob stored successfully via API',
        {
          walletType,
          network,
          encryptedLength: encryptedData.encrypted_mnemonic.length,
          encryptionMethod: encryptedData.encryption_method,
          isDeterministic: true,
        }
      );
      return true;
    } catch (error) {
      walletIntegrationLogger.error(
        'Failed to store recovery blob via API',
        error
      );
      return false;
    }
  };

  return {
    storeRecoveryBlob,
    isLoading: addRecoveryBlobMutation.isPending,
    error: addRecoveryBlobMutation.error,
  };
};

export default {
  encryptMnemonicDeterministic,
  decryptMnemonicDeterministic,
  useWalletAddressIntegration,
  useRecoveryBlobIntegration,
};
