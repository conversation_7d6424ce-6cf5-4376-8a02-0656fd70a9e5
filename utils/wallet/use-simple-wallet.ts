import { createLogger } from '@utils/logger';
import { useCallback, useEffect, useState } from 'react';
import {
  debugSecureStorage,
  detectWallet,
  type SimpleWallet,
  type WalletDetectionResult,
} from './simple-wallet-detection';

const logger = createLogger('useSimpleWallet');

export interface SimpleWalletState {
  isLoading: boolean;
  isInitialized: boolean;
  hasWallet: boolean;
  wallet: SimpleWallet | null;
  error: string | null;

  hasLocalMnemonic: boolean;
  hasBackendAddress: boolean;
  isInSync: boolean;
}

export interface SimpleWalletActions {
  refresh: () => Promise<void>;
  debugStorage: () => Promise<void>;

  clearWallet: () => void;
  setError: (error: string | null) => void;
}

export interface UseSimpleWalletReturn {
  state: SimpleWalletState;
  actions: SimpleWalletActions;
}

export const useSimpleWallet = (): UseSimpleWalletReturn => {
  const [state, setState] = useState<SimpleWalletState>({
    isLoading: true,
    isInitialized: false,
    hasWallet: false,
    wallet: null,
    error: null,
    hasLocalMnemonic: false,
    hasBackendAddress: false,
    isInSync: false,
  });

  const refresh = useCallback(async () => {
    try {
      logger.operation('Refreshing wallet detection');

      setState((prev) => {
        if (prev.isLoading) return prev;
        return { ...prev, isLoading: true, error: null };
      });

      const result: WalletDetectionResult = await detectWallet();

      if (result.hasWallet && result.wallet) {
        logger.success('Wallet found during refresh', {
          hasLocalMnemonic: result.wallet.hasLocalMnemonic,
          hasBackendAddress: result.wallet.hasBackendAddress,
        });

        setState((prev) => {
          const newState = {
            isLoading: false,
            isInitialized: true,
            hasWallet: true,
            wallet: result.wallet!,
            error: null,
            hasLocalMnemonic: result.wallet!.hasLocalMnemonic,
            hasBackendAddress: result.wallet!.hasBackendAddress,
            isInSync: result.wallet!.hasLocalMnemonic && result.wallet!.hasBackendAddress,
          };

          if (
            prev.isLoading === newState.isLoading &&
            prev.isInitialized === newState.isInitialized &&
            prev.hasWallet === newState.hasWallet &&
            prev.error === newState.error &&
            prev.hasLocalMnemonic === newState.hasLocalMnemonic &&
            prev.hasBackendAddress === newState.hasBackendAddress &&
            prev.isInSync === newState.isInSync &&
            prev.wallet?.id === result.wallet?.id
          ) {
            return prev;
          }

          return { ...prev, ...newState };
        });
      } else {
        logger.info('No wallet found during refresh', { error: result.error });

        setState((prev) => {
          const newState = {
            isLoading: false,
            isInitialized: true,
            hasWallet: false,
            wallet: null,
            error: result.error || null,
            hasLocalMnemonic: false,
            hasBackendAddress: false,
            isInSync: false,
          };

          if (
            prev.isLoading === newState.isLoading &&
            prev.isInitialized === newState.isInitialized &&
            prev.hasWallet === newState.hasWallet &&
            prev.error === newState.error &&
            prev.hasLocalMnemonic === newState.hasLocalMnemonic &&
            prev.hasBackendAddress === newState.hasBackendAddress &&
            prev.isInSync === newState.isInSync &&
            prev.wallet === newState.wallet
          ) {
            return prev;
          }

          return { ...prev, ...newState };
        });
      }
    } catch (error) {
      logger.error('Wallet refresh failed', { error });

      setState((prev) => {
        const newState = {
          isLoading: false,
          isInitialized: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        };

        if (
          prev.isLoading === newState.isLoading &&
          prev.isInitialized === newState.isInitialized &&
          prev.error === newState.error
        ) {
          return prev;
        }

        return { ...prev, ...newState };
      });
    }
  }, []);

  const debugStorage = useCallback(async () => {
    try {
      await debugSecureStorage();
    } catch (error) {
      logger.error('Storage debug failed', { error });
    }
  }, []);

  const clearWallet = useCallback(() => {
    logger.operation('Clearing wallet state');
    setState((prev) => ({
      ...prev,
      hasWallet: false,
      wallet: null,
      error: null,
      hasLocalMnemonic: false,
      hasBackendAddress: false,
      isInSync: false,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({ ...prev, error }));
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: infinite loop
  useEffect(() => {
    logger.operation('Initializing simple wallet system');
    refresh();
  }, []);

  const actions: SimpleWalletActions = {
    refresh,
    debugStorage,
    clearWallet,
    setError,
  };

  return { state, actions };
};

export const useWalletCreationStatus = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);

  const startCreation = useCallback(() => {
    setIsCreating(true);
    setCreationError(null);
  }, []);

  const completeCreation = useCallback(() => {
    setIsCreating(false);
    setCreationError(null);
  }, []);

  const failCreation = useCallback((error: string) => {
    setIsCreating(false);
    setCreationError(error);
  }, []);

  return {
    isCreating,
    creationError,
    startCreation,
    completeCreation,
    failCreation,
  };
};
