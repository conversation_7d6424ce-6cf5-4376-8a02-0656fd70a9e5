/** biome-ignore-all lint/suspicious/noConsole: debug logging */

import { fetchAPI, getQueryClient, getQueryData } from '@api';
import {
  type AddRecoveryBlobRequest,
  type AddRecoveryBlobResponse,
  type AddWalletAddressRequest,
  type AddWalletAddressResponse,
  addRecoveryBlob,
  addWalletAddress,
  type DistributeBtcRequest,
  type DistributeBtcResponse,
  distributeBtcAmongUsers,
  type GetLatestRecoveryBlobResponse,
  type GetLatestWalletAddressResponse,
  type GetRecoveryBlobsResponse,
  type GetWalletAddressesResponse,
  getAllRecoveryBlobs,
  getAllWalletAddresses,
  getLatestRecoveryBlob,
  getLatestWalletAddress,
  type WalletApiError,
} from '@endpoints/wallet.endpoints';
import { useMutation, useQuery } from '@tanstack/react-query';
import { createLogger } from '../logger';

const walletApiLogger = createLogger('WalletAPI');

export const walletQueryKeys = {
  all: ['wallet'] as const,
  addresses: () => [...walletQueryKeys.all, 'addresses'] as const,
  allAddresses: () => [...walletQueryKeys.addresses(), 'all'] as const,
  latestAddress: () => [...walletQueryKeys.addresses(), 'latest'] as const,
  recoveryBlobs: () => [...walletQueryKeys.all, 'recovery-blobs'] as const,
  allRecoveryBlobs: () => [...walletQueryKeys.recoveryBlobs(), 'all'] as const,
  latestRecoveryBlob: () =>
    [...walletQueryKeys.recoveryBlobs(), 'latest'] as const,
  distribution: (taskId?: string) =>
    taskId
      ? ([...walletQueryKeys.all, 'distribution', taskId] as const)
      : ([...walletQueryKeys.all, 'distribution'] as const),
};

export const useDistributeBtc = () => {
  const queryClient = getQueryClient();

  return useMutation<
    DistributeBtcResponse,
    WalletApiError,
    DistributeBtcRequest
  >({
    mutationFn: async (data: DistributeBtcRequest) => {
      walletApiLogger.operation('Starting Bitcoin distribution', {
        destinationCount: data.destinations.length,
        totalAmount: data.destinations.reduce(
          (sum, dest) => sum + dest.amount_sat,
          0
        ),
      });

      const response = await fetchAPI<DistributeBtcResponse>(
        distributeBtcAmongUsers,
        {
          data,
        }
      );

      walletApiLogger.success('Bitcoin distribution initiated', {
        taskId: response.task_id,
        totalTransactions: response.total_transactions,
      });

      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: walletQueryKeys.distribution(),
      });

      walletApiLogger.debug('Bitcoin distribution cache invalidated');
    },
    onError: (error) => {
      walletApiLogger.error('Bitcoin distribution failed', error);
    },
  });
};

export const useAddWalletAddress = () => {
  const queryClient = getQueryClient();

  return useMutation<
    AddWalletAddressResponse,
    WalletApiError,
    AddWalletAddressRequest
  >({
    mutationFn: async (data: AddWalletAddressRequest) => {
      walletApiLogger.operation('Adding wallet address', {
        address: data.address,
      });

      const response = await fetchAPI<AddWalletAddressResponse>(
        addWalletAddress,
        {
          data,
        }
      );

      walletApiLogger.success('Wallet address added successfully', {
        status: response.status,
      });

      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: walletQueryKeys.addresses() });

      walletApiLogger.debug('Wallet address cache invalidated');
    },
    onError: (error) => {
      walletApiLogger.error('Failed to add wallet address', error);
    },
  });
};

export const useGetAllWalletAddresses = (options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  const query = useQuery({
    ...getQueryData<GetWalletAddressesResponse>(getAllWalletAddresses),
    queryKey: walletQueryKeys.allAddresses(),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
  });

  if (query.data) {
    walletApiLogger.debug('Wallet addresses fetched', {
      count: query.data.addresses.length,
    });
  }
  if (query.error) {
    walletApiLogger.error('Failed to fetch wallet addresses', query.error);
  }

  return query;
};

export const useGetLatestWalletAddress = (options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  const query = useQuery({
    ...getQueryData<GetLatestWalletAddressResponse>(getLatestWalletAddress),
    queryKey: walletQueryKeys.latestAddress(),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 2 * 60 * 1000, // 2 minutes
  });

  if (query.data) {
    walletApiLogger.debug('Latest wallet address fetched', {
      hasAddress: !!query.data.address,
    });
  }
  if (query.error) {
    walletApiLogger.error('Failed to fetch latest wallet address', query.error);
  }

  return query;
};

export const useAddRecoveryBlob = () => {
  const queryClient = getQueryClient();

  return useMutation<
    AddRecoveryBlobResponse,
    WalletApiError,
    AddRecoveryBlobRequest
  >({
    mutationFn: async (data: AddRecoveryBlobRequest) => {
      walletApiLogger.operation('Adding recovery blob', {
        blobLength: data.recovery_blob.length,
      });

      const response = await fetchAPI<AddRecoveryBlobResponse>(
        addRecoveryBlob,
        {
          data,
        }
      );

      walletApiLogger.success('Recovery blob added successfully', {
        status: response.status,
      });

      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: walletQueryKeys.recoveryBlobs(),
      });

      walletApiLogger.debug('Recovery blob cache invalidated');
    },
    onError: (error) => {
      walletApiLogger.error('Failed to add recovery blob', error);
    },
  });
};

export const useGetAllRecoveryBlobs = (options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  const query = useQuery({
    ...getQueryData<GetRecoveryBlobsResponse>(getAllRecoveryBlobs),
    queryKey: walletQueryKeys.allRecoveryBlobs(),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 10 * 60 * 1000,
  });

  if (query.data) {
    walletApiLogger.debug('Recovery blobs fetched', {
      count: query.data.recovery_blobs.length,
    });
  }
  if (query.error) {
    walletApiLogger.error('Failed to fetch recovery blobs', query.error);
  }

  return query;
};

export const useGetLatestRecoveryBlob = (options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  const query = useQuery({
    ...getQueryData<GetLatestRecoveryBlobResponse>(getLatestRecoveryBlob),
    queryKey: walletQueryKeys.latestRecoveryBlob(),
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
  });

  if (query.data) {
    walletApiLogger.debug('Latest recovery blob fetched', {
      hasBlob: !!query.data.recovery_blob,
    });
  }
  if (query.error) {
    walletApiLogger.error('Failed to fetch latest recovery blob', query.error);
  }

  return query;
};

export const invalidateWalletQueries = (
  queryClient: ReturnType<typeof getQueryClient>
) => {
  queryClient.invalidateQueries({ queryKey: walletQueryKeys.all });
  walletApiLogger.debug('All wallet queries invalidated');
};

export const prefetchWalletAddresses = async (
  queryClient: ReturnType<typeof getQueryClient>
) => {
  await queryClient.prefetchQuery({
    ...getQueryData<GetWalletAddressesResponse>(getAllWalletAddresses),
    queryKey: walletQueryKeys.allAddresses(),
    staleTime: 5 * 60 * 1000,
  });

  walletApiLogger.debug('Wallet addresses prefetched');
};

export const prefetchRecoveryBlobs = async (
  queryClient: ReturnType<typeof getQueryClient>
) => {
  await queryClient.prefetchQuery({
    ...getQueryData<GetRecoveryBlobsResponse>(getAllRecoveryBlobs),
    queryKey: walletQueryKeys.allRecoveryBlobs(),
    staleTime: 10 * 60 * 1000,
  });

  walletApiLogger.debug('Recovery blobs prefetched');
};

export default {
  useDistributeBtc,
  useAddWalletAddress,
  useGetAllWalletAddresses,
  useGetLatestWalletAddress,
  useAddRecoveryBlob,
  useGetAllRecoveryBlobs,
  useGetLatestRecoveryBlob,

  invalidateWalletQueries,
  prefetchWalletAddresses,
  prefetchRecoveryBlobs,

  walletQueryKeys,
};
