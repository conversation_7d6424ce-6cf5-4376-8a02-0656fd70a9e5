/**
 * @fileoverview Simple Wallet Types
 *
 * Simplified type definitions for the wallet system, replacing the complex
 * domain value objects with simple, maintainable types.
 */

import type { WALLET_NETWORKS } from './constants';

/**
 * Network types for wallet operations
 */
export type Network = (typeof WALLET_NETWORKS)[keyof typeof WALLET_NETWORKS];

/**
 * Wallet type definitions
 */
export type WalletType = 'cloud' | 'private';

/**
 * Simple wallet configuration for creation
 */
export interface SimpleWalletConfig {
  network: Network;
  type: WalletType;
  mnemonic: string;
  password?: string;
}

/**
 * Wallet state interface
 */
export interface WalletState {
  hasWallet: boolean;
  hasLocalMnemonic: boolean;
  hasBackendAddress: boolean;
  isInSync: boolean;
  isLoading: boolean;
  error: string | null;
  wallet: {
    address: string;
    network: Network;
    type: WalletType;
  } | null;
}

/**
 * Wallet actions interface
 */
export interface WalletActions {
  refresh: () => Promise<void>;
  clearWallet: () => Promise<void>;
  debugStorage: () => Promise<void>;
}

/**
 * Simple wallet hook return type
 */
export interface UseSimpleWalletReturn {
  state: WalletState;
  actions: WalletActions;
}
