/**
 * Async utility functions for handling promises, timeouts, and error handling
 */
/** biome-ignore-all lint/suspicious/noExplicitAny: any is required for error handling */
/** biome-ignore-all lint/suspicious/noConsole: debug */
/** biome-ignore-all lint/correctness/noUndeclaredVariables: global dev flag */

/**
 * Timeout values for different types of operations
 */
export const TIMEOUT_VALUES = {
  SYNC: 120_000,
  WALLET_INFO: 30_000,
  STORAGE: 5000,
  QUICK: 3000,
} as const;

/**
 * Wraps a promise with a timeout to prevent indefinite hanging
 *
 * @param promise - The promise to wrap with timeout
 * @param timeoutMs - Timeout in milliseconds
 * @param operation - Description of the operation for error messages
 * @returns Promise that resolves with the original promise or rejects with timeout error
 *
 * @example
 * ```typescript
 * const result = await withTimeout(
 *   fetchData(),
 *   TIMEOUT_VALUES.SYNC,
 *   'Data fetch operation'
 * );
 * ```
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operation: string
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(
        () => reject(new Error(`${operation} timed out after ${timeoutMs}ms`)),
        timeoutMs
      )
    ),
  ]);
}

/**
 * Retry a promise-based operation with exponential backoff
 *
 * @param operation - Function that returns a promise
 * @param maxRetries - Maximum number of retry attempts
 * @param baseDelay - Base delay in milliseconds (will be doubled each retry)
 * @param operationName - Name of the operation for logging
 * @returns Promise that resolves with the operation result or rejects after all retries
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000,
  operationName = 'Operation'
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // biome-ignore lint/nursery/noAwaitInLoop: we need to await operation here
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        throw new Error(
          `${operationName} failed after ${maxRetries + 1} attempts. Last error: ${lastError.message}`
        );
      }

      const delay = baseDelay * 2 ** attempt;
      if (__DEV__) {
        console.warn(
          `[AsyncUtils] ${operationName} attempt ${attempt + 1} failed, retrying in ${delay}ms:`,
          lastError.message
        );
      }

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Debounce a promise-based function to prevent rapid successive calls
 *
 * @param fn - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export function debounceAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastPromise: Promise<any> | null = null;

  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (lastPromise) {
      return lastPromise;
    }

    lastPromise = new Promise((resolve, reject) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          lastPromise = null;
          timeoutId = null;
        }
      }, delay);
    });

    return lastPromise;
  }) as T;
}

/**
 * Extract a user-friendly error message from an error object
 *
 * @param error - The error to extract message from
 * @param fallbackMessage - Default message if error message is not available
 * @returns User-friendly error message
 */
export function getErrorMessage(
  error: unknown,
  fallbackMessage = 'An unexpected error occurred. Please try again.'
): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return fallbackMessage;
}

/**
 * Handle wallet creation errors with consistent logging and user feedback
 *
 * @param error - The error that occurred
 * @param operation - Description of the operation that failed
 * @param setErrorState - Function to set error state in component
 * @param fallbackMessage - Custom fallback message
 */
export function handleWalletCreationError(
  error: unknown,
  operation: string,
  setErrorState: (message: string) => void,
  fallbackMessage?: string
): void {
  if (__DEV__) {
    console.error(`[${operation}] Wallet creation error:`, error);
  }

  const errorMessage = getErrorMessage(
    error,
    fallbackMessage ||
    'wallet:errors.walletCreation.generic'
  );

  setErrorState(errorMessage);
}
