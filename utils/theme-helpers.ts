export const getColorFromTheme = (
  colors: Record<string, unknown>,
  color?: string
): string | undefined => {
  if (!color) {
    return;
  }

  const colorPath = color.split('.');
  let currentColor = colors;

  for (const key of colorPath) {
    if (
      currentColor &&
      typeof currentColor === 'object' &&
      key in currentColor
    ) {
      currentColor = currentColor[key] as Record<string, unknown>;
    } else {
      return;
    }
  }

  if (typeof currentColor === 'string') {
    return currentColor;
  }

  return;
};

/**
 * Adds an alpha channel to a HEX color.
 *
 * @param color The HEX color string (e.g., '#RRGGBB' or 'RRGGBB'). It can handle 3-digit and 6-digit hex codes.
 * @param alpha The alpha transparency value, from 0 (fully transparent) to 1 (fully opaque).
 * @returns The HEX color with the applied alpha channel (e.g., '#RRGGBBAA'). If the input color is invalid, it's returned unchanged.
 */
export const getColorWithAlpha = (
  color?: string,
  alpha = 1
): string | undefined => {
  if (!color) {
    return;
  }

  let processedColor = color.startsWith('#') ? color.slice(1) : color;

  if (processedColor.length === 3) {
    processedColor = [...processedColor].map((char) => char + char).join('');
  }

  if (processedColor.length !== 6) {
    return color;
  }

  const clampedAlpha = Math.max(0, Math.min(1, alpha));
  const alphaHex = Math.round(clampedAlpha * 255)
    .toString(16)
    .padStart(2, '0');

  return `#${processedColor}${alphaHex}`;
};
