import {
  deleteItemAsync as deleteItemAsyncSecureStore,
  getItemAsync as getItemAsyncSecureStore,
  setItemAsync as setItemAsyncSecureStore,
  WHEN_UNLOCKED,
} from 'expo-secure-store';
import { SecureStorageKeys } from '../const/secure-storage-keys';

export const saveToken = async (token: string) => {
  await setItemAsyncSecureStore(SecureStorageKeys.AUTH_TOKEN, token, {
    keychainAccessible: WHEN_UNLOCKED,
  });
};

export const saveRefreshToken = async (token: string) => {
  await setItemAsyncSecureStore(SecureStorageKeys.REFRESH_TOKEN, token, {
    keychainAccessible: WHEN_UNLOCKED,
  });
};

export const loadToken = async () => {
  return await getItemAsyncSecureStore(SecureStorageKeys.AUTH_TOKEN, {
    keychainAccessible: WHEN_UNLOCKED,
  });
};

export const loadRefreshToken = async () => {
  return await getItemAsyncSecureStore(SecureStorageKeys.REFRESH_TOKEN, {
    keychainAccessible: WHEN_UNLOCKED,
  });
};

export const clearToken = async () => {
  await deleteItemAsyncSecureStore(SecureStorageKeys.AUTH_TOKEN);
};

export const clearRefreshToken = async () => {
  await deleteItemAsyncSecureStore(SecureStorageKeys.REFRESH_TOKEN);
};
