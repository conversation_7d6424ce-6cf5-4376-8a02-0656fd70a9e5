import { z } from 'zod';

const passwordValidation = {
  HAS_UPPERCASE: /(?=.*[A-Z])/,
  HAS_LOWERCASE: /(?=.*[a-z])/,
  HAS_NUMBER: /(?=.*[0-9])/,
  HAS_SYMBOL: /(?=.*[!@#$%^&*])/,
  MIN_LENGTH: /.{12,}/,
  MIN_LENGTH_WALLET: /.{8,}/,
};

export const hasUppercaseSchema = z
  .string()
  .regex(
    passwordValidation.HAS_UPPERCASE,
    'validators.password.passwordStrength'
  );
export const hasLowercaseSchema = z
  .string()
  .regex(
    passwordValidation.HAS_LOWERCASE,
    'validators.password.passwordLetter'
  );
export const hasNumberSchema = z
  .string()
  .regex(passwordValidation.HAS_NUMBER, 'validators.password.requiredOneDigit');
export const hasSymbolSchema = z
  .string()
  .regex(
    passwordValidation.HAS_SYMBOL,
    'validators.password.passwordSpecialSign'
  );
export const minLengthSchema = z
  .string()
  .regex(passwordValidation.MIN_LENGTH, 'validators.password.min');
export const maxLengthSchema = z.string().max(24, 'validators.password.max');
export const minLengthWalletSchema = z
  .string()
  .regex(passwordValidation.MIN_LENGTH_WALLET, 'validators.password.minWallet');

export const passwordSchema = z
  .string()
  .nonempty('validators.password.required')
  .regex(
    passwordValidation.HAS_UPPERCASE,
    'validators.password.passwordStrength'
  )
  .regex(passwordValidation.HAS_LOWERCASE, 'validators.password.passwordLetter')
  .regex(passwordValidation.HAS_NUMBER, 'validators.password.requiredOneDigit')
  .regex(
    passwordValidation.HAS_SYMBOL,
    'validators.password.passwordSpecialSign'
  )
  .regex(passwordValidation.MIN_LENGTH, 'validators.password.min')
  .max(24, 'validators.password.max');

export const passwordSignInSchema = passwordSchema;

export const passwordWalletSchema = z
  .string()
  .nonempty('validators.password.required')
  .regex(passwordValidation.MIN_LENGTH_WALLET, 'validators.password.minWallet')
  .regex(passwordValidation.HAS_LOWERCASE, 'validators.password.passwordLetter')
  .regex(passwordValidation.HAS_NUMBER, 'validators.password.requiredOneDigit');

export const passwordConfirmWalletSchema = z
  .object({
    password: z.string(),
    passwordConfirm: z.string().nonempty('validators.password.required'),
  })
  .superRefine((data, ctx) => {
    if (data.password !== data.passwordConfirm) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'validators.password.passwordsMustMatch',
        path: ['passwordConfirm'],
      });
    }
  });

export const passwordConfirmSchema = z
  .object({
    password: passwordSchema,
    passwordConfirm: z.string().nonempty('validators.password.required'),
  })
  .superRefine((data, ctx) => {
    if (data.password !== data.passwordConfirm) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'validators.password.passwordsMustMatch',
        path: ['passwordConfirm'],
      });
    }
  });
