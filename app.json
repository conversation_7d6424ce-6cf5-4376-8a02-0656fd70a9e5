{"expo": {"name": "reti-app", "slug": "reti-app", "owner": "reti-app", "scheme": "retiapp", "version": "1.0.3", "orientation": "portrait", "backgroundColor": "#FFF", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/img.png", "resizeMode": "cover", "backgroundColor": "#FFF"}, "assetBundlePatterns": ["**/*"], "runtimeVersion": "1.0.3", "ios": {"supportsTablet": true, "infoPlist": {"CFBundleAllowMixedLocalizations": true, "ITSAppUsesNonExemptEncryption": false, "NSFaceIDUsageDescription": "This app uses Face ID to secure your wallet and sensitive data.", "NSCameraUsageDescription": "This app needs access to camera to take photos for identity verification.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select photos for identity verification."}, "bundleIdentifier": "tech.reti"}, "android": {"edgeToEdge": true, "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFF"}, "package": "com.reti.tech", "permissions": ["android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": [["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.tech.reti"}], ["expo-local-authentication", {"faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], "expo-router", "expo-localization", "expo-secure-store"], "extra": {"router": {"origin": false}, "eas": {"projectId": "9a92c767-953e-49b6-8b05-ded5fadd94ac"}}, "updates": {"url": "https://u.expo.dev/9a92c767-953e-49b6-8b05-ded5fadd94ac"}}}