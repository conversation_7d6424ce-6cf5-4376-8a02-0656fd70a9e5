{"name": "reti", "version": "1.0.3", "main": "index.ts", "scripts": {"start:dev": "NODE_ENV=development expo start", "start:test": "NODE_ENV=test expo start", "start:prod": "NODE_ENV=production expo start", "android:dev": "ENVFILE=.env.dev react-native run-android", "android:test": "ENVFILE=.env.test react-native run-android", "ios:dev": "ENVFILE=.env.dev react-native run-ios", "ios:test": "ENVFILE=.env.test react-native run-ios", "preview:android": "eas build --platform android --profile preview", "preview:ios": "eas build --platform ios --profile preview", "build:dev:android": "eas build --platform android --profile development", "build:dev:ios": "eas build --platform ios --profile development", "build-check": "tsc --noEmit --skipLib<PERSON>heck -p tsconfig.json", "android": "expo run:android", "ios": "expo run:ios", "android:prebuild": "npx expo prebuild --platform android", "postinstall": "patch-package", "format": "ultracite format"}, "dependencies": {"@breeztech/react-native-breez-sdk-liquid": "^0.8.0", "@expo-google-fonts/rubik": "^0.3.0", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.83.0", "@veriff/react-native-sdk": "^9.0.0", "axios": "^1.9.0", "babel-preset-expo": "^13.2.3", "bip39": "^3.1.0", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-clipboard": "^7.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "^5.2.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": " ~2.1.7", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-local-authentication": "^16.0.5", "expo-localization": "~16.1.5", "expo-router": "~5.0.6", "expo-screen-capture": "^7.1.5", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.16", "expo-web-browser": "~14.1.6", "i18next": "^25.3.2", "randombytes": "^2.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.11", "react-native-edge-to-edge": "^1.6.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.62", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-markdown-display": "^7.0.2", "react-native-mask-text": "^0.14.2", "react-native-nitro-modules": "^0.26.3", "react-native-paper": "^5.14.0", "react-native-reanimated": "~3.17.5", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-unistyles": "^3.0.4", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "safe-buffer": "^5.2.1", "yup": "^1.6.1", "zod": "^4.0.5", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@biomejs/biome": "2.1.1", "@react-native-community/cli": "latest", "@types/crypto-js": "^4.2.2", "@types/randombytes": "^2.0.3", "@types/react": "~19.0.10", "cross-env": "^7.0.3", "husky": "^9.1.7", "patch-package": "^8.0.0", "typescript": "~5.8.3", "ultracite": "5.0.36"}, "private": true}