import { createEndpoint, METHOD } from '@api';

export interface IRegister {
  username: string;
  password: string;
}

export interface IRegisterPostResponse {
  id: string;
  username: string;
}

export interface IConfirmGetResponse {
  message: string;
}

export const createAccount = createEndpoint({
  method: METHOD.POST,
  route: '/register/',
});

export const confirmRegistration = createEndpoint({
  method: METHOD.PATCH,
  route: '/confirm-registration-code/',
});

export const resendCode = createEndpoint({
  method: METHOD.POST,
  route: '/confirm-registration-code/',
});
