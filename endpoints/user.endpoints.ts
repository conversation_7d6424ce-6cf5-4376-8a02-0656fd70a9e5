import { createEndpoint, METHOD } from "@api";

export interface UserInfo {
  username: string;
}

export interface Document {
  id: string;
  is_active: boolean;
  document_number: string;
  country_of_document: string;
  valid_from: string;
  valid_until: string;
  type_of_document: string;
}

export interface Profile {
  id: string;
  user: string;
  is_active: boolean;
  first_and_middle_name: string;
  last_name: string;
  nationality: string;
  personal_number: string;
  date_of_birth: string;
  documents: Document[];
}

export interface ProfileInfoResponse {
  id: string;
  username: string;
  profile?: Profile;
}

export const getUserInfo = createEndpoint({
  method: METHOD.GET,
  route: "/reti-app/user-info/",
});

export const getProfileInfo = createEndpoint({
  method: METHOD.GET,
  route: "/reti-app/profile-info/",
});
