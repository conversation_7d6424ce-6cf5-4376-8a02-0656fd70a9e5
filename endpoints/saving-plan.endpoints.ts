import { createEndpoint, METHOD } from "@api";
import type { Frequency, PlanType } from "store/saving-plan.store";

export interface Target {
  id: string;
  icon_label: string;
  name?: string;
  is_active?: boolean;
}

export interface SavingPlansData {
  targets: [Target[]];
}

export const PurchaseInterval = {
  Week: "W",
  Month: "M",
  Quarter: "Q",
} as const;

export type PurchaseInterval =
  (typeof PurchaseInterval)[keyof typeof PurchaseInterval];

export interface SavingPlanPostProps {
  name?: string;
  plan_type?: PlanType;
  percentage_value?: number;
  ignore_transaction_above?: number;
  time_horizon: number;
  amount_value?: number;
  purchase_interval?: Frequency | null;
  target_id?: string;
}

export interface SavingPlanProps extends SavingPlanPostProps {
  user: string;
  name: string;
  plan_type: PlanType;
  percentage_value: number;
  amount_value: number;
  ignore_transaction_above: number;
  saving_enabled: boolean;
  time_horizon: number;
  purchase_interval?: PurchaseInterval;
  target: Target;
}

export interface SavingPlanTargetProps {
  id: string;
  icon_label: string;
  icon?: string;
  is_active?: boolean;
}

export interface SavingPlanConfigParametersProps {
  optimal_border: 0;
  optimal_border_message: "string";
  danger_border: 0;
  danger_border_message: "string";
  max_border: number;
  max_border_message: string;
}

export const getSavingPlanSettings = createEndpoint({
  method: METHOD.GET,
  route: "/savings-to-crypto-app/saving-plan-settings/",
});

export const postSavingPlanSettings = createEndpoint({
  method: METHOD.POST,
  route: "/savings-to-crypto-app/saving-plan-settings/",
});

export const patchSavingPlanSettings = createEndpoint({
  method: METHOD.PATCH,
  route: "/savings-to-crypto-app/saving-plan-settings/",
});

export const getSavingPlanTargets = createEndpoint({
  method: METHOD.GET,
  route: "/savings-to-crypto-app/saving-plan-settings/config-targets/",
});

export const postSavingPlanTargets = createEndpoint({
  method: METHOD.POST,
  route: "/savings-to-crypto-app/saving-plan-settings/config-targets/",
});

export const getSavingPlanConfigParameters = createEndpoint({
  method: METHOD.GET,
  route: "/savings-to-crypto-app/saving-plan-settings/config-parameters/",
});
