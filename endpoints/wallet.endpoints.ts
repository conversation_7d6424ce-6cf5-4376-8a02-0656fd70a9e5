/** biome-ignore-all lint/suspicious/noExplicitAny: . */
import { createEndpoint, METHOD } from '@api';

// ============================================================================
// BITCOIN DISTRIBUTION INTERFACES
// ============================================================================

export interface BitcoinDestination {
  /** Bitcoin address to send to */
  address: string;
  /** Amount in satoshis */
  amount_sat: number;
}

export interface DistributeBtcRequest {
  /** Array of destinations for Bitcoin distribution */
  destinations: BitcoinDestination[];
}

export interface DistributeBtcResponse {
  /** Unique task identifier for tracking the distribution */
  task_id: string;
  /** Current status of the distribution task */
  status: 'started' | 'processing' | 'completed' | 'failed';
  /** Total number of transactions to be processed */
  total_transactions: number;
  /** Optional: Estimated completion time */
  estimated_completion?: string;
  /** Optional: Error message if status is 'failed' */
  error_message?: string;
}

// ============================================================================
// WALLET ADDRESS MANAGEMENT INTERFACES
// ============================================================================

export interface WalletAddress {
  /** Unique identifier for the address */
  id: string;
  /** The Bitcoin/Lightning address */
  address: string;
  /** Type of address (bitcoin, lightning, liquid) */
  address_type: 'bitcoin' | 'lightning' | 'liquid';
  /** User-friendly label for the address */
  label?: string;
  /** Whether this is the primary/default address */
  is_primary: boolean;
  /** Timestamp when address was created */
  created_at: string;
  /** Timestamp when address was last updated */
  updated_at: string;
  /** Whether the address is still active/valid */
  is_active: boolean;
}

export interface AddWalletAddressRequest {
  address: string;
}

export interface AddWalletAddressResponse {
  status: 'success';
}

export interface GetWalletAddressesResponse {
  addresses: string[];
}

export interface GetLatestWalletAddressResponse {
  address: string;
}

// ============================================================================
// RECOVERY BLOB MANAGEMENT INTERFACES
// ============================================================================

export interface RecoveryBlob {
  /** Unique identifier for the recovery blob */
  id: string;
  /** Encrypted mnemonic data */
  encrypted_mnemonic: string;
  /** Encryption method used */
  encryption_method: 'aes-256-gcm' | 'aes-256-cbc';
  /** Salt used for encryption (if applicable) */
  salt?: string;
  /** Initialization vector for encryption */
  iv: string;
  /** User-friendly label for the recovery blob */
  label?: string;
  /** Timestamp when blob was created */
  created_at: string;
  /** Timestamp when blob was last accessed */
  last_accessed_at?: string;
  /** Whether the blob is still active */
  is_active: boolean;
  /** Optional metadata */
  metadata?: {
    wallet_type?: string;
    network?: string;
    [key: string]: any;
  };
}

export interface AddRecoveryBlobRequest {
  recovery_blob: string;
}

export interface AddRecoveryBlobResponse {
  status: 'success';
}

export interface GetRecoveryBlobsResponse {
  recovery_blobs: string[];
}

export interface GetLatestRecoveryBlobResponse {
  recovery_blob: string;
}

// ============================================================================
// COMMON ERROR INTERFACES
// ============================================================================

export interface WalletApiError {
  /** Error code */
  code: string;
  /** Human-readable error message */
  message: string;
  /** Additional error details */
  details?: {
    field?: string;
    validation_errors?: string[];
    [key: string]: any;
  };
  /** HTTP status code */
  status: number;
}

// ============================================================================
// ENDPOINT DEFINITIONS
// ============================================================================

// Bitcoin Distribution Endpoint
export const distributeBtcAmongUsers = createEndpoint({
  method: METHOD.POST,
  route: '/savings-to-crypto-app/web3/distribute-btc-among-users/',
});

// Wallet Address Management Endpoints
export const addWalletAddress = createEndpoint({
  method: METHOD.POST,
  route: '/savings-to-crypto-app/web3/wallet/addresses/add/',
});

export const getAllWalletAddresses = createEndpoint({
  method: METHOD.GET,
  route: '/savings-to-crypto-app/web3/wallet/addresses/all/',
});

export const getLatestWalletAddress = createEndpoint({
  method: METHOD.GET,
  route: '/savings-to-crypto-app/web3/wallet/addresses/latest/',
});

// Recovery Blob Management Endpoints
export const addRecoveryBlob = createEndpoint({
  method: METHOD.POST,
  route: '/savings-to-crypto-app/web3/wallet/recovery-blobs/add/',
});

export const getAllRecoveryBlobs = createEndpoint({
  method: METHOD.GET,
  route: '/savings-to-crypto-app/web3/wallet/recovery-blobs/all/',
});

export const getLatestRecoveryBlob = createEndpoint({
  method: METHOD.GET,
  route: '/savings-to-crypto-app/web3/wallet/recovery-blobs/latest/',
});

// Note: Types are already exported via interface declarations above
// No need for separate export type block to avoid conflicts
