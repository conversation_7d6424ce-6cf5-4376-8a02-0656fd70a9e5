import { createEndpoint, METHOD } from '@api';

export interface Agreement {
  id: number;
  label: string;
  required: boolean;
  name: string;
  checked?: boolean;
  content?: string;
}

export interface TermsOfUseResponse {
  version: string;
  agreements: Agreement[];
}

export interface TermsPostData {
  username: string;
  agreements: Agreement[];
}

export const getTerms = createEndpoint({
  method: METHOD.GET,
  route: '/terms-and-conditions/',
});

export const postTerms = createEndpoint({
  method: METHOD.POST,
  route: '/terms-and-conditions/',
});
