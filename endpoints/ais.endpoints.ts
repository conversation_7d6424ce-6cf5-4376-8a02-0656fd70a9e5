import { createEndpoint, METHOD } from '@api';

export const VerificationStatus = {
  INITIATED: 'INITIATED',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
} as const;

export type VerificationStatus =
  (typeof VerificationStatus)[keyof typeof VerificationStatus];

export interface BankAccount {
  accountId: string;
  currency: string;
  accountHolderType: string;
  accountType: string;
  name: string;
  createDate: Date;
  accountNumber: string;
  schemeName: string;
  nameClient: string;
  holderName: string;
  holderAddress: string;
  holderNameAddress: string;
  accountRelations: [];
  balances: [];
  auxData: [];
  logo?: string;
  connectionDate?: string;
  renewalDate?: string;
  expiryDate?: string;
}

export interface AisProcess {
  url?: string;
  processId?: string;
  process_id?: string;
  verificationStatus?: VerificationStatus;
  created_at?: string;
  updated_at?: string;
}
export interface AisAccountResponse {
  accounts: BankAccount[];
  process_id: string;
}

export const getAisAccounts = createEndpoint({
  method: METHOD.GET,
  route: '/ais-process/get_accounts/',
});

export const createAisProcess = createEndpoint({
  method: METHOD.POST,
  route: '/ais-process/',
});
