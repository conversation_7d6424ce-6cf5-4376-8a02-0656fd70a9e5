import { createEndpoint, METHOD } from '@api';

export interface Bank {
  id: string;
  name: string;
  bic: string;
  transaction_total_days: string;
  countries: string[];
  logo: string;
  max_access_valid_for_days: string;
}

export interface ConfirmBankChoiceResponse {
  account_selection: boolean;
  accounts: any[];
  agreement: string;
  created: string;
  id: string;
  institution_id: string;
  link: string;
  redirect: string;
  redirect_immediate: boolean;
  reference: string;
  ssn: string | null;
  status: string;
  user_language: string;
}

export interface BankAccount {
  id: string;
  iban: string;
  institution_id: string;
  name: string;
  status: string;
  last_accessed: string;
  created: string;
}

export interface BankInfoDict {
  [institutionId: string]: {
    logo: string;
    name: string;
  };
}

const BASE = '/savings-to-crypto-app';

export const BankEndpoints = {
  list: `${BASE}/gocardless-bank-list/`,
  confirmBankChoice: `${BASE}/gocardless-activation-view/:bankId/`,
  confirmActivation: `${BASE}/confirm-gocardless-activation-view/`,
  connection: `${BASE}/gocardless-account-example-data/`,
  bankAccountConnection: `${BASE}/gocardless-connection-view/`,
} as const;

export const getAllBanks = createEndpoint({
  method: METHOD.GET,
  route: BankEndpoints.list,
});

export const confirmBankChoice =
  createEndpoint({
    method: METHOD.GET,
    route: BankEndpoints.confirmBankChoice,
  });

export const confirmActivation =
  createEndpoint({
    method: METHOD.GET,
    route: BankEndpoints.confirmActivation,
  });

export const getBankAccountsInfo = createEndpoint({
  method: METHOD.GET,
  route: BankEndpoints.connection,
});

export const deleteBankAccountConnection = createEndpoint({
  method: METHOD.DELETE,
  route: BankEndpoints.bankAccountConnection,
});