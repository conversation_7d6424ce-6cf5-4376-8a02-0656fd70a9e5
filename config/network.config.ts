/**
 * @fileoverview Global Network Configuration
 * 
 * Centralized network configuration that ensures consistent network settings
 * across all components, services, and SDK integrations.
 * 
 * This configuration controls:
 * - Breez SDK network initialization
 * - Wallet address validation
 * - Exchange rate service endpoints
 * - Transaction processing
 * - All network-dependent operations
 */

import { LiquidNetwork } from '@breeztech/react-native-breez-sdk-liquid';

declare const __DEV__: boolean;

// ============================================================================
// NETWORK CONFIGURATION CONSTANTS
// ============================================================================

/**
 * Supported network types
 */
export const NETWORK_TYPES = {
  TESTNET: 'testnet' as const,
  MAINNET: 'mainnet' as const,
} as const;

export type NetworkType = typeof NETWORK_TYPES[keyof typeof NETWORK_TYPES];

/**
 * Global network configuration
 * 
 * CRITICAL: This is the single source of truth for network configuration.
 * All components MUST use this configuration to ensure consistency.
 */
export const GLOBAL_NETWORK_CONFIG = {
  // Current network - change this to switch between testnet/mainnet
  // CURRENT_NETWORK: __DEV__ ? NETWORK_TYPES.TESTNET : NETWORK_TYPES.MAINNET,
  CURRENT_NETWORK: NETWORK_TYPES.TESTNET,

  // Force testnet for development
  FORCE_TESTNET_IN_DEV: true,

  // Network validation
  VALIDATE_NETWORK_CONSISTENCY: true,
} as const;

/**
 * Get the current network type
 */
export function getCurrentNetwork(): NetworkType {
  if (__DEV__ && GLOBAL_NETWORK_CONFIG.FORCE_TESTNET_IN_DEV) {
    return NETWORK_TYPES.TESTNET;
  }
  return GLOBAL_NETWORK_CONFIG.CURRENT_NETWORK;
}

/**
 * Get the Breez SDK LiquidNetwork enum value for current network
 */
export function getBreezSdkNetwork(): LiquidNetwork {
  const currentNetwork = getCurrentNetwork();
  const liquidNetwork = currentNetwork === NETWORK_TYPES.TESTNET
    ? LiquidNetwork.TESTNET
    : LiquidNetwork.MAINNET;

  // Debug logging for development
  if (__DEV__) {
    // biome-ignore lint/suspicious/noConsole: Development debugging only
    console.log('🌐 Network Configuration Debug:', {
      currentNetwork,
      liquidNetwork,
      liquidNetworkType: typeof liquidNetwork,
      liquidNetworkValue: liquidNetwork,
      testnetEnum: LiquidNetwork.TESTNET,
      mainnetEnum: LiquidNetwork.MAINNET,
    });
  }

  return liquidNetwork;
}

/**
 * Check if currently on testnet
 */
export function isTestnet(): boolean {
  return getCurrentNetwork() === NETWORK_TYPES.TESTNET;
}

/**
 * Check if currently on mainnet
 */
export function isMainnet(): boolean {
  return getCurrentNetwork() === NETWORK_TYPES.MAINNET;
}

// ============================================================================
// NETWORK-SPECIFIC CONFIGURATIONS
// ============================================================================

/**
 * Network-specific API endpoints
 */
export const NETWORK_ENDPOINTS = {
  [NETWORK_TYPES.TESTNET]: {
    EXCHANGE_RATE_API: 'https://api.exchangerate-api.com/v4/latest/USD',
    BINANCE_API: 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
    // Add testnet-specific endpoints here
  },
  [NETWORK_TYPES.MAINNET]: {
    EXCHANGE_RATE_API: 'https://api.exchangerate-api.com/v4/latest/USD',
    BINANCE_API: 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
    // Add mainnet-specific endpoints here
  },
} as const;

/**
 * Get network-specific endpoints
 */
export function getNetworkEndpoints() {
  return NETWORK_ENDPOINTS[getCurrentNetwork()];
}

/**
 * Liquid network address prefixes for validation
 */
export const LIQUID_ADDRESS_PREFIXES = {
  [NETWORK_TYPES.TESTNET]: {
    BECH32: 'tlq1' as const,
    P2SH: 'XJL' as const,
    P2PKH: 'XT' as const,
    P2SH_ALT: 'XG' as const,
  },
  [NETWORK_TYPES.MAINNET]: {
    BECH32: 'lq1' as const,
    P2SH: 'VJL' as const,
    P2PKH: 'VT' as const,
    P2SH_ALT: 'VG' as const,
  },
} as const;

/**
 * Get address prefixes for current network
 */
export function getCurrentNetworkAddressPrefixes() {
  return LIQUID_ADDRESS_PREFIXES[getCurrentNetwork()];
}

/**
 * Validate if an address belongs to the current network
 */
export function validateAddressForCurrentNetwork(address: string): boolean {
  const prefixes = getCurrentNetworkAddressPrefixes();

  return (
    address.startsWith(prefixes.BECH32) ||
    address.startsWith(prefixes.P2SH) ||
    address.startsWith(prefixes.P2PKH) ||
    address.startsWith(prefixes.P2SH_ALT)
  );
}

// ============================================================================
// NETWORK VALIDATION AND RUNTIME CHECKS
// ============================================================================

/**
 * Runtime validation to ensure network consistency
 */
export function validateNetworkConsistency(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if network configuration is consistent
  const currentNetwork = getCurrentNetwork();
  const breezNetwork = getBreezSdkNetwork();

  // Validate Breez SDK network matches current network
  const expectedBreezNetwork = currentNetwork === NETWORK_TYPES.TESTNET
    ? LiquidNetwork.TESTNET
    : LiquidNetwork.MAINNET;

  if (breezNetwork !== expectedBreezNetwork) {
    errors.push(
      `Breez SDK network mismatch: expected ${expectedBreezNetwork}, got ${breezNetwork}`
    );
  }

  // Development warnings
  if (__DEV__ && currentNetwork === NETWORK_TYPES.MAINNET) {
    warnings.push(
      'Running mainnet in development mode - ensure this is intentional'
    );
  }

  if (!__DEV__ && currentNetwork === NETWORK_TYPES.TESTNET) {
    warnings.push(
      'Running testnet in production mode - this may not be intended'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Log network configuration for debugging
 */
export function logNetworkConfiguration() {
  const currentNetwork = getCurrentNetwork();
  const breezNetwork = getBreezSdkNetwork();
  const validation = validateNetworkConsistency();

  // biome-ignore lint/suspicious/noConsole: Network configuration logging
  console.log('🌐 Network Configuration:', {
    currentNetwork,
    breezNetwork,
    isDev: __DEV__,
    forceTestnetInDev: GLOBAL_NETWORK_CONFIG.FORCE_TESTNET_IN_DEV,
    validation,
  });

  if (validation.warnings.length > 0) {
    // biome-ignore lint/suspicious/noConsole: Network configuration warnings
    console.warn('⚠️ Network Configuration Warnings:', validation.warnings);
  }

  if (!validation.isValid) {
    // biome-ignore lint/suspicious/noConsole: Network configuration errors
    console.error('❌ Network Configuration Errors:', validation.errors);
  }
}

// ============================================================================
// NETWORK CONFIGURATION UTILITIES
// ============================================================================

/**
 * Get network-specific working directory for Breez SDK
 */
export function getNetworkWorkingDirectory(): string {
  const network = getCurrentNetwork();
  return `./breez_${network}`;
}

/**
 * Get network-specific cache keys
 */
export function getNetworkCacheKey(baseKey: string): string {
  const network = getCurrentNetwork();
  return `${baseKey}_${network}`;
}

/**
 * Network configuration for development/testing
 */
export const DEVELOPMENT_CONFIG = {
  ENABLE_NETWORK_LOGGING: __DEV__,
  VALIDATE_ON_STARTUP: __DEV__,
  STRICT_VALIDATION: false, // Set to true for strict network validation
} as const;
