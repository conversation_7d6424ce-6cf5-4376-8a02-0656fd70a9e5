import { StyleSheet } from 'react-native-unistyles';

export const theme = {
  colors: {
    primary: {
      100: '#D4F7EF',
      200: '#A8F0DF',
      300: '#7DE8CF',
      400: '#52E0BF',
      500: '#40C1A3',
      600: '#1FAD8C',
      700: '#1F7A65',
      800: '#0F5746',
      900: '#0A2922',
    },
    grey: {
      50: '#F9FAFA',
      100: '#DEE3E1',
      200: '#C2CCC8',
      300: '#A6B5AF',
      400: '#8A9E96',
      500: '#6F867D',
      600: '#586A63',
      700: '#414E49',
      800: '#2A322F',
      900: '#131615',
    },
    dark: '#3F3F3F',
    black: '#000000',
    white: '#FFFFFF',
    red: '#E96160',
    yellow: '#F38600',
    blue: '#0094FE',
    green: '#24D327',
  },
  gap: (v: number) => v * 4,
  padding: (v: number) => v * 4,
  margin: (v: number) => v * 4,
  spacing: (v: number) => v * 4,
};

const themes = {
  light: theme,
};

type AppThemes = typeof themes;

declare module 'react-native-unistyles' {
  export interface UnistylesThemes extends AppThemes {}
}

StyleSheet.configure({
  themes,
  settings: {
    initialTheme: 'light',
  },
});
