{"common": {"logout": "Logout", "logOut": "Logout", "signIn": "Sign In", "signUp": "Sign Up", "moveForward": "Continue", "or": "Or", "continue": "Continue", "back": "Back", "passwordForgotten": "Forgot password? ", "start": "Let's start", "yes": "Yes", "no": "No", "verified": "Verified", "introduction": "Introduction", "copy": "Copy", "copied": "Copied!", "cancel": "Cancel", "ok": "Ok", "confirm": "Confirm", "finish": "Finish", "tryAgain": "Try again"}, "units": {"percent": "%", "money": " PLN"}, "languages": {"en": "English", "pl": "Polish"}, "languageChange": {"title": "Are you sure you want to switch to {{language}}?", "description": "Your language will change to {{language}}. Do you want to proceed? You can always change it later in the settings!"}, "inUse": "In use", "exitDialog": {"title": "Are you sure you want to exit?", "description": "Your progress will be saved, you can return and continue the configuration process at any time!"}, "messages": {"authenticationError": "Bad login or password", "registrationError": "Error during registration process", "urlError": "Error: URL link not provided", "noAccountFound": "Can't find account with given email", "loggingOut": "Log out", "loading": "Loading...", "activationLinkSent": "We have sent the activation link to your e-mail!", "accountActivated": "Twoje konto zostało aktywowane, moż<PERSON>z się zalogować!"}, "validators": {"validationCheck": {"minLengthCapital": "At least one capital letter", "minLengthSmallLetter": "At least one lowercase letter", "requiredOneDigit": "At least one digit", "requiredAtLeastOneSpecialCharacter": "At least 1 special character", "minLength": "Minimum 12 characters", "maxLength": "Maximum 24 characters"}, "password": {"required": "Password is required", "min": "Password must be at least 12 characters long", "minWallet": "Password must be at least 8 characters long", "max": "Password cannot exceed 24 characters", "passwordStrength": "Password must contain at least one big letter", "passwordLetter": "Password must contain at least one small letter", "passwordSpecialSign": "Password must contain at least one special sign", "requiredOneDigit": "Password must contain at least one digit", "passwordsMustMatch": "Password must match"}, "email": {"required": "email is required", "emailValidator": "Please provide valid email address"}}, "screens": {"home": {"title": "Use your spending to invest wisely."}}, "signIn": {"formProps": {"title": "Sign In", "subtitle": "Use your credentials to sign in", "bottomText": {"text": "Don't have an account?", "linkText": "Sign Up now"}}, "inputs": {"email": {"label": "Enter email"}, "password": {"label": "Password"}}, "buttons": {"signIn": {"label": "Sign in", "signInByGoogleAccount": "Use google account"}}, "forgotPassword": "Forgot password?"}, "signUp": {"formProps": {"title": "Sign Up", "bottomText": {"text": "Already have an account?", "linkText": "Sign In"}}, "inputs": {"username": {"label": "Email"}, "password": {"label": "Password"}, "confirmPassword": {"label": "Confirm password"}, "termsAndConditions": {"label": "I accept terms and conditions"}}, "buttons": {"signUp": {"label": "Sign Up"}, "moveForward": {"label": "Move forward"}}, "validationMessages": {"emailValidationMessage": "Please, provide a valid e-mail address", "passwordValidationMessage": "Password should contain 8 characters, 1 number and 1 uppercase letter", "confirmPasswordValidationMessage": "Password are not the same"}}, "activationCode": {"title": "Enter activation code", "resendCodeLabel": "Didn't get the code? <PERSON>sen<PERSON>", "resendCodeBlocked": "Resend in {{seconds}} s"}, "passwordReset": {"title": "Reset password", "description": "Enter a new password or send a reset link to your email.", "descriptions": {"email": "Enter your email to receive a password reset link.", "linkSent": "We’ve sent a password reset link to your email.", "setPassword": "Enter and confirm your new password."}, "linkSentInfo": "We've sent a password reset link to your email.", "resendLinkLabel": "Didn't get the link? <PERSON>sen<PERSON>", "resendLinkBlocked": "Resend in {{seconds}} s"}, "termsOfUse": {"title": "Zgody"}, "requestInfo": {"success": "Success"}, "buttonsLabels": {"dismiss": "<PERSON><PERSON><PERSON>"}, "security": {"buttonLabel": "Add security phrase", "title": "Security phrase and private key", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."}, "dataSecurity": {"title": "How RETI Secures Your Data", "slides": {"intro": {"title": "How RETI secures\nyour data?", "description": "Your security and privacy are our top priorities. Learn how we protect your information every step of the way."}, "secureLogin": {"title": "<PERSON><PERSON>", "description": "Two-factor authentication (2FA), Face ID, fingerprint – you can choose your preferred method of securing access. <bold>RETI never stores your password in plain text.</bold>"}, "encryption": {"title": "End-to-end Encryption", "description": "All data transmitted in RETI is protected with end-to-end encryption (E2EE). This means that <bold>only you and the authorized recipient</bold> have access to the content – no one in between, not even us, can see what you're transmitting."}}, "buttons": {"continue": "Continue", "next": "Next"}}, "savingPlan": {"formProps": {"title": "Savings tailored to your lifestyle!"}, "howItWorks": {"title": "How does our Savings plan work?", "description": "This is your way to save in Bitcoin – tailored to your needs and lifestyle. You have full control over how you want to set aside funds."}, "whyItIsWorth": {"title": "Why is it worth it?", "description": "Thanks to automation and adaptation to your habits, saving becomes simple and effective. You eliminate emotions, build results over time, and have full control over your plan."}, "callToAction": {"text": "Small steps, big results – start now!"}, "savingGoalSelection": {"title": "Choose what you want to save for", "infoBox": "Selecting your main goal will help us tailor future tips and suggestions just for you!"}, "nameStep": {"title": "Name your savings plan", "info": "Give your plan a name — create something that suits you!", "inputLabel": "Plan name", "placeholder": "e.g., First apartment"}, "timeHorizonStep": {"title": "Set your time horizon", "info": "Decide how long you want to run your savings plan. Adjust the time horizon to match your goals.", "unit": {"one": "year", "few": "years", "many": "years"}}, "plans": {"title": "Set your savings plan", "adjusted": {"title": "Adjusted to Your Life", "description": "A plan perfectly tailored to your financial habits. We analyze your spending and automatically adjust your savings amount so it's comfortable and almost unnoticeable. Saving becomes simple and natural, without sacrifice.", "tags": ["Premium", "Recommended", "Safer"], "message": "Great! Saving will become easy, and your habits will support your financial goals."}, "simple": {"title": "Simple Saving", "description": "You decide how much and how often to save — weekly, monthly, or at your own pace. It's a convenient way to reach your goals or build a financial cushion, always on your own terms.", "tags": ["Free", "Not recommended", "Less safe"], "message": "Think carefully before choosing the amount! If you're unsure, consider selecting the first plan."}}, "amountStep": {"title": "Choose your amount and rhythm", "amountLabel": "Saving amount", "frequencyLabel": "How often do you want to save?", "unit": "PLN", "frequencies": {"W": "Week", "M": "Month", "Q": "Quarter"}}, "ignoreTransactionsStep": {"title": "Do you want to ignore larger transactions?", "inputLabel": "Amount above which transactions will be ignored", "unit": "PLN", "message": "Setting the value too low may result in many transactions being skipped, which can reduce the effectiveness of your savings. We recommend choosing a value that ensures regular and effective saving."}, "adjustedPlan": {"title": "Set a Saving Percentage or Amount", "percentagePlan": {"heading": "Percentage of Each Transaction Amount", "description": "By choosing this option, you define a percentage of each transaction's value that will be automatically transferred to your savings account. For example, with a 5% setting, from a transaction worth 100 PLN, 5 PLN will go into your savings.", "recommendation": "It is recommended to set a value between 2% and 10%. These amounts allow for regular saving without noticeably affecting daily expenses.", "alert": "Setting such a high percentage may significantly impact the availability of funds for daily spending. Are you sure you want to continue?"}, "fixedPlan": {"heading": "Fixed Amount per Transaction", "description": "This option allows you to specify a fixed amount that will be automatically saved to your savings account with each transaction. For example, with a 5 PLN setting, 5 PLN will be saved from every transaction, regardless of its value.", "recommendation": "It is recommended to set an amount between 1 PLN and 10 PLN. These values allow for regular saving without putting too much strain on your budget."}}, "summary": {"saved": {"title": "Your Saving Plan", "description": "Here is a summary of your active saving plan."}, "content": {"timeHorizon": "Time Horizon:", "savingType": "Saving Method:", "savingPercentage": "Saving Percentage:", "savingFixedValue": "Saving Amount:", "ignoreTransaction": "Ignore Larger Transactions:", "ignoreTransactionPrefix": "Yes, above "}, "buttons": {"createPlan": "Create Plan", "changePlan": "Change", "connect-bank": "Connect to bank account"}}}, "bank-connection": {"success": "Connected bank account successfully. You will be redirected shortly.", "noRef": "Invalid reference in bank connection. Please try again or contact customer support.", "error": "An error occurred while connecting the bank account. Please try again.", "buttonTitle": "Add new bank account", "title": "Connected bank accounts", "description": "By connecting your bank account, your savings adapt to your lifestyle.", "details": "Account details", "disconect": "Disconnect", "renew": "Renew connection", "connectionDate": "Connection date", "lastRenew": "Last renewal", "expiryDate": "Connection expires", "renewConnection": "Renew connection", "noAccounts": "No connected bank accounts. Add a new one using the button below", "chooseBank": "Choose your bank from the list below to continue", "addNewAccount": "Add new bank account", "expiryTooltip": "When less than 30 days remain until the connection expires", "confirmationTitle": "Bank connection", "confirmationDescription": "Your bank account has been successfully connected.", "discconectModal": {"title": "Confirm disconnection of bank account", "description": "Once disconnected, you will no longer be able to use this bank account in our app and all related data will be removed.", "cancel": "Cancel", "confirm": "Yes, disconnect"}, "userCancelled": "You cancelled the bank connection process.", "userCancelledTitle": "Bank connection cancelled", "userCancelledDescription": "You have cancelled the process. You can try again now or choose a different bank.", "tryAgain": "Try again", "chooseDifferentBank": "Choose a different bank"}, "planBox": {"title": "Your Plan", "typeBasic": "Basic", "typePremium": "PRO", "monthly": "mo"}, "ambassadorBox": {"subtitle": "Invite and earn", "reward": "Get up to 50 PLN"}, "viewKeyModal": {"title": "Enter password to display", "description": "To display your account number key, you need to confirm your identity."}, "dashboard": {"greeting": "Hello", "totalBalance": "Total Balance", "configurationBanner": {"text": "Complete your account setup\nand start saving to the fullest!", "button": "Take me there!", "highlightWord": "saving"}, "verificationBanner": {"text": "Verify your identity\nto unlock all features!", "button": "Verify now", "highlightWord": "identity"}, "walletCreationBanner": {"text": "Create your Bitcoin wallet\nand start saving in crypto!", "button": "Create wallet", "highlightWord": "Bitcoin"}, "sections": {"savings": "Your Savings", "transactions": "Recent Transactions"}, "savingsChart": {"legend": {"savingsValue": "Savings Value", "depositedAmount": "Deposited PLN"}}, "deposited": "deposited", "buttons": {"seeAll": "See All"}, "transactions": {"types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>"}}, "modal": {"savings": {"title": "Summary", "detailsTitle": "Detailed Data", "fullPeriod": "Full Period", "totalBalancePLN": "Total Balance in PLN", "totalBalanceBTC": "Total Balance in BTC", "deposited": "Deposited", "percentageBalance": "Percentage Balance", "amountBalance": "Amount Balance", "timeframes": {"5y": "5y", "1y": "1y", "6m": "6m", "1m": "1m"}}}, "loading": {"balance": "Loading wallet balance...", "transactions": "Loading recent transactions...", "bitcoinPrice": "Loading Bitcoin price...", "savingsChart": "Loading savings data..."}}, "kyc": {"title": "KYC Verification", "subtitle": "KYC (Know Your Customer) verification is a standard process for confirming user identity that ensures security and prevents abuse.", "description": "KYC (Know Your Customer) verification is a standard process for confirming user identity that ensures security and prevents abuse.\nWe need some information:", "requirements": {"title": "What you'll need:", "personalData": "Your personal data (first name, last name, PESEL, date of birth)", "idDocument": "Photo of your identity document", "facePhoto": "Photo of your face", "document": "Identity document (ID card or passport)", "face": "Photo of your face for verification", "lighting": "Good lighting when taking photos", "quality": "Clear, sharp photos without obstructions"}, "processNote": "The whole process is quick and secure.", "securityNote": "Your data is encrypted and securely stored.", "progress": {"introduction": "Introduction"}, "buttons": {"start": "Let's start", "starting": "Starting...", "back": "Back"}, "status": {"title": "Verification status", "inProgress": "Verification in progress", "inProgressUser": "Verification not finished", "completed": "Verification completed", "failed": "Verification failed", "submitted": "Verification submitted, awaiting review", "checking": "Checking verification status...", "notStarted": "Verification not started", "loading": "Loading verification status...", "cancelled": "Verification cancelled"}}, "navigation": {"myData": "My Data", "savingPlan": "Saving Plan", "connectedBankAccounts": "Connected Bank Accounts", "cardPaymentConnected": "Connected Payment Cards", "web3wallet": "Web3 Wallet", "walletImport": "Import Wallet", "biometricLogin": "Biometric Login", "pinManagement": "PIN Management", "changePassword": "Change Password", "retiHowWeSecureYourData": "How <PERSON><PERSON> Secures Your Data", "pushNotifications": "Push Notifications", "language": "Language", "rulebook": "Terms and Conditions", "reportbug": "Report a Bug", "about": "About the App", "logout": "Log Out", "logoutConfirmation": {"title": "Confirm <PERSON>ut", "message": "Are you sure you want to log out? You will need to sign in again to access your account."}}, "userDetails": {"title": "Your Data", "subtitle": "Here you will find your personal data.", "firstName": "First Name", "lastName": "Last Name", "pesel": "PESEL", "email": "Email Address", "phone": "Phone", "joinDate": "Join Date", "birthDate": "Date of Birth", "noProfile": "No profile data found, please verify your identity"}}