{"title": "Wallet", "balance": "Balance", "address": "Address", "generatingAddress": "Generating address...", "seedPhrase": "Seed phrase", "show": "Show", "hide": "<PERSON>de", "noAddressAvailable": "No address available", "screenshotWarning": {"title": "Warning!", "message": "For security reasons, avoid taking screenshots of your seed phrase.", "button": "OK"}, "loading": {"checkingWalletStatus": "Checking wallet status...", "settingUpWallet": "🔧 Setting up your wallet", "preparingSecureWallet": "Preparing your secure wallet...", "navigationNote": "This will only take a moment. You can navigate away and come back later."}, "creationOverlay": {"takingLonger": "Creating your wallet is taking a bit longer than usual. You can continue using the app – we'll finish setup in the background."}, "walletSetupFailed": {"title": "⚠️ Wallet Setup Failed", "description": "There was an issue setting up your wallet.", "tryAgain": "Try Again"}, "noWalletFound": {"title": "No Wallet Found", "description": "Create a wallet to get started with Bitcoin Lightning payments.", "createWallet": "Create Wallet", "debugStorage": "Debug Storage"}, "connection": {"loadingBalance": "Loading balance...", "authenticationRequired": "Authentication required", "loadingAddress": "Loading address...", "generatingAddress": "Generating wallet address...", "addressGenerationFailed": "Address generation failed, retrying...", "generatingInBackground": "Generating address in background...", "addressGenerationFailedWarning": "Address generation failed. Will retry automatically.", "retryNow": "Retry Now", "generating": "Generating...", "walletAddressReady": "Wallet address ready"}, "transactions": {"title": "Recent Transactions", "viewAll": "View all", "noTransactions": "No transactions yet", "notFound": "Transaction not found", "received": "Received", "sent": "<PERSON><PERSON>", "pending": "Pending", "failed": "Failed", "completed": "Completed", "loadingMore": "Loading...", "loadMore": "Load more", "details": {"status": "Status", "type": "Type", "date": "Date", "timeAgo": "Time ago", "transactionDetails": "Transaction Details", "transactionId": "Transaction ID", "amountSatoshis": "Amount (Satoshis)", "networkFee": "Network Fee"}}, "deleteWallet": {"title": "Delete Wallet", "message": "Are you sure you want to delete this wallet? This action cannot be undone and will remove all wallet data.", "confirm": "Delete", "cancel": "Cancel"}, "developmentTools": {"title": "Development Tools", "walletManagement": "Wallet Management", "deleteWallet": "Delete Wallet", "debugStorage": "Debug Storage"}, "errors": {"generic": "Error", "deletionFailed": "Failed to delete wallet. Please try again."}, "walletAddress": "Wallet address", "viewSeedPhrase": {"title": "Seed", "description": "Store your recovery phrase in a safe place, preferably offline.", "alert": "Don't take screenshots or share it with anyone - not even us!", "authenticationRequired": "Authentication required to view seed phrase", "authenticating": "Authenticating...", "authenticationFailed": "Authentication failed. Please try again.", "authenticationCancelled": "Authentication was cancelled"}, "selectWalletType": {"title": "Storing the seed phrase", "description": "Choose whether you want to store your private key in the RETI cloud or save it in a secure place.", "cloud": {"title": "Storing the seed phrase in the RETI cloud", "description": "Storing your seed phrase in the RETI cloud is the most secure way to store your private key. If you lose your private key, you can recover it using your seed phrase."}, "private": {"title": "Private storage of the private key", "description": "This method is less secure, but allows for full control over the private key. You can write it down on paper, in a safe, or in another secure location."}}, "saveSeedPhrase": {"title": "Save Seed Phrase", "description": "Below you will find 12 unique words - the so-called seed - which are used to recover access to your Web3 portfolio.", "confirmTitle": "Confirm Seed Phrase", "confirmDescription": "Enter the words from your seed in the correct order.", "enterPhrase": "Enter your 12-word seed phrase", "placeholder": "Enter word", "phraseCorrect": "Seed phrase is correct!", "phraseIncorrect": "Seed phrase does not match. Please check your input.", "verify": "Verify", "alert": "Write it down now on paper or in another secure offline place.", "creatingWallet": "Creating wallet...", "pleaseWait": "Please wait while we set up your wallet.", "walletCreationFailed": "Wallet Creation Failed", "tryAgain": "Try Again", "error": "Error", "seedPhraseNotGenerated": "Seed phrase not generated. Please try again.", "incorrectPhrase": {"title": "Incorrect Phrase", "message": "The seed phrase you entered does not match. Please try again."}, "screenshotDetected": {"title": "Screenshot Detected", "message": "For security reasons, avoid taking screenshots of your seed phrase.", "ok": "OK"}}, "creationSuccess": {"title": "Congratulations!", "paragraphs": "<bold>Your new wallet has been successfully created and is now ready to use.</bold>\n\nYou can now enjoy automatic savings deposited directly into your wallet!\n\nRemember to always keep your sensitive data (seed or password) safe. They are the key to your funds, so take care of them as your most valuable possession.\n\nGood luck and happy saving with RETI using the benefits of Web3!", "button": "Go to wallet"}, "cloud": {"title": "Storing the seed phrase in the RETI cloud"}, "private": {"title": "Private storage of the private key"}, "import": {"title": "Import Wallet", "description": "Choose how you'd like to import your existing wallet.", "checkingCloudRecovery": "Checking for cloud recovery data...", "errorCheckingCloudRecovery": "Failed to check cloud recovery availability. Please try again.", "cloudRecovery": {"title": "Cloud Recovery", "description": "Recover your wallet using your <bold>cloud backup password</bold>. This will restore your wallet from the encrypted backup stored securely in the cloud.", "unavailable": "No cloud backup found for your account.", "passwordLabel": "Cloud Backup Password", "passwordPlaceholder": "Enter your cloud backup password", "importing": "Importing wallet...", "importWallet": "Import Wallet", "loadingRecoveryData": "Loading recovery data...", "securityNote": "Your password is used to decrypt your wallet backup. We cannot recover your wallet without the correct password.", "errors": {"noRecoveryData": "No Recovery Data Found", "noRecoveryDataDescription": "We couldn't find any cloud backup for your account. Please try manual seed phrase import instead.", "invalidPassword": "Invalid password. Please check your password and try again.", "invalidRecoveryBlob": "Invalid recovery data format. Please contact support.", "cloudRecoveryNotAvailable": "Cloud recovery is not yet available. Please use manual seed phrase import.", "generic": "Failed to import wallet. Please try again."}}, "manualSeedPhrase": {"title": "Manual Seed Phrase", "description": "Import your wallet by entering your <bold>12-word seed phrase</bold>. Make sure you have your seed phrase written down exactly as it was given to you.", "seedPhraseLabel": "Enter your 12-word seed phrase", "importing": "Importing wallet...", "importWallet": "Import Wallet", "validationErrors": "Please fix the following errors:", "securityNote": "Your seed phrase will be stored securely on this device and used to generate a new wallet address.", "errors": {"invalidSeedPhrase": "Invalid seed phrase. Please check your words and try again.", "generic": "Failed to import wallet. Please try again."}}, "success": {"title": "Congratulations!", "paragraphs": "<bold>Your wallet has been successfully imported and is now ready to use.</bold>\n\nYou can now enjoy automatic savings deposited directly into your wallet!\n\nRemember to always keep your sensitive data (seed or password) safe. They are the key to your funds, so take care of them as your most valuable possession.\n\nGood luck and happy saving with RETI using the benefits of Web3!", "button": "Go to wallet"}}, "bitcoinDistribution": {"title": "Bitcoin Distribution Testing (DEV)", "warning": {"title": "⚠️ Development Testing Tool", "description": "This interface tests the /distribute-btc-among-users/ API endpoint. Use only for development and testing purposes."}, "summary": {"title": "Request Summary", "destinations": "Destinations: {{count}} valid entries", "totalAmount": "Total Amount: {{btc}} BTC ({{sats}} sats)"}, "form": {"title": "Bitcoin Destinations", "addButton": "Add", "clearButton": "Clear All", "destinationTitle": "Destination #{{number}}", "removeButton": "Remove", "addressLabel": "Bitcoin Address", "addressPlaceholder": "Enter Bitcoin address (e.g., **********************************)", "amountLabel": "Amount (Satoshis)", "amountPlaceholder": "Enter amount in satoshis (min: 1)", "submitButton": "Submit Distribution Request", "submittingButton": "Submitting...", "validationError": "Please fix validation errors before submitting"}, "validation": {"addressRequired": "Address is required", "addressInvalid": "Invalid Bitcoin address format", "amountRequired": "Amount is required", "amountInvalid": "Amount must be a positive integer", "amountMinimum": "Minimum amount is 1 satoshis"}, "alerts": {"validationError": "Validation Error", "validationErrorMessage": "Please fix all validation errors before submitting.", "noDestinations": "Error", "noDestinationsMessage": "At least one valid destination is required.", "confirmTitle": "Confirm Distribution", "confirmMessage": "Send {{btc}} BTC ({{sats}} sats) to {{count}} destination(s)?", "cancel": "Cancel", "send": "Send", "successTitle": "Success", "successMessage": "Distribution initiated!\nTask ID: {{taskId}}\nTransactions: {{transactions}}", "errorTitle": "Error", "errorMessage": "Failed to submit distribution: {{error}}"}, "response": {"title": "✅ Last Response", "taskId": "Task ID: {{taskId}}", "status": "Status: {{status}}", "totalTransactions": "Total Transactions: {{count}}"}, "documentation": {"title": "📖 API Documentation", "endpoint": "Endpoint: POST /distribute-btc-among-users/", "requestFormat": "Request Format:", "responseFormat": "Response Format:"}, "authRequired": "Authentication required"}}