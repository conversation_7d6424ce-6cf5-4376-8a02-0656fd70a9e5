{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "extends": ["ultracite"], "linter": {"rules": {"style": {"noNestedTernary": "off", "noExportedImports": "off", "noNonNullAssertion": "off", "useBlockStatements": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off", "noForEach": "off"}, "nursery": {"noAwaitInLoop": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "performance": {"noNamespaceImport": "off"}}}}