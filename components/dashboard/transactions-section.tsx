import {
  getSavingPlanSettings,
  type SavingPlanProps,
} from '@endpoints/saving-plan.endpoints';
import useAuthStore from '@store/auth.store';
import useBreezSdkStore from '@store/breez-sdk.store';
import { useQuery } from '@tanstack/react-query';
import { Box, Button, Text } from '@ui';
import { dayjs } from '@utils';
import { getQueryData } from '@utils/api/api-functions';
import { transactionsLogger } from '@utils/logger';
import { useSimpleWallet } from '@utils/wallet/use-simple-wallet';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { useTranslations } from 'hooks/use-translations';
import type React from 'react';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { AuthState } from 'types/auth.types';
import type { Payment } from 'types/breez-sdk.types';

interface TransactionItemProps {
  payment: Payment;
}

export const TransactionItem: React.FC<TransactionItemProps> = memo(
  ({ payment }) => {
    const t = useTranslations();
    const { convertSatoshis } = useExchangeRate();
    const isReceived = payment.paymentType === 'receive';
    const amountBtc = payment.amountSat / 100_000_000;

    const amountPLN = convertSatoshis(payment.amountSat, 'PLN');

    return (
      <Box align="center" flexDirection="row" justify="between" py={2}>
        <Box align="center" flexDirection="row" gap={2}>
          <Image
            contentFit="contain"
            source={require('@assets/transaction-icon.png')}
            style={styles.transactionIcon}
          />
          <Box gap={0.5}>
            <Text color="deepDark" weight="medium">
              {t(
                `dashboard.transactions.types.${isReceived ? 'deposit' : 'withdrawal'}`
              )}
            </Text>
            <Text color="grey.400" size="sm">
              {dayjs(payment.timestamp * 1000).format('D MMM, HH:mm')}
            </Text>
          </Box>
        </Box>
        <Box align="end" gap={0.5}>
          <Text color="grey.900" weight="medium">
            {isReceived ? '+' : '-'}
            {amountPLN.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}{' '}
            PLN
          </Text>
          <Text color="grey.400" size="sm">
            {amountBtc.toLocaleString('en-US', {
              minimumFractionDigits: 8,
              maximumFractionDigits: 8,
            })}{' '}
            BTC
          </Text>
        </Box>
      </Box>
    );
  }
);

export const TransactionsSection: React.FC = memo(() => {
  const t = useTranslations();

  const { authenticated } = useAuthStore();
  const isAuthenticated = useMemo(
    () => authenticated === AuthState.Authenticated,
    [authenticated]
  );

  const { actions: walletActions } = useSimpleWallet();
  const { recentPayments, isConnected, loadPayments } = useBreezSdkStore();
  // const transactions = useMemo(() => {
  //   const hasWallet = walletState.hasWallet && walletState.wallet;
  //   const hasBreezConnection = isBreezConnected && walletInfo;

  //   if (hasWallet && hasBreezConnection && recentPayments?.length > 0) {
  //     return recentPayments.slice(0, 3).map((payment, index) => ({
  //       id: index + 1,
  //       type: payment.paymentType === 'receive' ? 'deposit' : 'withdrawal',
  //       date: new Date(
  //         (payment.timestamp || Date.now() / 1000) * 1000
  //       ).toISOString(),
  //       amount: convertSatoshis(payment.amountSat, 'PLN'),
  //       btc: payment.amountSat / 100_000_000,
  //     }));
  //   }
  //   return walletState.hasWallet ? [] : [];
  // }, [
  //   walletState,
  //   isBreezConnected,
  //   walletInfo,
  //   recentPayments,
  //   convertSatoshis,
  // ]);
  const { data: plan } = useQuery(
    getQueryData<SavingPlanProps>(getSavingPlanSettings, {
      queryOptions: { retry: false, staleTime: 60_000 },
    })
  );

  const handleRefresh = useCallback(async () => {
    try {
      await walletActions.refresh();
      if (isConnected) {
        await loadPayments({ limit: 50, offset: 0 });
      }
    } catch (error) {
      transactionsLogger.error(
        'Failed to refresh transactions (dashboard section)',
        error
      );
    }
  }, [isConnected, loadPayments, walletActions]);

  useEffect(() => {
    if (isConnected && recentPayments.length === 0) {
      handleRefresh();
    }
  }, [isConnected, recentPayments.length, handleRefresh]);

  const limitedPayments = useMemo(
    () => recentPayments.slice(0, 3),
    [recentPayments]
  );

  const seeAllHref = plan
    ? '(saving-plan)/plan-summary'
    : '(authenticated)/transactions';

  const renderEmptyState = () => (
    <Box bgColor="white" p={4} radius={12}>
      <Box align="center" p={2}>
        <Text color="grey.600" size="sm" textAlign="center">
          {t('wallet:transactions.noTransactions')}
        </Text>
      </Box>
    </Box>
  );

  return (
    <View style={styles.transactionsCard}>
      <Box mb={4}>
        {!(isAuthenticated && isConnected) || limitedPayments.length === 0 ? (
          renderEmptyState()
        ) : (
          <Box gap={3}>
            {limitedPayments.map((payment, index) => (
              <View key={payment.txId || `payment-${index}`}>
                <TransactionItem payment={payment} />
                {index < limitedPayments.length - 1 && (
                  <View style={styles.transactionDivider} />
                )}
              </View>
            ))}
          </Box>
        )}
      </Box>
      <Button
        disabled={
          !(isAuthenticated && isConnected) || limitedPayments.length === 0
        }
        href={seeAllHref}
        py={2.5}
        size="xs"
      >
        {t('dashboard.buttons.seeAll')}
      </Button>
    </View>
  );
});

const styles = StyleSheet.create((theme) => ({
  transactionsCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 16,
    padding: 16,
    shadowColor: theme.colors.primary[600],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  transactionIcon: {
    width: 40,
    height: 40,
  },
  transactionDivider: {
    height: 1,
    backgroundColor: theme.colors.grey[50],
    marginVertical: 8,
  },
}));
