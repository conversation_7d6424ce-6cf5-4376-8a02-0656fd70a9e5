import { Box, Button } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useTranslations } from 'hooks/use-translations';
import type React from 'react';
import { useRef } from 'react';
import { Dimensions, Text, View } from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import Carousel, {
  type ICarouselInstance,
} from 'react-native-reanimated-carousel';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

export interface QuickAction {
  id: string;
  type:
    | 'configuration'
    | 'verification'
    | 'bank-connection'
    | 'wallet-creation';
  titleKey: string;
  buttonKey: string;
  highlightWordKey?: string;
  image: string;
  route: string;
}

interface QuickActionsSliderProps {
  quickActions: QuickAction[];
}

const CarouselIndicator: React.FC<{
  index: number;
  length: number;
  animValue: Animated.SharedValue<number>;
}> = (props) => {
  const { theme } = useUnistyles();
  const { animValue, index, length } = props;

  const animStyle = useAnimatedStyle(() => {
    const value = animValue.value;

    const inputRange = [index - 1, index, index + 1];
    const outputSizeRange = [4, 6, 4];
    const outputColorRange = [
      theme.colors.grey[100],
      theme.colors.primary[600],
      theme.colors.grey[100],
    ];

    if (index === 0) {
      inputRange.push(length - 1, length);
      outputSizeRange.push(4, 6);
      outputColorRange.push(theme.colors.grey[100], theme.colors.primary[600]);
    }

    if (index === length - 1) {
      inputRange.unshift(-1);
      outputSizeRange.unshift(6);
      outputColorRange.unshift(theme.colors.primary[600]);
    }

    const size = interpolate(
      value,
      inputRange,
      outputSizeRange,
      Extrapolate.CLAMP
    );

    const backgroundColor = interpolateColor(
      value,
      inputRange,
      outputColorRange
    );

    return {
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor,
    };
  }, [animValue, index, length]);

  return <Animated.View style={[styles.indicator, animStyle]} />;
};

const HighlightedText = ({
  text,
  highlightWord,
}: {
  text: string;
  highlightWord?: string;
}) => {
  if (!highlightWord) {
    return <Text style={styles.configText}>{text}</Text>;
  }

  const lowerText = text.toLowerCase();
  const lowerHighlight = highlightWord.toLowerCase();
  const startIndex = lowerText.indexOf(lowerHighlight);

  if (startIndex === -1) {
    return <Text style={styles.configText}>{text}</Text>;
  }

  const beforeText = text.substring(0, startIndex);
  const highlightText = text.substring(
    startIndex,
    startIndex + highlightWord.length
  );
  const afterText = text.substring(startIndex + highlightWord.length);

  return (
    <Text style={styles.configText}>
      {beforeText && <Text style={styles.configText}>{beforeText}</Text>}
      <Text style={[styles.configText, styles.highlightedText]}>
        {highlightText}
      </Text>
      {afterText && <Text style={styles.configText}>{afterText}</Text>}
    </Text>
  );
};

export const QuickActionsSlider: React.FC<QuickActionsSliderProps> = ({
  quickActions,
}) => {
  const t = useTranslations();
  const carouselRef = useRef<ICarouselInstance>(null);
  const progressValue = useSharedValue<number>(0);

  const renderQuickActionSlide = ({ item }: { item: QuickAction }) => {
    const text = t(item.titleKey);
    const highlightWord = item.highlightWordKey
      ? t(item.highlightWordKey)
      : undefined;

    return (
      <Box px={4}>
        <View style={styles.configBanner}>
          <View style={styles.configContent}>
            <HighlightedText highlightWord={highlightWord} text={text} />
            <Button
              href={item.route}
              py={2.5}
              self="flex-start"
              size="xs"
              w="auto"
            >
              {t(item.buttonKey)}
            </Button>
          </View>
          <View style={styles.configImageContainer}>
            <Image
              contentFit="contain"
              source={item.image}
              style={styles.configIllustration}
            />
          </View>
        </View>
      </Box>
    );
  };

  if (quickActions.length <= 1) {
    return quickActions.length > 0 ? (
      <View style={styles.sliderContainer}>
        {renderQuickActionSlide({ item: quickActions[0] })}
      </View>
    ) : null;
  }

  return (
    <View style={styles.sliderContainer}>
      <Carousel
        autoPlay={true}
        autoPlayInterval={4000}
        data={quickActions}
        height={132}
        loop
        onProgressChange={(_, absoluteProgress) => {
          progressValue.value = absoluteProgress;
        }}
        ref={carouselRef}
        renderItem={renderQuickActionSlide}
        scrollAnimationDuration={450}
        width={screenWidth}
      />
      <View style={styles.sliderIndicators}>
        {quickActions.map((action, index) => (
          <CarouselIndicator
            animValue={progressValue}
            index={index}
            key={`carousel-indicator-${action.id}`}
            length={quickActions.length}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create((theme) => ({
  configBanner: {
    backgroundColor: theme.colors.grey[900],
    borderRadius: 16,
    padding: 16,
    shadowColor: theme.colors.primary[600],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
    position: 'relative',
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  configContent: {
    flex: 1,
    zIndex: 1,
  },
  configText: {
    fontSize: 16,
    color: theme.colors.white,
    marginBottom: 16,
    lineHeight: 19.2,
  },
  highlightedText: {
    color: theme.colors.primary[300],
  },
  configImageContainer: {
    position: 'absolute',
    bottom: -16,
    right: 0,
    height: '100%',
    width: 120,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  configIllustration: {
    width: 150,
    height: 150,
  },
  sliderContainer: {
    marginBottom: 24,
  },
  sliderIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    height: 6,
    marginTop: 8,
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.grey[100],
  },
}));
