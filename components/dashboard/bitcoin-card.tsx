import SvgArrowUpRight from '@assets/arrow-up-right.svg';
import { CachedImage as Image } from 'components/ui/cached-image';
import type React from 'react';
import { memo } from 'react';
import { Text, View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

interface BitcoinCardProps {
  bitcoinValue: number;
  bitcoinChangePercent: number;
  bitcoinChangeTimeframe: string;
}

export const BitcoinCard: React.FC<BitcoinCardProps> = memo(
  ({ bitcoinValue, bitcoinChangePercent, bitcoinChangeTimeframe }) => (
    <View style={styles.bitcoinCard}>
      <View style={styles.bitcoinLeft}>
        <Image
          contentFit="contain"
          source={require('@assets/bitcoin.png')}
          style={styles.bitcoinIcon}
        />
        <View>
          <Text style={styles.bitcoinTitle}>Bitcoin</Text>
          <Text style={styles.bitcoinSymbol}>BTC</Text>
        </View>
      </View>
      <View style={styles.bitcoinRight}>
        <Text style={styles.bitcoinValue}>
          {bitcoinValue
            .toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })
            .replace(',', ' ')}{' '}
          PLN
        </Text>
        <View style={styles.bitcoinChange}>
          <Text style={styles.bitcoinChangeText}>
            + {bitcoinChangePercent}% ({bitcoinChangeTimeframe})
          </Text>
          <View style={styles.arrowIconContainer}>
            <SvgArrowUpRight />
          </View>
        </View>
      </View>
    </View>
  )
);

const styles = StyleSheet.create((theme) => ({
  bitcoinCard: {
    backgroundColor: theme.colors.white,
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: theme.colors.primary[600],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  bitcoinLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bitcoinIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  bitcoinTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.grey[900],
    marginBottom: 4,
    lineHeight: 19.2,
  },
  bitcoinSymbol: {
    fontSize: 12,
    color: theme.colors.grey[500],
    lineHeight: 14.4,
  },
  bitcoinRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  bitcoinValue: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.grey[900],
    lineHeight: 19.2,
  },
  bitcoinChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  bitcoinChangeText: {
    fontSize: 12,
    color: theme.colors.green,
    lineHeight: 14.4,
  },
  arrowIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
