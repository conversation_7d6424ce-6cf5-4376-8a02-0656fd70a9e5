/** biome-ignore-all lint/suspicious/noArrayIndexKey: . */
import { Box, Skeleton, Text } from '@ui';
import { useTranslations } from 'hooks/use-translations';
import type React from 'react';
import { memo } from 'react';

export const DashboardBalanceSkeleton: React.FC = memo(() => {
  const t = useTranslations();

  return (
    <Box
      accessibilityLabel={t('dashboard.loading.balance')}
      accessibilityRole="progressbar"
      align="center"
      gap={2}
      justify="center"
    >
      <Text color="grey.500" mb={2}>
        {t('dashboard.totalBalance')}
      </Text>

      <Skeleton
        borderRadius={8}
        height={32}
        style={{ marginBottom: 8 }}
        width={160}
      />

      <Skeleton borderRadius={6} height={20} width={120} />
    </Box>
  );
});

DashboardBalanceSkeleton.displayName = 'DashboardBalanceSkeleton';

export const DashboardTransactionItemSkeleton: React.FC = memo(() => (
  <Box
    accessibilityLabel="Loading transaction"
    accessibilityRole="progressbar"
    align="center"
    flexDirection="row"
    justify="between"
    py={2}
  >
    <Box align="center" flexDirection="row" gap={2}>
      <Skeleton borderRadius={20} height={40} width={40} />

      <Box gap={1}>
        <Skeleton borderRadius={4} height={16} width={80} />
        <Skeleton borderRadius={4} height={14} width={100} />
      </Box>
    </Box>

    <Box align="end" gap={1}>
      <Skeleton borderRadius={4} height={16} width={90} />
      <Skeleton borderRadius={4} height={14} width={110} />
    </Box>
  </Box>
));

DashboardTransactionItemSkeleton.displayName =
  'DashboardTransactionItemSkeleton';

export const DashboardTransactionsSkeleton: React.FC = memo(() => {
  const t = useTranslations();

  return (
    <Box
      accessibilityLabel={t('dashboard.loading.transactions')}
      accessibilityRole="progressbar"
      bgColor="white"
      p={6}
      radius={12}
    >
      <Box align="center" flexDirection="row" justify="between" mb={4}>
        <Text color="grey.500" size="lg" weight="medium">
          {t('dashboard.sections.transactions')}
        </Text>
        <Skeleton borderRadius={4} height={16} width={60} />
      </Box>

      <Box gap={3}>
        {Array.from({ length: 3 }, (_, index) => (
          <Box key={`transaction-skeleton-${index}`}>
            <DashboardTransactionItemSkeleton />
            {index < 2 && <Box bgColor="grey.100" h={1} mt={2} w="100%" />}
          </Box>
        ))}
      </Box>

      <Box mt={4}>
        <Skeleton borderRadius={8} height={40} width="100%" />
      </Box>
    </Box>
  );
});

DashboardTransactionsSkeleton.displayName = 'DashboardTransactionsSkeleton';

export const DashboardBitcoinCardSkeleton: React.FC = memo(() => (
  <Box
    accessibilityLabel="Loading Bitcoin price data"
    accessibilityRole="progressbar"
    bgColor="white"
    p={4}
    radius={12}
  >
    <Box align="center" flexDirection="row" justify="between">
      <Box align="center" flexDirection="row" gap={3}>
        <Skeleton borderRadius={20} height={40} width={40} />

        <Box gap={1}>
          <Skeleton borderRadius={4} height={16} width={60} />
          <Skeleton borderRadius={4} height={14} width={30} />
        </Box>
      </Box>

      <Box align="end" gap={1}>
        <Skeleton borderRadius={4} height={18} width={100} />
        <Skeleton borderRadius={4} height={14} width={80} />
      </Box>
    </Box>
  </Box>
));

DashboardBitcoinCardSkeleton.displayName = 'DashboardBitcoinCardSkeleton';

export const DashboardSavingsChartSkeleton: React.FC = memo(() => (
  <Box
    accessibilityLabel="Loading savings chart"
    accessibilityRole="progressbar"
    bgColor="white"
    p={6}
    radius={12}
  >
    <Skeleton
      borderRadius={12}
      height={200}
      style={{ marginBottom: 16 }}
      width="100%"
    />

    <Box align="center" flexDirection="row" gap={6} justify="center">
      <Box align="center" flexDirection="row" gap={2}>
        <Skeleton borderRadius={6} height={12} width={12} />
        <Skeleton borderRadius={4} height={14} width={80} />
      </Box>
      <Box align="center" flexDirection="row" gap={2}>
        <Skeleton borderRadius={6} height={12} width={12} />
        <Skeleton borderRadius={4} height={14} width={90} />
      </Box>
    </Box>
  </Box>
));

DashboardSavingsChartSkeleton.displayName = 'DashboardSavingsChartSkeleton';
