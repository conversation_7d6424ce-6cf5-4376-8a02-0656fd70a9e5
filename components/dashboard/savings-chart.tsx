import { Box } from '@ui';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslations } from 'hooks/use-translations';
import type React from 'react';
import { Dimensions, Text, View } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { StyleSheet } from 'react-native-unistyles';

const screenWidth = Dimensions.get('window').width;

interface ChartDataPoint {
  value: number;
  hideDataPoint?: boolean;
}

interface SavingsChartProps {
  savingsValue: number;
  depositedAmount: number;
  savingsChartData: ChartDataPoint[];
  depositedChartData: ChartDataPoint[];
  children?: React.ReactNode;
}

const FadingVerticalLines = ({ numberOfLines }: { numberOfLines: number }) => {
  const lineStyle = {
    width: 1,
    height: 160,
  };

  return (
    <View style={styles.verticalLinesContainer}>
      {Array.from({ length: numberOfLines }).map((_, index) => (
        <LinearGradient
          colors={['rgba(222, 227, 225, 0)', 'rgba(222, 227, 225, 0.5)']}
          key={`vertical-line-${index}`}
          style={lineStyle}
        />
      ))}
    </View>
  );
};

const DashedBottomLine = () => {
  return (
    <View style={styles.dashedLineContainer}>
      {Array.from({ length: 40 }).map((_, index) => (
        <View key={`dash-${index}`} style={styles.dash} />
      ))}
    </View>
  );
};

export const SavingsChart: React.FC<SavingsChartProps> = ({
  savingsValue,
  depositedAmount,
  savingsChartData,
  depositedChartData,
  children,
}) => {
  const t = useTranslations();
  const numberOfPoints = savingsChartData.length;
  const numberOfSections = 5;
  const chartCardMargin = 16;
  const chartCardPadding = 16;
  const containerWidth = screenWidth - 2 * (chartCardMargin + chartCardPadding);
  const calculatedSpacing =
    numberOfPoints > 1 ? containerWidth / (numberOfPoints - 1) : 0;

  const allValues = [
    ...savingsChartData.map((d) => d.value),
    ...depositedChartData.map((d) => d.value),
  ];
  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues);
  const stepValue = (maxValue - minValue) / numberOfSections;

  return (
    <Box bgColor="white" mb={2} p={4} style={styles.chartCard}>
      <View style={styles.chartHeader}>
        <Text style={styles.chartAmount}>
          {savingsValue
            .toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })
            .replace(',', ' ')}{' '}
          PLN
        </Text>
        <Text style={styles.chartSubtext}>
          {t('dashboard.deposited')}{' '}
          {depositedAmount
            .toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })
            .replace(',', ' ')}{' '}
          PLN
        </Text>
      </View>

      <View style={styles.chartContainer}>
        <LineChart
          animateOnDataChange
          animationDuration={1000}
          areaChart
          color1="#F38600"
          color2="#40C1A3"
          curved
          data={savingsChartData}
          data2={depositedChartData}
          dataPointsColor1="#F38600"
          dataPointsColor2="#40C1A3"
          dataPointsRadius={4}
          disableScroll
          endFillColor1="rgba(243, 134, 0, 0.02)"
          endFillColor2="rgba(64, 193, 163, 0.02)"
          endOpacity={0.02}
          height={160}
          hideRules
          hideYAxisText
          initialSpacing={0}
          maxValue={maxValue}
          mostNegativeValue={minValue}
          noOfSections={numberOfSections}
          overflowBottom={-7}
          showVerticalLines={false}
          spacing={calculatedSpacing}
          startFillColor1="rgba(243, 134, 0, 0.1)"
          startFillColor2="rgba(64, 193, 163, 0.1)"
          startOpacity={0.1}
          stepValue={stepValue}
          thickness1={3}
          thickness2={3}
          width={containerWidth}
          xAxisLabelsHeight={0}
          xAxisThickness={0}
          yAxisLabelWidth={0}
          yAxisTextStyle={{ color: 'transparent' }}
          yAxisThickness={0}
        />
        <FadingVerticalLines numberOfLines={numberOfPoints} />
        <DashedBottomLine />
      </View>

      <View style={styles.chartLegend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#F38600' }]} />
          <Text style={[styles.legendText, { color: '#F38600' }]}>
            {t('dashboard.savingsChart.legend.savingsValue')}
          </Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#40C1A3' }]} />
          <Text style={[styles.legendText, { color: '#40C1A3' }]}>
            {t('dashboard.savingsChart.legend.depositedAmount')}
          </Text>
        </View>
      </View>

      {children && <View style={styles.childrenContainer}>{children}</View>}
    </Box>
  );
};

const styles = StyleSheet.create((theme) => ({
  chartCard: {
    borderRadius: 16,
    shadowColor: theme.colors.primary[600],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  chartHeader: {
    marginBottom: 16,
  },
  chartAmount: {
    fontSize: 24,
    fontWeight: '500',
    color: theme.colors.grey[900],
    marginBottom: 4,
    lineHeight: 28.8,
  },
  chartSubtext: {
    fontSize: 16,
    color: theme.colors.grey[500],
    lineHeight: 19.2,
  },
  chartContainer: {
    height: 160,
    position: 'relative',
  },
  chartLegend: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  legendText: {
    fontSize: 12,
    lineHeight: 14.4,
  },
  verticalLinesContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 0,
  },
  dashedLineContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dash: {
    width: 4,
    height: 1,
    backgroundColor: theme.colors.grey[100],
  },
  childrenContainer: {
    marginTop: 8,
  },
}));
