import { fetchAPI } from '@api';
import { startPasswordReset } from '@endpoints/password-reset.endpoints';
import { useTranslations } from '@hooks';
import useErrorStore from '@store/error.store';
import { usePasswordResetCooldownStore } from '@store/password-reset.store';
import { useMutation } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useUnistyles } from 'react-native-unistyles';
import { ErrorKey } from 'types/error.types';

export const ResendResetLink = ({ email }: { email: string }) => {
  const t = useTranslations();
  const { theme } = useUnistyles();

  const { canSend, getRemainingMs, markSent } = usePasswordResetCooldownStore();
  const { addError } = useErrorStore();

  // Re-render every second to refresh countdown
  const [, forceTick] = useState(0);
  useEffect(() => {
    const id = setInterval(() => forceTick((v) => v + 1), 1000);
    return () => clearInterval(id);
  }, []);

  // Derive state on each render so it updates with tick
  const blocked = !canSend(email);
  const secondsLeft = Math.ceil(getRemainingMs(email) / 1000);

  const { mutate: sendResetLink, isPending } = useMutation({
    mutationFn: () => fetchAPI(startPasswordReset, { data: { email } }),
    onSuccess: () => {
      markSent(email);
    },
    onError: (error: AxiosError) => {
      addError(ErrorKey.PasswordResetError, {
        key: ErrorKey.PasswordResetError,
        errorText:
          Object.values(error.response?.data as Record<string, string>)?.[0] ||
          error.message,
        status: error.status,
        translate: false,
      });
    },
  });

  const onPress = useCallback(() => {
    if (blocked || isPending) return;
    sendResetLink();
  }, [blocked, isPending, sendResetLink]);

  const label = blocked
    ? t('passwordReset.resendLinkBlocked', { seconds: secondsLeft })
    : t('passwordReset.resendLinkLabel');

  return (
    <View>
      <Button disabled={blocked || isPending} mode="text" onPress={onPress}>
        <Text
          style={{
            color: blocked ? theme.colors.grey[500] : theme.colors.primary[600],
            maxWidth: 60,
          }}
          variant="bodyLarge"
        >
          {label}
        </Text>
      </Button>
    </View>
  );
};
