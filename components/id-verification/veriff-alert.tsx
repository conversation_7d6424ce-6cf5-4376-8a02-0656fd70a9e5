import type React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';

export type VeriffAlertType = 'success' | 'error' | 'info' | 'warning';

interface AlertProps {
  type: VeriffAlertType;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
}

export const VeriffAlert: React.FC<AlertProps> = ({
  type,
  title,
  subtitle,
  icon,
}) => {
  const alertStyles = getAlertStyles(type);

  return (
    <View style={[styles.container, alertStyles.container]}>
      <View style={styles.header}>
        {icon && <View style={styles.iconContainer}>{icon}</View>}
        <Text style={[styles.title, alertStyles.title]}>{title}</Text>
      </View>
      {subtitle && (
        <Text style={[styles.subtitle, alertStyles.subtitle]}>{subtitle}</Text>
      )}
    </View>
  );
};

const getAlertStyles = (type: VeriffAlertType) => {
  switch (type) {
    case 'success':
      return {
        container: {
          backgroundColor: 'rgba(36, 211, 39, 0.1)',
          borderColor: '#9FEFA0',
        },
        title: { color: '#24D327' },
        subtitle: { color: 'rgba(36, 211, 39, 0.5)' },
      };
    case 'error':
      return {
        container: {
          backgroundColor: 'rgba(233, 97, 96, 0.1)',
          borderColor: '#F9D2D2',
        },
        title: { color: '#E96160' },
        subtitle: { color: 'rgba(233, 97, 96, 0.5)' },
      };
    case 'info':
      return {
        container: {
          backgroundColor: 'rgba(0, 148, 254, 0.1)',
          borderColor: '#99D4FF',
        },
        title: { color: '#0077CC' },
        subtitle: { color: 'rgba(0, 119, 204, 0.5)' },
      };
    case 'warning':
      return {
        container: {
          backgroundColor: 'rgba(255, 149, 0, 0.1)',
          borderColor: '#FFD699',
        },
        title: { color: '#FF9500' },
        subtitle: { color: 'rgba(255, 149, 0, 0.5)' },
      };
    default:
      return {
        container: {},
        title: {},
        subtitle: {},
      };
  }
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    borderWidth: 1,
    paddingVertical: 24,
    paddingHorizontal: 16,
    minHeight: 80,
    justifyContent: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  iconContainer: {
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Figtree',
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 19.2,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Figtree',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14.4,
    textAlign: 'center',
    marginTop: 4,
  },
});
