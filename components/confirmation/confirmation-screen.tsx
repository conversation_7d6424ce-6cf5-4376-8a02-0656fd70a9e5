import SVGCHeckCircle from '@assets/check-circle.svg';
import { useTranslations } from '@hooks';
import { PageLayout, Title } from '@ui';
import { useEffect } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet } from 'react-native-unistyles';

const SVG_DIMENSION = 150;
const TIMING = 300;

interface IConfirmationScreen {
  message: string;
  buttonTitle: string;
  onButtonPress: () => void;
  headerBackHref?: string;
}

export const ConfirmationScreen = ({
  message,
  buttonTitle,
  onButtonPress,
  headerBackHref,
}: IConfirmationScreen) => {
  const t = useTranslations();
  const sharedOpacity = useSharedValue(0);
  const scale = useSharedValue(0);

  useEffect(() => {
    sharedOpacity.value = withDelay(400, withTiming(1, { duration: TIMING }));
    scale.value = withSpring(1, { damping: 7, stiffness: 200 });
  }, [sharedOpacity, scale]);

  const opacity = useAnimatedStyle(() => {
    return {
      opacity: sharedOpacity.value,
    };
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <PageLayout
      backButtonHref={headerBackHref}
      buttonText={t(buttonTitle)}
      contentGap={2}
      contentMarginTop={6}
      onButtonPress={onButtonPress}
    >
      <Animated.View style={[styles.iconContainer, animatedStyle]}>
        <SVGCHeckCircle height={SVG_DIMENSION} width={SVG_DIMENSION} />
      </Animated.View>
      <Animated.View style={opacity}>
        <Title textAlign="center">{t(message)}</Title>
      </Animated.View>
    </PageLayout>
  );
};

const styles = StyleSheet.create(() => ({
  iconContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}));
