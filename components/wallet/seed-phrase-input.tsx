import { useTranslations } from '@hooks';
import { Box, Text } from '@ui';
import { memo, useRef } from 'react';
import { TextInput } from 'react-native';
import { useUnistyles } from 'react-native-unistyles';

export interface SeedPhraseInputProps {
  phrases: string[];
  asInput?: boolean;
  userMnemonic?: string[];
  handleInputChange?: (text: string, index: number) => void;
  focusNextInput?: (index: number) => void;
  setRef?: (el: TextInput | null, index: number) => void;
}

export interface SeedPhraseItemProps {
  index: number;
  phrase: string;
  asInput?: boolean;
  userMnemonic?: string[];
  handleInputChange?: (text: string, index: number) => void;
  focusNextInput?: (index: number) => void;
  setRef?: (el: TextInput | null) => void;
}

export const SeedPhraseItem = memo<SeedPhraseItemProps>(
  ({
    index,
    phrase,
    asInput = false,
    userMnemonic,
    handleInputChange,
    focusNextInput,
    setRef,
  }) => {
    const t = useTranslations('wallet', { keyPrefix: 'saveSeedPhrase' });
    const { theme } = useUnistyles();

    return (
      <Box align="center" flexDirection="row" gap={2} key={phrase}>
        <Text color="grey.200" size="lg" w={20}>
          {index + 1}
        </Text>
        {asInput ? (
          <TextInput
            autoCapitalize="none"
            blurOnSubmit={false}
            contextMenuHidden
            onChangeText={(text) => handleInputChange?.(text, index)}
            onSubmitEditing={() => focusNextInput?.(index)}
            placeholder={t('placeholder')}
            placeholderTextColor={theme.colors.grey[300]}
            ref={(el) => {
              setRef?.(el);
            }}
            returnKeyType={index === 11 ? 'done' : 'next'}
            style={{
              flex: 1,
              fontSize: 18,
              lineHeight: 24,
              fontWeight: '500',
            }}
            value={userMnemonic?.[index]}
          />
        ) : (
          <Text size="lg" weight="medium">
            {phrase}
          </Text>
        )}
      </Box>
    );
  }
);

SeedPhraseItem.displayName = 'SeedPhraseItem';

export const SeedPhraseGrid = memo<SeedPhraseInputProps>(
  ({
    phrases,
    asInput = false,
    userMnemonic,
    handleInputChange,
    focusNextInput,
    setRef,
  }) => {
    const inputRefs = useRef<(TextInput | null)[]>([]);

    const half = Math.ceil(phrases.length / 2);
    const firstHalf = phrases.slice(0, half);
    const secondHalf = phrases.slice(half);

    const handleSetRef = (el: TextInput | null, index: number) => {
      inputRefs.current[index] = el;
      setRef?.(el, index);
    };

    return (
      <Box
        bgColor="grey.50"
        borderColor="grey.100"
        borderWidth={1}
        flexDirection="row"
        gap={4}
        mt={2}
        p={6}
        radius={24}
      >
        <Box flex={1} gap={4}>
          {firstHalf.map((phrase, i) => (
            <SeedPhraseItem
              asInput={asInput}
              focusNextInput={focusNextInput}
              handleInputChange={handleInputChange}
              index={i}
              key={asInput ? i : phrase}
              phrase={phrase}
              setRef={(el) => handleSetRef(el, i)}
              userMnemonic={userMnemonic}
            />
          ))}
        </Box>

        <Box flex={1} gap={4}>
          {secondHalf.map((phrase, i) => {
            const index = i + half;
            return (
              <SeedPhraseItem
                asInput={asInput}
                focusNextInput={focusNextInput}
                handleInputChange={handleInputChange}
                index={index}
                key={asInput ? index : phrase}
                phrase={phrase}
                setRef={(el) => handleSetRef(el, index)}
                userMnemonic={userMnemonic}
              />
            );
          })}
        </Box>
      </Box>
    );
  }
);

SeedPhraseGrid.displayName = 'SeedPhraseGrid';

export default {
  SeedPhraseItem,
  SeedPhraseGrid,
};
