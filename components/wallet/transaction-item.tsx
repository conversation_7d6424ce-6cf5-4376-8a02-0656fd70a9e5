import { useTranslations } from '@hooks';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { Box, Text } from '@ui';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import type { Payment } from 'types/breez-sdk.types';
import dayjs from 'utils/dayjs-config';

interface TransactionItemProps {
  payment: Payment;
}

export const TransactionItem = ({ payment }: TransactionItemProps) => {
  const t = useTranslations('wallet');
  const { convertSatoshis } = useExchangeRate();

  const isReceived = payment.paymentType === 'receive';
  const amount = payment.amountSat / 100_000_000;
  const amountPLN = convertSatoshis(payment.amountSat, 'PLN');

  const timestamp = payment.timestamp
    ? dayjs(payment.timestamp * 1000)
    : dayjs();

  const handlePress = () => {
    router.push(`/(wallet)/transaction/${payment.txId}`);
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <Box
        align="center"
        bgColor="grey.50"
        flexDirection="row"
        justify="between"
        p={3}
        radius={8}
      >
        <Box flex={1} gap={1}>
          <Box align="center" flexDirection="row" gap={2}>
            <Text size="sm" weight="medium">
              {isReceived ? '↓' : '↑'}{' '}
              {t(`transactions.${isReceived ? 'received' : 'sent'}`)}
            </Text>
            {payment.status === 'pending' && (
              <Text color="orange" size="xs" weight="medium">
                {t('transactions.pending')}
              </Text>
            )}
            {payment.status === 'failed' && (
              <Text color="red" size="xs" weight="medium">
                {t('transactions.failed')}
              </Text>
            )}
            {payment.status === 'complete' && (
              <Text color="green" size="xs" weight="medium">
                ✓
              </Text>
            )}
          </Box>

          <Text color="grey.600" size="xs">
            {timestamp.fromNow()}
          </Text>
        </Box>

        <Box align="end" gap={1}>
          <Text color={isReceived ? 'green' : 'red'} size="sm" weight="medium">
            {isReceived ? '+' : '-'}
            {amount.toFixed(8)} BTC
          </Text>
          <Text color="grey.600" size="xs">
            ≈ {amountPLN.toFixed(2)} PLN
          </Text>
        </Box>
      </Box>
    </TouchableOpacity>
  );
};
