import { useTranslations } from '@hooks';
import { useExchangeRate } from 'hooks/use-exchange-rate';
import { Box, Text } from '@ui';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import type { Payment } from 'types/breez-sdk.types';
import type { CachedTransactionData } from 'types/cache.types';
import dayjs from 'utils/dayjs-config';

// Unified transaction data structure
interface UnifiedTransactionData {
  id: string;
  type: 'received' | 'sent';
  amountSat: number;
  amountBTC: number;
  amountPLN: number;
  timestamp: number;
  status?: 'pending' | 'failed' | 'complete';
  isClickable: boolean;
  txId?: string;
}

interface UnifiedTransactionItemProps {
  transaction: UnifiedTransactionData;
}

// Adapter function to convert Payment to UnifiedTransactionData
export const adaptPaymentToUnified = (
  payment: Payment,
  convertSatoshis: (satoshis: number, currency: 'PLN') => number
): UnifiedTransactionData => {
  const isReceived = payment.paymentType === 'receive';
  const amountBTC = payment.amountSat / 100_000_000;
  const amountPLN = convertSatoshis(payment.amountSat, 'PLN');

  return {
    id: payment.txId,
    type: isReceived ? 'received' : 'sent',
    amountSat: payment.amountSat,
    amountBTC,
    amountPLN,
    timestamp: payment.timestamp ? payment.timestamp * 1000 : Date.now(),
    status: payment.status as 'pending' | 'failed' | 'complete',
    isClickable: true,
    txId: payment.txId,
  };
};

// Adapter function to convert CachedTransactionData to UnifiedTransactionData
export const adaptCachedToUnified = (
  cached: CachedTransactionData,
  convertSatoshis: (satoshis: number, currency: 'PLN') => number
): UnifiedTransactionData => {
  const isReceived = cached.type === 'deposit';
  const amountSat = Math.round(cached.btc * 100_000_000);
  const amountPLN = convertSatoshis(amountSat, 'PLN');

  return {
    id: `cached-${cached.id}`,
    type: isReceived ? 'received' : 'sent',
    amountSat,
    amountBTC: cached.btc,
    amountPLN,
    timestamp: new Date(cached.date).getTime(),
    status: 'complete', // Cached transactions are always complete
    isClickable: false, // Cached transactions don't have detail pages
    txId: undefined,
  };
};

export const UnifiedTransactionItem = ({ transaction }: UnifiedTransactionItemProps) => {
  const t = useTranslations('wallet');

  const isReceived = transaction.type === 'received';
  const timestamp = dayjs(transaction.timestamp);

  const handlePress = () => {
    if (transaction.isClickable && transaction.txId) {
      router.push(`/(wallet)/transaction/${transaction.txId}`);
    }
  };

  const content = (
    <Box
      align="center"
      bgColor="grey.50"
      flexDirection="row"
      justify="between"
      p={3}
      radius={8}
    >
      <Box flex={1} gap={1}>
        <Box align="center" flexDirection="row" gap={2}>
          <Text size="sm" weight="medium">
            {isReceived ? '↓' : '↑'}{' '}
            {t(`transactions.${isReceived ? 'received' : 'sent'}`)}
          </Text>
          {transaction.status === 'pending' && (
            <Text color="orange" size="xs" weight="medium">
              {t('transactions.pending')}
            </Text>
          )}
          {transaction.status === 'failed' && (
            <Text color="red" size="xs" weight="medium">
              {t('transactions.failed')}
            </Text>
          )}
          {transaction.status === 'complete' && (
            <Text color="green" size="xs" weight="medium">
              ✓
            </Text>
          )}
        </Box>

        <Text color="grey.600" size="xs">
          {timestamp.fromNow()}
        </Text>
      </Box>

      <Box align="end" gap={1}>
        <Text color={isReceived ? 'green' : 'red'} size="sm" weight="medium">
          {isReceived ? '+' : '-'}
          {transaction.amountBTC.toFixed(8)} BTC
        </Text>
        <Text color="grey.600" size="xs">
          ≈ {transaction.amountPLN.toFixed(2)} PLN
        </Text>
      </Box>
    </Box>
  );

  // Wrap in TouchableOpacity only if clickable
  if (transaction.isClickable) {
    return <TouchableOpacity onPress={handlePress}>{content}</TouchableOpacity>;
  }

  return content;
};
