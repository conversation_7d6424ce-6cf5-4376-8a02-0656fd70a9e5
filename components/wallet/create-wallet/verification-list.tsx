import SvgVerificationNonChecked from '@assets/check-non-verified.svg';
import SvgVerificationChecked from '@assets/check-verified.svg';
import { useTranslations } from '@hooks';
import {
  hasLowercaseSchema,
  hasNumberSchema,
  minLengthWalletSchema,
} from '@utils';
import type React from 'react';
import { memo, useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

const DIMENSION = 25;

interface IVerificationItem {
  isVerified: boolean;
  label: string;
}

export const VerificationLabel: React.FC<IVerificationItem> = memo(
  ({ isVerified = false, label }) => {
    const t = useTranslations();
    return (
      <View style={styles.row}>
        <View style={styles.rowIcon}>
          {isVerified ? (
            <SvgVerificationChecked height={DIMENSION} width={DIMENSION} />
          ) : (
            <SvgVerificationNonChecked height={DIMENSION} width={DIMENSION} />
          )}
        </View>
        <View>
          <Text style={styles.label}>{t(label)}</Text>
        </View>
      </View>
    );
  }
);

export const VerificationList: React.FC<{ password?: string }> = memo(
  ({ password }) => {
    const [validationList, setValidationList] = useState<
      { isVerified: boolean; label: string }[]
    >([]);

    useEffect(() => {
      const validate = () => {
        return [
          {
            isVerified: minLengthWalletSchema.safeParse(password).success,
            label: 'validators.validationCheck.minLengthWallet',
          },
          {
            isVerified: hasLowercaseSchema.safeParse(password).success,
            label: 'validators.validationCheck.minLengthSmallLetter',
          },
          {
            isVerified: hasNumberSchema.safeParse(password).success,
            label: 'validators.validationCheck.requiredOneDigit',
          },
        ];
      };

      setValidationList(validate());
    }, [password]);

    return (
      <View style={styles.container}>
        {validationList.map((element, index) => (
          <VerificationLabel
            isVerified={element.isVerified}
            key={`${index}-${element.label}`}
            label={element.label}
          />
        ))}
      </View>
    );
  }
);

const styles = StyleSheet.create((theme) => ({
  container: {
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowIcon: {
    marginRight: 10,
  },
  label: {
    color: theme.colors.dark,
  },
}));
