/** biome-ignore-all lint/suspicious/noArrayIndexKey: for empty seed phrase state index is required */
import { WALLET_VALIDATION } from '@utils/wallet/constants';
import EyeIcon from 'assets/eye-icon.svg';
import { BlurView } from 'expo-blur';
import { useEffect, useState } from 'react';
import {
  AppState,
  type GestureResponderEvent,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

interface SeedPhraseProps {
  mnemonic?: string;
  isRevealed?: boolean;
  isBlurred?: boolean;
  onPress?: (event: GestureResponderEvent) => void;
  showEyeIcon?: boolean;
}

export const SeedPhrase = ({
  mnemonic,
  isRevealed = false,
  isBlurred = false,
  onPress,
  showEyeIcon = false,
}: SeedPhraseProps) => {
  const phrases = mnemonic
    ? mnemonic.split(' ')
    : new Array(WALLET_VALIDATION.MNEMONIC_WORD_COUNT).fill('');
  const [isAppActive, setIsAppActive] = useState(true);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setIsAppActive(nextAppState === 'active');
    };

    const appStateSubscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );

    return () => {
      appStateSubscription.remove();
    };
  }, []);

  const half = Math.ceil(phrases.length / 2);
  const firstHalf = phrases.slice(0, half);
  const secondHalf = phrases.slice(half);

  const shouldBeBlurred = isBlurred && isRevealed && !isAppActive;

  const renderPhrase = (phrase: string) => {
    if (isRevealed) {
      return <Text style={styles.phraseText}>{phrase}</Text>;
    }
    return <View style={styles.mockedText} />;
  };

  return (
    <View style={styles.seedPhraseContainer}>
      <View style={styles.column}>
        {firstHalf.map((phrase, index) => (
          <View key={phrase + index} style={styles.phraseItem}>
            <Text style={styles.phraseIndex}>{index + 1}</Text>
            {renderPhrase(phrase)}
          </View>
        ))}
      </View>
      <View style={styles.column}>
        {secondHalf.map((phrase, index) => (
          <View key={phrase + index} style={styles.phraseItem}>
            <Text style={styles.phraseIndex}>{index + 1 + half}</Text>
            {renderPhrase(phrase)}
          </View>
        ))}
      </View>
      {showEyeIcon && !isRevealed && (
        <TouchableOpacity
          activeOpacity={0.8}
          disabled={!onPress}
          onPress={onPress}
          style={styles.eyeButton}
        >
          <EyeIcon height={24} width={24} />
        </TouchableOpacity>
      )}
      {shouldBeBlurred && (
        <BlurView intensity={100} style={StyleSheet.absoluteFill} />
      )}
    </View>
  );
};

const styles = StyleSheet.create((theme) => ({
  seedPhraseContainer: {
    flexDirection: 'row',
    padding: 24,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: theme.colors.grey[100],
    backgroundColor: theme.colors.grey[50],
    gap: 16,
    overflow: 'hidden',
    position: 'relative',
    height: 280,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  column: {
    flex: 1,
    flexDirection: 'column',
    gap: 16,
  },
  phraseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  phraseIndex: {
    fontSize: 18,
    lineHeight: 24,
    color: theme.colors.grey[300],
    width: 25,
  },
  phraseText: {
    fontSize: 18,
    lineHeight: 24,
    color: theme.colors.dark,
    fontWeight: '500',
  },
  mockedText: {
    height: 24,
    width: 100,
    backgroundColor: theme.colors.grey[100],
    borderRadius: 8,
  },
  eyeButton: {
    position: 'absolute',
    borderRadius: 35,
    backgroundColor: theme.colors.primary[600],
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
}));
