import { useOnError } from '@hooks';
import useErrorStore from '@store/error.store';
import { NotificationContainer, NotificationType } from '@ui';
import { useCallback, useState } from 'react';
import { type AppError, ErrorKey } from 'types/error.types';

export const RegisterErrorContainer = ({
  callback,
  onClear,
}: {
  callback: () => void;
  onClear?: () => void;
}) => {
  const [show, setShow] = useState(false);
  const [errorMessage, setErrorMessage] = useState<AppError | undefined>(
    undefined
  );
  const { clearError } = useErrorStore();
  useOnError(ErrorKey.RegistrationError, (error) => {
    if (error) {
      callback();
      setShow(true);
      setErrorMessage(error);
    } else {
      setShow(false);
      setErrorMessage(undefined);
    }
  });

  const onClearPress = useCallback(() => {
    onClear?.();
    clearError(ErrorKey.RegistrationError);
  }, [clearError, onClear]);

  const onPress = useCallback(() => {
    setShow(false);
    callback();
    onClearPress();
  }, [callback, onClearPress]);

  return (
    <NotificationContainer
      label={errorMessage?.errorText}
      onPress={onPress}
      show={show}
      translate={errorMessage?.translate}
      type={NotificationType.Error}
    />
  );
};
