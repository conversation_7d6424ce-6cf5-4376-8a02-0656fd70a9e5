import { fetchAPI } from '@api';
import { resendCode as resendCodeEndpoint } from '@endpoints/registration.endpoints';
import { useTranslations } from '@hooks';
import useErrorStore from '@store/error.store';
import { useMutation } from '@tanstack/react-query';
import type { Endpoint } from '@utils/api/types';
import type { AxiosError } from 'axios';
import { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useUnistyles } from 'react-native-unistyles';
import { ErrorKey } from 'types/error.types';

const DEFAULT_COOLDOWN_MS = 10_000;

export const ResendCode = ({
  username,
  onResend,
  endpoint = resendCodeEndpoint,
  errorKey = ErrorKey.RegistrationError,
  payloadBuilder,
  label,
  blockedLabelBuilder,
  cooldownMs = DEFAULT_COOLDOWN_MS,
  initialRemainingMs = 0,
  // Controlled mode (optional)
  blocked,
  secondsLeft,
  onResendPress,
}: {
  username: string;
  onResend?: () => void;
  endpoint?: Endpoint;
  errorKey?: ErrorKey;
  payloadBuilder?: (value: string) => Record<string, unknown>;
  label?: string;
  blockedLabelBuilder?: (secondsLeft: number) => string;
  cooldownMs?: number;
  initialRemainingMs?: number;
  blocked?: boolean;
  secondsLeft?: number;
  onResendPress?: () => void;
}) => {
  const t = useTranslations();
  const { theme } = useUnistyles();

  const [isBlockedInternal, setIsBlockedInternal] = useState(false);
  const [counterTime, setCounterTime] = useState(DEFAULT_COOLDOWN_MS);

  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const { addError } = useErrorStore();

  // initialize from external remaining time
  useEffect(() => {
    if (initialRemainingMs > 0) {
      setIsBlockedInternal(true);
      setCounterTime(initialRemainingMs);
    } else {
      setIsBlockedInternal(false);
      setCounterTime(cooldownMs);
    }
  }, [cooldownMs, initialRemainingMs]);

  // cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (counterTime <= 0 && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      setIsBlockedInternal(false);
      setCounterTime(cooldownMs);
    }
  }, [counterTime, cooldownMs]);

  const startCounter = useCallback(() => {
    setIsBlockedInternal(true);
    setCounterTime(cooldownMs);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    intervalRef.current = setInterval(() => {
      setCounterTime((value) => value - 1000);
    }, 1000);
  }, [cooldownMs]);

  const { mutate: resendCode } = useMutation({
    mutationFn: () =>
      fetchAPI(endpoint, {
        data: payloadBuilder ? payloadBuilder(username) : { username },
      }),
    onSuccess: () => {
      onResend?.();
    },
    onError: (error: AxiosError) => {
      addError(errorKey, {
        key: errorKey,
        errorText:
          Object.values(error.response?.data as Record<string, string>)?.[0] ||
          error.message,
        status: error.status,
        translate: false,
      });
    },
  });

  const handlePress = useCallback(() => {
    if (onResendPress) {
      onResendPress();
      return;
    }
    startCounter();
    resendCode();
  }, [onResendPress, resendCode, startCounter]);

  const effectiveBlocked = blocked ?? isBlockedInternal;
  const effectiveSeconds =
    secondsLeft ?? Math.max(0, Math.round(counterTime / 1000));

  const defaultBlocked = t('activationCode.resendCodeBlocked', {
    seconds: effectiveSeconds,
  });
  const defaultLabel = t('activationCode.resendCodeLabel');

  return (
    <View>
      <Button disabled={effectiveBlocked} mode="text" onPress={handlePress}>
        <Text
          style={{
            color: effectiveBlocked
              ? theme.colors.grey[500]
              : theme.colors.primary[600],
            maxWidth: 60,
          }}
          variant="bodyLarge"
        >
          {effectiveBlocked
            ? (blockedLabelBuilder?.(effectiveSeconds) ?? defaultBlocked)
            : (label ?? defaultLabel)}
        </Text>
      </Button>
    </View>
  );
};
