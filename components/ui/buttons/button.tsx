import { type SpacingProps, useSpacing } from '@hooks';
import { router } from 'expo-router';
import type React from 'react';
import {
  ActivityIndicator,
  type DimensionValue,
  type GestureResponderEvent,
  Text,
  type TextStyle,
  TouchableOpacity,
  View,
  type ViewStyle,
} from 'react-native';
import { StyleSheet, type UnistylesVariants } from 'react-native-unistyles';

const styles = StyleSheet.create((theme) => ({
  button: {
    fontSize: 18,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.gap(2),
    variants: {
      size: {
        default: {
          paddingBlock: theme.padding(5),
          paddingInline: theme.padding(4),
        },
        xs: {
          paddingBlock: theme.padding(3.5),
          paddingInline: theme.padding(4),
        },
        lg: {
          paddingBlock: theme.padding(5),
        },
      },
      variant: {
        default: {
          backgroundColor: theme.colors.primary[600],
        },
        contained: {
          backgroundColor: theme.colors.primary[600],
        },
        outlined: {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.primary[600],
        },
        outlinedGrey: {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.grey[800],
        },
        secondary: {
          backgroundColor: theme.colors.primary[600],
        },
        muted: {
          backgroundColor: theme.colors.grey[800],
        },
        destructive: {
          backgroundColor: theme.colors.red,
        },
        disabled: {
          backgroundColor: theme.colors.grey[100],
        },
      },
      rounded: {
        default: {
          borderRadius: 32,
        },
        full: {
          borderRadius: 100,
        },
      },
      withFarLeftIcon: {
        true: {
          width: '100%',
          justifyContent: 'space-between',
          paddingBlock: theme.padding(4),
        },
        false: {
          justifyContent: 'center',
        },
      },
    },
  },
}));

const labelStyles = StyleSheet.create((theme) => ({
  label: {
    fontSize: 18,
    fontWeight: '500',
    variants: {
      size: {
        default: {
          fontSize: 18,
        },
        xs: {
          fontSize: 14,
          fontWeight: '400',
        },
        lg: {
          fontSize: 18,
        },
      },
      variant: {
        default: {
          color: theme.colors.white,
        },
        contained: {
          color: theme.colors.white,
        },
        outlined: {
          color: theme.colors.primary[600],
        },
        outlinedGrey: {
          color: theme.colors.grey[800],
        },
        secondary: {
          color: theme.colors.primary[600],
        },
        muted: {
          color: theme.colors.white,
        },
        destructive: {
          color: theme.colors.white,
        },
        disabled: {
          color: theme.colors.grey[300],
        },
      },
    },
  },
}));

type ButtonVariants = UnistylesVariants<typeof styles>;
type LabelVariants = UnistylesVariants<typeof labelStyles>;

/**
 * Defines the visual appearance of the button. This includes variants for size,
 * style (e.g., 'outlined'), and shape (e.g., 'rounded').
 */
type ButtonAppearanceProps = ButtonVariants & LabelVariants;

/**
 * Controls the layout and positioning of the button. This includes spacing
 * (margins, padding), dimensions (width), and alignment.
 */
type ButtonLayoutProps = SpacingProps & {
  /**
   * The width of the button.
   */
  w?: DimensionValue;
  /**
   * Specifies the alignment of the button's children (icon and text).
   */
  align?: ViewStyle['alignItems'];
  /**
   * Specifies how the button aligns itself within its parent container.
   */
  self?: ViewStyle['alignSelf'];
  /**
   * Background color of the button. Takes in key from theme.colors.
   *
   * @see `unistyles.ts` for available colors.
   */
  bgColor?: string;
  /**
   * Allows passing custom styles directly to the underlying `TouchableOpacity` component.
   */
  buttonStyle?: ViewStyle;
  /**
   * Allows passing custom styles directly to the button's `Text` component.
   */
  labelStyle?: TextStyle;
};

/**
 * Manages the content displayed inside the button, including the main text (children)
 * and an optional icon.
 */
type ButtonContentProps = {
  /**
   * The primary content of the button, typically a string of text.
   */
  children: React.ReactNode;
  /**
   * An optional icon element to be displayed alongside the button's text.
   */
  icon?: React.ReactNode;
  /**
   * The position of the icon.
   *
   * @default 'left'
   * @options 'farLeft', 'left', 'right'
   */
  iconPosition?: 'farLeft' | 'left' | 'right';
};

/**
 * Handles the button's interactive behavior, such as click events, navigation,
 * and states like 'disabled' or 'loading'.
 */
type ButtonBehaviorProps = {
  /**
   * The function to be called when the button is pressed.
   */
  onPress?: (e: GestureResponderEvent) => void;
  /**
   * If `true`, the button will be non-interactive and visually styled as disabled.
   *
   * @default false
   */
  disabled?: boolean;
  /**
   * If `true`, displays a loading indicator and disables the button.
   *
   * @default false
   */
  loading?: boolean;
  /**
   * An optional URL. If provided, pressing the button will navigate to this URL using `expo-router`.
   */
  href?: string;
  hrefType?: 'push' | 'replace';
};

/**
 * A comprehensive set of props for the `Button` component, combining appearance,
 * layout, content, and behavior properties into a single type.
 */
export type ButtonProps = ButtonAppearanceProps &
  ButtonLayoutProps &
  ButtonContentProps &
  ButtonBehaviorProps;

export const Button = ({
  onPress,
  buttonStyle,
  labelStyle,
  variant,
  size,
  disabled,
  children,
  href,
  hrefType = 'push',
  icon,
  iconPosition = 'left',
  rounded,
  w,
  align,
  self,
  bgColor,
  loading,
  ...rest
}: ButtonProps) => {
  const isDisabled = disabled || loading;

  styles.useVariants({
    variant: isDisabled ? 'disabled' : variant,
    rounded,
    size,
    withFarLeftIcon: iconPosition === 'farLeft',
  });
  labelStyles.useVariants({
    variant: isDisabled ? 'disabled' : variant,
    size,
  });

  const spacingStyles = useSpacing(rest);

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      disabled={isDisabled}
      onPress={(e) => {
        if (loading) {
          return;
        }
        onPress?.(e);
        if (href) {
          router[hrefType](href);
        }
      }}
      style={[
        styles.button,
        spacingStyles,
        buttonStyle,
        {
          width: w || 'auto',
          ...(align && { alignItems: align }),
          ...(self && { alignSelf: self }),
          ...(bgColor && { backgroundColor: bgColor }),
        },
      ]}
      {...rest}
    >
      {loading ? (
        <ActivityIndicator color={labelStyles.label.color as string} />
      ) : (
        <>
          {icon && iconPosition === 'farLeft' && icon}
          {icon && iconPosition === 'left' && icon}
          <Text style={[labelStyles.label, labelStyle]}>{children}</Text>
          {icon && iconPosition === 'right' && icon}
          {icon && iconPosition === 'farLeft' && <View style={{ width: 10 }} />}
        </>
      )}
    </TouchableOpacity>
  );
};
