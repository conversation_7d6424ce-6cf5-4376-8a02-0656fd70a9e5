import { useTranslations } from '@hooks';
import CheckIcon from 'assets/check-circle-filled.svg';
import CopyEmptyIcon from 'assets/copy-empty.svg';
import CopyIcon from 'assets/copy-icon.svg';
import { setStringAsync } from 'expo-clipboard';
import { useRef, useState } from 'react';
import { Dimensions, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

export const CopyButtonVariants = {
  DEFAULT: 'default',
  CLEAN: 'clean',
} as const;

export type CopyButtonVariant =
  (typeof CopyButtonVariants)[keyof typeof CopyButtonVariants];

interface CopyButtonProps {
  textToCopy: string;
  variant?: CopyButtonVariant;
}

export const CopyButton = ({
  textToCopy,
  variant = CopyButtonVariants.DEFAULT,
}: CopyButtonProps) => {
  const t = useTranslations();
  const [copied, setCopied] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<
    'center' | 'left' | 'right'
  >('center');
  const buttonRef = useRef<TouchableOpacity>(null);
  const { theme } = useUnistyles();

  const handleCopy = async () => {
    await setStringAsync(textToCopy);
    setCopied(true);

    if (buttonRef.current) {
      buttonRef.current.measure((_x, _y, width, _height, pageX, _pageY) => {
        const screenWidth = Dimensions.get('window').width;
        const tooltipWidth = 60;
        const buttonCenter = pageX + width / 2;

        if (buttonCenter + tooltipWidth / 2 > screenWidth - 10) {
          setTooltipPosition('right');
        } else if (buttonCenter - tooltipWidth / 2 < 10) {
          setTooltipPosition('left');
        } else {
          setTooltipPosition('center');
        }
      });
    }

    setTimeout(() => setCopied(false), 2000);
  };

  const isClean = variant === CopyButtonVariants.CLEAN;

  if (isClean) {
    return (
      <TouchableOpacity
        onPress={handleCopy}
        style={[styles.copyButton, styles.cleanButton]}
      >
        <View style={styles.copyButtonContent}>
          {copied ? <CheckIcon /> : <CopyEmptyIcon fill={theme.colors.dark} />}
          <Text style={[styles.copyButtonText, styles.cleanButtonText]}>
            {copied ? t('common.copied') : t('common.copy')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      activeOpacity={0.75}
      onPress={handleCopy}
      ref={buttonRef}
      style={styles.container}
    >
      <CopyIcon height={24} width={24} />
      {copied && (
        <Text
          style={[
            styles.copiedText,
            tooltipPosition === 'right' && styles.copiedTextRight,
            tooltipPosition === 'left' && styles.copiedTextLeft,
          ]}
        >
          {t('common.copied')}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    position: 'relative',
  },
  copiedText: {
    fontSize: 12,
    color: theme.colors.white,
    backgroundColor: theme.colors.primary[600],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    position: 'absolute',
    top: -32,
    left: '50%',
    transform: [{ translateX: -25 }],
    textAlign: 'center',
    minWidth: 50,
    fontWeight: '500',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  copiedTextRight: {
    left: 'auto',
    right: 0,
    transform: [{ translateX: 0 }],
  },
  copiedTextLeft: {
    left: 0,
    transform: [{ translateX: 0 }],
  },
  copyButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cleanButton: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.grey[800],
    borderWidth: 1,
  },
  copyButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  copyButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  cleanButtonText: {
    color: theme.colors.grey[800],
  },
}));
