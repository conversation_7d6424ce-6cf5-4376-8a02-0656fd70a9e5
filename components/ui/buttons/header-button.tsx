import { useTranslations } from '@hooks';
import type React from 'react';
import {
  Text,
  type TextStyle,
  TouchableOpacity,
  type ViewStyle,
} from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

type HeaderButtonProps = {
  onPress: () => void;
  translationTitle: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
};

export const HeaderButton: React.FC<HeaderButtonProps> = ({
  onPress,
  translationTitle,
  style,
  textStyle,
  testID,
}) => {
  const t = useTranslations();

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={onPress}
      style={[styles.button, style]}
      testID={testID}
    >
      <Text style={[styles.buttonText, textStyle]}>{t(translationTitle)}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create((theme) => ({
  button: {
    backgroundColor: theme.colors.primary[600],
    borderRadius: 100,
    paddingHorizontal: 16,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    height: 42,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.white,
    includeFontPadding: false,
  },
}));
