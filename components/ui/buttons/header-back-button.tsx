import { useRouter } from 'expo-router';
import { useCallback } from 'react';
import { IconButton } from './icon-button';

export const HeaderBackButton = ({
  overrideOnPress,
}: {
  overrideOnPress?: () => void;
}) => {
  const router = useRouter();

  const onPress = useCallback(() => {
    router.back();
  }, [router]);

  return <IconButton icon="arrow-left" onPress={overrideOnPress ?? onPress} />;
};
