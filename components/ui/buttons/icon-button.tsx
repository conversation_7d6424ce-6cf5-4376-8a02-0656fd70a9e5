import type { ViewStyle } from 'react-native';
import { IconButton as RNIconButton } from 'react-native-paper';
import type { IconSource } from 'react-native-paper/lib/typescript/components/Icon';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

const styles = StyleSheet.create((theme) => ({
  iconButton: {
    padding: theme.padding(2),
    backgroundColor: theme.colors.grey[100],
  },
}));

type IconButtonProps = {
  iconButtonStyle?: ViewStyle;
  disabled?: boolean;
  onPress?: () => void;
  icon: IconSource;
};

export const IconButton = ({
  onPress,
  iconButtonStyle,
  disabled,
  icon,
  ...rest
}: IconButtonProps) => {
  const { theme } = useUnistyles();

  return (
    <RNIconButton
      disabled={disabled}
      icon={icon}
      iconColor={theme.colors.grey[900]}
      onPress={onPress}
      size={16}
      style={[styles.iconButton, iconButtonStyle]}
      {...rest}
    />
  );
};
