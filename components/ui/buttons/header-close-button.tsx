import { useTranslations } from '@hooks';
import { useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { ConfirmExitDialog } from '../dialog/confirm-exit-dialog';
import { IconButton } from './icon-button';

export const HeaderCloseButton = () => {
  const [visible, setVisible] = useState(false);
  const router = useRouter();

  const t = useTranslations();

  const showDialog = useCallback(() => {
    setVisible(true);
  }, []);

  const hideDialog = useCallback(() => {
    setVisible(false);
  }, []);

  const handleConfirm = useCallback(() => {
    hideDialog();
    router.replace({ pathname: '(authenticated)' });
  }, [router, hideDialog]);

  return (
    <>
      <IconButton icon="close" onPress={showDialog} />
      <ConfirmExitDialog
        cancelLabel={t('common.yes')}
        confirmLabel={t('common.no')}
        description={t('exitDialog.description')}
        onConfirm={hideDialog}
        onDismiss={handleConfirm}
        title={t('exitDialog.title')}
        visible={visible}
      />
    </>
  );
};
