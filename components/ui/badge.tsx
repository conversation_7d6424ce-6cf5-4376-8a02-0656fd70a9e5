import { type SpacingProps, useSpacing } from '@hooks';
import type React from 'react';
import { Text, type TextStyle, View, type ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

const styles = StyleSheet.create((theme) => ({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    gap: theme.gap(1),
    paddingHorizontal: theme.padding(2),
    paddingVertical: theme.padding(0.5),
    borderRadius: 100,
    minHeight: 20,
    variants: {
      variant: {
        default: {
          backgroundColor: theme.colors.primary[100],
        },
        primary: {
          backgroundColor: theme.colors.primary[600],
          borderColor: theme.colors.primary[300],
          borderWidth: 1,
        },
        success: {
          backgroundColor: `${theme.colors.green}1A`,
        },
        error: {
          backgroundColor: `${theme.colors.red}1A`,
        },
        info: {
          backgroundColor: `${theme.colors.blue}1A`,
        },
        warning: {
          backgroundColor: `${theme.colors.yellow}1A`,
        },
        outline: {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.primary[600],
        },
        lightPrimary: {
          backgroundColor: `${theme.colors.primary[100]}80`,
          borderWidth: 1,
          borderColor: theme.colors.primary[200],
          paddingHorizontal: theme.padding(3),
          paddingVertical: theme.padding(1.5),
        },
        grey: {
          backgroundColor: theme.colors.grey[50],
          borderWidth: 1,
          borderColor: theme.colors.grey[100],
          paddingHorizontal: theme.padding(3),
          paddingVertical: theme.padding(1.5),
        },
      },
      size: {
        sm: {
          paddingHorizontal: theme.padding(1.5),
          minHeight: 16,
        },
        md: {
          paddingHorizontal: theme.padding(2),
          minHeight: 20,
        },
        lg: {
          paddingHorizontal: theme.padding(3),
          minHeight: 28,
        },
      },
    },
  },
}));

const labelStyles = StyleSheet.create((theme) => ({
  label: {
    fontWeight: '500',
    fontSize: 12,
    color: theme.colors.primary[600],
    variants: {
      variant: {
        default: {
          color: theme.colors.primary[600],
        },
        primary: {
          color: theme.colors.white,
        },
        success: {
          color: theme.colors.green,
        },
        error: {
          color: theme.colors.red,
        },
        info: {
          color: theme.colors.blue,
        },
        warning: {
          color: theme.colors.yellow,
        },
        outline: {
          color: theme.colors.primary[600],
        },
        lightPrimary: {
          color: theme.colors.primary[800],
        },
        grey: {
          color: theme.colors.dark,
        },
      },
      size: {
        sm: {
          fontSize: 10,
        },
        md: {
          fontSize: 12,
        },
        lg: {
          fontSize: 14,
        },
      },
    },
  },
}));

type BadgeVariant =
  | 'default'
  | 'primary'
  | 'success'
  | 'error'
  | 'info'
  | 'warning'
  | 'outline'
  | 'lightPrimary'
  | 'grey';

export interface BadgeProps extends SpacingProps {
  children: React.ReactNode;
  icon?: React.ReactNode;
  style?: ViewStyle;
  labelStyle?: TextStyle;
  variant?: BadgeVariant;
  size?: 'sm' | 'md' | 'lg';
}

export const Badge = ({
  children,
  icon,
  variant = 'default',
  size = 'md',
  style,
  labelStyle,
  ...rest
}: BadgeProps) => {
  styles.useVariants({
    variant: variant === 'default' ? undefined : variant,
    size,
  });
  labelStyles.useVariants({
    variant: variant === 'default' ? undefined : variant,
    size,
  });
  const spacingStyles = useSpacing(rest as SpacingProps);

  return (
    <View style={[styles.badge, spacingStyles, style]}>
      {icon && icon}
      <Text style={[labelStyles.label, labelStyle]}>{children}</Text>
    </View>
  );
};
