import {
  Image as ExpoImage,
  type ImageProps as ExpoImageProps,
} from 'expo-image';
import { ImageManipulator, SaveFormat } from 'expo-image-manipulator';
import type React from 'react';
import { memo, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  type ImageStyle,
  type StyleProp,
  StyleSheet,
  View,
} from 'react-native';

export interface CachedImageProps extends Omit<ExpoImageProps, 'source'> {
  source: string | number | { uri: string };
  thumbnailSize?: { width: number; height: number };
  showThumbnail?: boolean;
}

const thumbnailCache = new Map<string, string>();

function getImageUri(source: CachedImageProps['source']): string | undefined {
  if (typeof source === 'string') {
    return source;
  }
  if (typeof source === 'object' && source?.uri) {
    return source.uri;
  }
  return;
}

async function createThumbnailWithCache(
  uri: string,
  size: { width: number; height: number }
): Promise<string> {
  const cacheKey = `${uri}-${size.width}x${size.height}`;

  if (thumbnailCache.has(cacheKey)) {
    return thumbnailCache.get(cacheKey)!;
  }

  try {
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: size.width, height: size.height } }],
      { compress: 0.6, format: SaveFormat.JPEG }
    );

    thumbnailCache.set(cacheKey, result.uri);
    return result.uri;
  } catch (_error) {
    return uri;
  }
}

export const CachedImage: React.FC<CachedImageProps> = memo(
  ({
    source,
    thumbnailSize = { width: 32, height: 32 },
    showThumbnail = true,
    style,
    ...rest
  }) => {
    const [thumbUri, setThumbUri] = useState<string | undefined>(undefined);
    const [loading, setLoading] = useState(true);
    const [fullLoaded, setFullLoaded] = useState(false);

    const sourceUri = useMemo(() => getImageUri(source), [source]);

    useEffect(() => {
      let isMounted = true;

      if (sourceUri && showThumbnail) {
        createThumbnailWithCache(sourceUri, thumbnailSize).then((thumb) => {
          if (isMounted) {
            setThumbUri(thumb);
          }
        });
      } else {
        setThumbUri(undefined);
      }

      return () => {
        isMounted = false;
      };
    }, [sourceUri, thumbnailSize, showThumbnail]);

    if (typeof source === 'number') {
      return <ExpoImage source={source} style={style} {...rest} />;
    }

    return (
      <View style={style as StyleProp<ImageStyle>}>
        {showThumbnail && thumbUri && !fullLoaded && (
          <ExpoImage
            blurRadius={2}
            contentFit={rest.contentFit || 'cover'}
            source={{ uri: thumbUri }}
            style={[StyleSheet.absoluteFill, style]}
          />
        )}
        <ExpoImage
          {...rest}
          onLoadEnd={() => {
            setLoading(false);
            setFullLoaded(true);
          }}
          onLoadStart={() => setLoading(true)}
          source={source}
          style={style}
        />
        {loading && (
          <View
            style={[
              StyleSheet.absoluteFill,
              { justifyContent: 'center', alignItems: 'center' },
            ]}
          >
            <ActivityIndicator />
          </View>
        )}
      </View>
    );
  }
);
