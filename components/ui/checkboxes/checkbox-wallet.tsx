import { forwardRef, memo, useCallback, useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon, Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

interface CheckboxWalletProps {
  label: React.ReactNode | string;
  initialChecked: boolean;
  onPress?: (v: boolean) => void;
  required?: boolean;
}

export const CheckboxWallet = memo(
  forwardRef<React.ComponentRef<typeof TouchableOpacity>, CheckboxWalletProps>(
    (
      { label, initialChecked = false, onPress, required }: CheckboxWalletProps,
      ref
    ) => {
      const [checked, setChecked] = useState(initialChecked);
      const { theme } = useUnistyles();

      useEffect(() => {
        setChecked(initialChecked);
      }, [initialChecked]);

      const onCheckboxPress = useCallback(() => {
        setChecked((v) => {
          onPress?.(!v);
          return !v;
        });
      }, [onPress]);

      return (
        <TouchableOpacity
          onPress={onCheckboxPress}
          ref={ref}
          style={styles.checkBoxContainer}
        >
          <View style={styles.checkBox}>
            <Icon
              color={theme.colors.grey[500]}
              size={28}
              source={checked ? 'checkbox-outline' : 'checkbox-blank-outline'}
            />
          </View>
          {typeof label === 'string' ? (
            <Text style={styles.text}>{label}</Text>
          ) : (
            label
          )}
          {required && (
            <Text style={[styles.text, { color: theme.colors.red }]}>
              {' *'}
            </Text>
          )}
        </TouchableOpacity>
      );
    }
  )
);
const styles = StyleSheet.create((theme) => ({
  checkBoxContainer: {
    flexDirection: 'row',
    paddingVertical: 10,
  },
  checkBox: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    margin: 2,
    marginRight: 10,
  },
  checkBoxLabel: {
    color: theme.colors.grey[500],
    justifyContent: 'flex-start',
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
  },
}));
