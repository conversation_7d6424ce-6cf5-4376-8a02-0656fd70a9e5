import type { SpacingProps } from '@hooks';
import type { ReactNode } from 'react';
import { Pressable, type PressableProps } from 'react-native';
import { Icon } from 'react-native-paper';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import { Box } from '../layout/box';
import { Text } from '../typography/text';

const styles = StyleSheet.create((theme) => ({
  check: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    marginLeft: theme.spacing(2),
    color: theme.colors.dark,
    flex: 1,
  },
}));

interface CheckboxProps extends PressableProps, SpacingProps {
  label: ReactNode;
  isChecked: boolean;
  onPress: () => void;
  required?: boolean;
}

export const NewCheckbox = ({
  label,
  isChecked,
  onPress,
  required,
  ...rest
}: CheckboxProps) => {
  const { theme } = useUnistyles();

  const animatedCheckStyle = useAnimatedStyle(() => ({
    backgroundColor: withTiming(
      isChecked ? theme.colors.primary[600] : 'transparent'
    ),
    borderColor: withTiming(
      isChecked ? theme.colors.primary[600] : theme.colors.grey[200]
    ),
  }));

  const animatedIconStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withTiming(isChecked ? 1 : 0) }],
  }));

  const labelWithRequired = required ? `${label} *` : label;

  return (
    <Pressable
      accessibilityLabel="checkbox"
      accessibilityRole="checkbox"
      accessibilityState={{ checked: isChecked }}
      onPress={onPress}
      {...rest}
    >
      <Box align="start" flexDirection="row">
        <Animated.View style={[styles.check, animatedCheckStyle]}>
          <Animated.View style={animatedIconStyle}>
            <Icon color={theme.colors.white} size={16} source="check" />
          </Animated.View>
        </Animated.View>
        {typeof label === 'string' ? (
          <Text style={styles.label}>{labelWithRequired}</Text>
        ) : (
          <Box style={styles.label}>{label}</Box>
        )}
      </Box>
    </Pressable>
  );
};
