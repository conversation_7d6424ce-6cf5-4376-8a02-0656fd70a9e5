import { forwardRef, memo, useCallback, useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon, Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

interface CheckboxProps {
  label: string;
  initialChecked: boolean;
  onPress?: (v: boolean) => void;
  required?: boolean;
  value?: boolean;
}

export const Checkbox = memo(
  forwardRef<View, CheckboxProps>(
    ({ label, initialChecked = false, onPress, required, value }, ref) => {
      const [checked, setChecked] = useState(initialChecked);
      const { theme } = useUnistyles();

      useEffect(() => {
        if (value !== undefined) {
          return setChecked(value);
        }
        setChecked(initialChecked);
      }, [value, initialChecked]);

      const onCheckboxPress = useCallback(() => {
        setChecked((v) => {
          onPress?.(!v);
          return !v;
        });
      }, [onPress]);

      return (
        <TouchableOpacity
          accessibilityLabel={`${value ? 'checked' : 'unchecked'}`}
          accessibilityRole="checkbox"
          accessibilityState={{ checked }}
          onPress={onCheckboxPress}
          ref={ref}
          style={styles.checkBoxContainer}
        >
          <View style={styles.checkBox}>
            <Icon
              color={theme.colors.grey[500]}
              size={28}
              source={checked ? 'checkbox-outline' : 'checkbox-blank-outline'}
            />
          </View>
          <Text style={styles.text}>{label}</Text>
          {required && (
            <Text style={[styles.text, { color: theme.colors.red }]}>
              {' *'}
            </Text>
          )}
        </TouchableOpacity>
      );
    }
  )
);

const styles = StyleSheet.create((theme) => ({
  checkBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  checkBox: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    margin: 2,
    marginRight: 10,
  },
  checkBoxLabel: {
    color: theme.colors.grey[500],
    justifyContent: 'flex-start',
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
  },
}));
