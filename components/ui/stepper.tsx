import { useTranslations } from '@hooks';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { TextInput, View } from 'react-native';
import { IconButton, Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

type StepperProps = {
  value: number | null;
  min: number;
  max: number;
  step: number;
  unit: string;
  placeholder: string;
  onChange: (newValue: number | null) => void;
};

export const Stepper = memo(
  ({
    value,
    min = 0,
    max = 9_999_999,
    step = 1,
    unit = '',
    placeholder = '0',
    onChange,
  }: StepperProps) => {
    const t = useTranslations();
    const { theme } = useUnistyles();
    const [inputValue, setInputValue] = useState(
      value !== undefined && value !== null ? value.toString() : placeholder
    );

    useEffect(() => {
      if (value !== undefined && value !== null) {
        setInputValue(value.toString());
      }
    }, [value]);

    const clamp = useCallback(
      (val: number) => Math.max(min, Math.min(max, val)),
      [min, max]
    );

    const currentValue = value ?? 0;

    const handleDecrease = useCallback(() => {
      onChange(clamp(currentValue - step));
    }, [currentValue, step, clamp, onChange]);

    const handleIncrease = useCallback(() => {
      onChange(clamp(currentValue + step));
    }, [currentValue, step, clamp, onChange]);

    const handleInputChange = useCallback(
      (text: string) => {
        const cleaned = text.replace(',', '.').replace(/[^0-9.]/g, '');
        const parts = cleaned.split('.');

        if (parts.length > 2) {
          return;
        }

        if (parts[1]?.length > 2) {
          return;
        }

        const numeric = Number.parseFloat(cleaned);

        if (Number.isNaN(numeric)) {
          setInputValue(cleaned);
          return;
        }

        if (numeric > max || numeric < min) {
          return;
        }

        onChange(numeric);
        setInputValue(cleaned);
      },
      [max, min, onChange]
    );

    const handleBlur = useCallback(() => {
      const trimmed = inputValue.trim();
      const numeric = Number.parseFloat(trimmed);

      if (trimmed === '' || Number.isNaN(numeric)) {
        setInputValue(min.toString());
        onChange(min);
        return;
      }

      if (trimmed.endsWith('.')) {
        setInputValue(numeric.toString());
        onChange(numeric);
      }
    }, [inputValue, min, onChange]);

    const unitText = useMemo(() => t(unit), [t, unit]);

    return (
      <View style={styles.container}>
        <IconButton
          containerColor={theme.colors.white}
          disabled={value !== null && value <= min}
          icon="minus"
          iconColor={theme.colors.primary[500]}
          mode="outlined"
          onPress={handleDecrease}
          size={24}
          style={styles.iconButton}
        />
        <View style={styles.inputWrapper}>
          <TextInput
            keyboardType="decimal-pad"
            onBlur={handleBlur}
            onChangeText={handleInputChange}
            placeholder={placeholder}
            style={styles.input}
            value={inputValue}
          />
          {unit ? <Text style={styles.unit}>{unitText}</Text> : null}
        </View>
        <IconButton
          containerColor={theme.colors.white}
          disabled={value !== null && value >= max}
          icon="plus"
          iconColor={theme.colors.primary[500]}
          mode="outlined"
          onPress={handleIncrease}
          size={24}
          style={styles.iconButton}
        />
      </View>
    );
  }
);

const styles = StyleSheet.create((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 58,
    height: 58,
    borderWidth: 2,
    borderRadius: 16,
    borderColor: theme.colors.primary[500],
  },
  iconButtonDisabled: {
    opacity: 0.5,
  },
  inputWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 16,
    borderColor: theme.colors.grey[100],
    position: 'relative',
    marginHorizontal: 4,
    height: 58,
  },
  input: {
    fontSize: 18,
    fontWeight: '400',
    color: theme.colors.black,
    paddingVertical: 0,
    paddingHorizontal: 0,
    width: '100%',
    textAlign: 'center',
  },
  unit: {
    position: 'absolute',
    right: 16,
    fontSize: 16,
    color: theme.colors.grey[300],
  },
}));
