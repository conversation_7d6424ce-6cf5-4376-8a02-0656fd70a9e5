import { allPropKeys, type SpacingProps, useSpacing } from 'hooks';
import type React from 'react';
import { type ElementType, forwardRef, useMemo } from 'react';
import { type StyleProp, View, type ViewStyle } from 'react-native';
import {
  StyleSheet,
  type UnistylesVariants,
  useUnistyles,
} from 'react-native-unistyles';
import { getColorFromTheme } from '../../../utils/theme-helpers';

const styles = StyleSheet.create({
  box: {
    variants: {
      flexDirection: {
        row: {
          flexDirection: 'row',
        },
        column: {
          flexDirection: 'column',
        },
        rowReverse: {
          flexDirection: 'row-reverse',
        },
        columnReverse: {
          flexDirection: 'column-reverse',
        },
        default: {
          flexDirection: 'column',
        },
      },
      align: {
        start: {
          alignItems: 'flex-start',
        },
        center: {
          alignItems: 'center',
        },
        end: {
          alignItems: 'flex-end',
        },
        stretch: {
          alignItems: 'stretch',
        },
        baseline: {
          alignItems: 'baseline',
        },
      },
      justify: {
        start: {
          justifyContent: 'flex-start',
        },
        center: {
          justifyContent: 'center',
        },
        end: {
          justifyContent: 'flex-end',
        },
        between: {
          justifyContent: 'space-between',
        },
        around: {
          justifyContent: 'space-around',
        },
        evenly: {
          justifyContent: 'space-evenly',
        },
      },
    },
  },
});

type BoxVariants = UnistylesVariants<typeof styles>;

interface BoxOwnProps extends BoxVariants, SpacingProps {
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
  bgColor?: string;
  zIndex?: number;
  as?: ElementType;
  radius?: number;
  borderColor?: string;
  borderWidth?: number;
  wrap?: boolean;
}

type BoxProps<E extends ElementType> = BoxOwnProps &
  React.ComponentPropsWithRef<E>;

const BoxComponent = forwardRef<unknown, BoxProps<ElementType>>(
  (
    {
      as: Component = View,
      style,
      children,
      flexDirection,
      align,
      justify,
      bgColor,
      zIndex,
      radius,
      borderColor,
      borderWidth,
      wrap,
      ...rest
    },
    ref
  ) => {
    const componentProps: Record<string, unknown> = {};
    const spacingProps: Record<string, unknown> = {};

    for (const key of Object.keys(rest)) {
      if (allPropKeys.includes(key as keyof SpacingProps)) {
        spacingProps[key] = rest[key as keyof typeof rest];
      } else {
        componentProps[key] = rest[key as keyof typeof rest];
      }
    }

    const spacingStyles = useSpacing(spacingProps as SpacingProps);
    const { theme } = useUnistyles();

    styles.useVariants({
      flexDirection,
      align,
      justify,
    });

    const dynamicStyles = useMemo(() => {
      const newStyles: ViewStyle = {};
      const backgroundColor = getColorFromTheme(theme.colors, bgColor);

      if (backgroundColor) {
        newStyles.backgroundColor = backgroundColor;
      }

      return newStyles;
    }, [bgColor, theme.colors]);

    return (
      <Component
        ref={ref}
        style={[
          styles.box,
          dynamicStyles,
          spacingStyles,
          style,
          {
            ...(zIndex && { zIndex }),
            ...(radius && { borderRadius: radius }),
            ...(borderColor && {
              borderColor: getColorFromTheme(theme.colors, borderColor),
            }),
            ...(borderWidth && { borderWidth }),
            ...(wrap && { flexWrap: 'wrap' }),
          },
        ]}
        {...componentProps}
      >
        {children}
      </Component>
    );
  }
);

export const Box = BoxComponent as <E extends ElementType = typeof View>(
  props: BoxProps<E>
) => React.ReactElement | null;
