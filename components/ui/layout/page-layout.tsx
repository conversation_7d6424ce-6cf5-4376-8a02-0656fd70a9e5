/** biome-ignore-all lint/performance/useTopLevelRegex: page level regex */
/** biome-ignore-all lint/complexity/noExcessiveCognitiveComplexity: complex component */

import ArrowBack from '@assets/arrow-back.svg';
import { useIsKeyboardVisible } from '@hooks';
import MaskedView from '@react-native-masked-view/masked-view';
import { usePageRefresh } from '@store/refresh.store';
import { getColorFromTheme } from '@utils/theme-helpers';
import { BlurView } from 'expo-blur';
import { usePathname, useRouter } from 'expo-router';
import React, { memo, useCallback, useRef } from 'react';
import {
  Dimensions,
  type GestureResponderEvent,
  Keyboard,
  type LayoutChangeEvent,
  type NativeScrollEvent,
  type NativeSyntheticEvent,
  RefreshControl,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import LinearGradient from 'react-native-linear-gradient';
import { ProgressBar } from 'react-native-paper';
import Animated, {
  Extrapolation,
  FadeIn,
  FadeOut,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import type { PageLayoutProps } from 'types/page-layout.types';
import { Button } from '../buttons';
import { Text, Title } from '../typography';
import { Box } from './box';

const screenWidth = Dimensions.get('window').width;

// --- Constants ---
const HEADER_HEIGHT = 108;
const ANIMATION_START_SCROLL_Y = 50; // Scroll Y value to start fading the large title
const ANIMATION_END_SCROLL_Y = 80; // Scroll Y value when the header is fully visible

export const PageLayout = memo(
  ({
    title,
    description,
    headerTitleSpacing = 4,
    headerBlurOptions,
    noPadding = false,
    contentMarginTop = 0,
    contentGap = 0,
    children,
    buttonText,
    buttonProps,
    buttonTopLabel,
    onButtonPress,
    onBackPress,
    backButtonHref,
    leftComponent,
    leftComponentContainerProps,
    rightComponent,
    rightComponentContainerProps,
    noButtonBlur = false,
    buttonBlurOptions,
    buttonBlurGradientOptions,
    contentContainerStyle,
    bgColor = 'grey.50',
    animEntering = FadeIn,
    animExiting = FadeOut,
    noAnimation = false,
    noBackButton = false,
    noHeader = false,
    withBackgroundGradient = false,
    backgroundGradientOptions,
    statusBarOptions,
    noButtonGradient = false,
    buttonGradientVariant = 'light',
    buttonGradientOptions,
    headerWithProgress = false,
    headerProgress = 0,
    noScroll = false,
    customBackgroundGradients,
    headerOverlayContent = false,
    noLargeTitle = false,
    smallTitleAlwaysVisible = false,
    headerAnimationStartY = ANIMATION_START_SCROLL_Y,
    headerAnimationEndY = ANIMATION_END_SCROLL_Y,
    disableTopOverscroll = false,
    headerTitleThresholdY,
    titleColor = 'grey.80',
    pullToRefresh = false,
    onRefresh,
    refreshIndicatorOffset = 0,
    headerLogo,
  }: PageLayoutProps) => {
    const [isScrollable, setIsScrollable] = React.useState(false);
    const layoutHeightRef = useRef(0);
    const contentHeightRef = useRef(0);
    const buttonHeightRef = useRef(0);
    const contentRef = React.useRef<View>(null);

    const { top: topInset, bottom: bottomInset } = useSafeAreaInsets();
    const scrollY = useSharedValue(0);
    const isKeyboardVisible = useIsKeyboardVisible();
    const { theme } = useUnistyles();
    const router = useRouter();
    const pathname = usePathname();

    const pageKey = pathname.replace(/^\//, '') || 'home';

    const { isRefreshing, setRefreshing, setError, clearError } =
      usePageRefresh(pageKey);

    const handleRefresh = useCallback(async () => {
      if (!(pullToRefresh && onRefresh) || isRefreshing) {
        return;
      }

      try {
        clearError();
        setRefreshing(true);
        await onRefresh(pageKey);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Refresh failed';
        setError(errorMessage);
      } finally {
        setRefreshing(false);
      }
    }, [
      pullToRefresh,
      onRefresh,
      isRefreshing,
      pageKey,
      clearError,
      setRefreshing,
      setError,
    ]);

    const checkScrollable = useCallback(() => {
      if (layoutHeightRef.current > 0) {
        const SCROLL_BUFFER = 20;
        const scrollable =
          contentHeightRef.current >
          layoutHeightRef.current -
            buttonHeightRef.current -
            SCROLL_BUFFER -
            HEADER_HEIGHT;
        setIsScrollable(scrollable);
      }
    }, []);

    const handleBackPress = useCallback(() => {
      if (backButtonHref) {
        router.push(backButtonHref);
        return;
      }

      onBackPress?.();
      setTimeout(() => {
        if (router.canGoBack()) {
          router.back();
        }
      }, 10);
    }, [onBackPress, backButtonHref, router]);

    const onContentLayout = useCallback(
      (event: LayoutChangeEvent) => {
        contentHeightRef.current = event.nativeEvent.layout.height;
        checkScrollable();
      },
      [checkScrollable]
    );

    const onLayout = useCallback(
      (event: LayoutChangeEvent) => {
        layoutHeightRef.current = event.nativeEvent.layout.height;
        checkScrollable();
      },
      [checkScrollable]
    );

    const onButtonLayout = useCallback(
      (event: LayoutChangeEvent) => {
        buttonHeightRef.current = event.nativeEvent.layout.height;
        checkScrollable();
      },
      [checkScrollable]
    );

    const onScroll = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        scrollY.value = event.nativeEvent.contentOffset.y;
      },
      [scrollY]
    );

    const handleButtonPress = useCallback(
      (e: GestureResponderEvent) => {
        onButtonPress?.(e);
        Keyboard.dismiss();
      },
      [onButtonPress]
    );

    const largeTitleAnimatedStyle = useAnimatedStyle(() => {
      const opacity = interpolate(
        scrollY.value,
        [0, headerAnimationStartY],
        [1, 0],
        Extrapolation.CLAMP
      );
      return { opacity };
    });

    const headerBackgroundAnimatedStyle = useAnimatedStyle(() => {
      const opacity = interpolate(
        scrollY.value,
        [headerAnimationStartY, headerAnimationEndY],
        [0, 1],
        Extrapolation.CLAMP
      );
      return { opacity };
    });

    const headerTitleAnimatedStyle = useAnimatedStyle(() => {
      if (smallTitleAlwaysVisible) {
        return { opacity: 1, transform: [{ translateY: 0 }] };
      }

      const thresholdY = headerTitleThresholdY ?? headerAnimationStartY;

      const titleStartY = thresholdY;
      const titleEndY = thresholdY + 30;

      const shouldShow = scrollY.value >= thresholdY;

      const opacity = shouldShow
        ? interpolate(
            scrollY.value,
            [titleStartY, titleEndY],
            [0, 1],
            Extrapolation.CLAMP
          )
        : 0;

      const translateY = interpolate(
        scrollY.value,
        [titleStartY, titleEndY],
        [15, 0],
        Extrapolation.CLAMP
      );

      return { opacity, transform: [{ translateY }] };
    });

    return (
      <Animated.View
        entering={noAnimation ? undefined : animEntering}
        exiting={noAnimation ? undefined : animExiting}
        style={styles.container}
      >
        <StatusBar
          backgroundColor="transparent"
          barStyle="dark-content"
          translucent
          {...statusBarOptions}
        />

        <SafeAreaView
          edges={['left', 'right']}
          onLayout={onLayout}
          style={[
            { flex: 1 },
            bgColor && {
              backgroundColor: getColorFromTheme(theme.colors, bgColor),
            },
          ]}
        >
          {withBackgroundGradient && (
            <LinearGradient
              colors={[theme.colors.primary[500], 'transparent']}
              locations={[0, 1]}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 0,
                opacity: 0.35,
              }}
              {...backgroundGradientOptions}
            />
          )}
          {noScroll ? (
            <View
              style={[
                styles.flex,
                {
                  paddingTop: headerOverlayContent ? 0 : HEADER_HEIGHT,
                  paddingBottom: noButtonBlur ? 100 : 150,
                },
              ]}
            >
              {noPadding ? (
                <View
                  onLayout={onContentLayout}
                  ref={contentRef}
                  style={[{ flex: 1 }, contentContainerStyle]}
                >
                  {children}
                </View>
              ) : (
                <Box
                  onLayout={onContentLayout}
                  pb={8}
                  px={4}
                  ref={contentRef}
                  style={[{ flex: 1 }, contentContainerStyle]}
                  zIndex={2}
                >
                  {title && !noLargeTitle && !smallTitleAlwaysVisible && (
                    <Animated.View style={largeTitleAnimatedStyle}>
                      <Title color="grey.800" mt={headerTitleSpacing}>
                        {title}
                      </Title>
                    </Animated.View>
                  )}
                  {description && <Text mt={2}>{description}</Text>}
                  <Box
                    gap={contentGap}
                    mt={contentMarginTop}
                    style={{ flex: 1 }}
                  >
                    {children}
                  </Box>
                </Box>
              )}
            </View>
          ) : (
            <KeyboardAwareScrollView
              bounces={!disableTopOverscroll}
              contentContainerStyle={{
                paddingTop: headerOverlayContent ? 0 : HEADER_HEIGHT,
                paddingBottom: noButtonBlur ? 100 : 150,
                flexGrow: 1,
              }}
              enableOnAndroid
              keyboardShouldPersistTaps="handled"
              onScroll={onScroll}
              overScrollMode={disableTopOverscroll ? 'never' : 'auto'}
              refreshControl={
                pullToRefresh ? (
                  <RefreshControl
                    colors={[
                      getColorFromTheme(theme.colors, 'primary.500') ||
                        '#007AFF',
                    ]}
                    onRefresh={handleRefresh}
                    progressBackgroundColor={getColorFromTheme(
                      theme.colors,
                      'grey.50'
                    )}
                    progressViewOffset={
                      (headerOverlayContent
                        ? HEADER_HEIGHT + topInset
                        : topInset) + refreshIndicatorOffset
                    } // Offset to avoid Dynamic Island/notch and custom content
                    refreshing={isRefreshing}
                    tintColor={getColorFromTheme(theme.colors, 'primary.500')}
                  />
                ) : undefined
              }
              scrollEnabled={isScrollable || pullToRefresh} // Enable scrolling for swipe-to-refresh even with short content
              showsVerticalScrollIndicator={false}
              style={styles.flex}
            >
              <Box
                onLayout={onContentLayout}
                pb={8}
                px={noPadding ? 0 : 4}
                ref={contentRef}
                style={contentContainerStyle}
                zIndex={2}
              >
                {title &&
                  !noLargeTitle &&
                  !smallTitleAlwaysVisible &&
                  !headerWithProgress && (
                    <Animated.View style={largeTitleAnimatedStyle}>
                      <Title color="grey.800" mt={headerTitleSpacing}>
                        {title}
                      </Title>
                    </Animated.View>
                  )}
                {description && <Text mt={2}>{description}</Text>}
                <Box gap={contentGap} mt={contentMarginTop}>
                  {children}
                </Box>
              </Box>
            </KeyboardAwareScrollView>
          )}

          {!noHeader && (
            <Box
              left={0}
              pos="absolute"
              right={0}
              style={{ height: HEADER_HEIGHT }}
              top={0}
              zIndex={1}
            >
              <Animated.View
                style={[StyleSheet.absoluteFill, headerBackgroundAnimatedStyle]}
              >
                <MaskedView
                  maskElement={
                    <LinearGradient
                      colors={['white', 'white', 'transparent']}
                      end={{ x: 0, y: 1 }}
                      locations={[0, 0.825, 1]}
                      start={{ x: 0, y: 0 }}
                      style={StyleSheet.absoluteFill}
                    />
                  }
                  style={StyleSheet.absoluteFill}
                >
                  <BlurView
                    intensity={40}
                    style={StyleSheet.absoluteFill}
                    tint="systemThinMaterialLight"
                    {...headerBlurOptions}
                  />
                </MaskedView>
              </Animated.View>

              <View style={[styles.headerContent, { paddingTop: topInset }]}>
                <View
                  style={[styles.headerButton, leftComponentContainerProps]}
                >
                  {leftComponent ??
                    (!noBackButton && (
                      <TouchableOpacity
                        activeOpacity={0.65}
                        onPress={handleBackPress}
                      >
                        <Box bgColor="grey.100" p={2} radius={100}>
                          <ArrowBack
                            style={{
                              width: 16,
                              height: 16,
                              tintColor: theme.colors.grey[900],
                            }}
                          />
                        </Box>
                      </TouchableOpacity>
                    ))}
                </View>
                {headerWithProgress && (
                  <Box align="center" h={20} justify="center">
                    <ProgressBar
                      color={theme.colors.primary[500]}
                      progress={headerProgress}
                      style={{
                        height: 6,
                        borderRadius: 3,
                        marginRight: 16,
                        width: screenWidth - 32 - 96,
                        backgroundColor: theme.colors.grey[100],
                      }}
                    />
                  </Box>
                )}

                {!headerWithProgress && (headerLogo || title) && (
                  <Animated.View
                    style={[
                      styles.headerTitleContainer,
                      smallTitleAlwaysVisible ? {} : headerTitleAnimatedStyle,
                    ]}
                  >
                    {headerLogo ? (
                      headerLogo
                    ) : (
                      <Title
                        color={titleColor}
                        numberOfLines={1}
                        style={{ fontSize: 18 }}
                        weight="medium"
                      >
                        {title}
                      </Title>
                    )}
                  </Animated.View>
                )}
                {(rightComponent || title) && (
                  <View
                    style={[styles.headerButton, rightComponentContainerProps]}
                  >
                    {rightComponent}
                  </View>
                )}
              </View>
            </Box>
          )}

          {/* --- Bottom Button --- */}
          {buttonText && !isKeyboardVisible && (
            <View
              onLayout={onButtonLayout}
              style={[
                styles.buttonContainer,
                {
                  paddingBottom: bottomInset > 0 ? bottomInset : 16,
                },
              ]}
            >
              {!noButtonBlur && (
                <MaskedView
                  maskElement={
                    <LinearGradient
                      colors={['transparent', 'white', 'white']}
                      end={{ x: 0, y: 1 }}
                      locations={[0, 0.3, 1]}
                      start={{ x: 0, y: 0 }}
                      style={StyleSheet.absoluteFill}
                      {...buttonBlurGradientOptions}
                    />
                  }
                  style={StyleSheet.absoluteFill}
                >
                  <BlurView
                    intensity={15}
                    style={StyleSheet.absoluteFill}
                    tint="extraLight"
                    {...buttonBlurOptions}
                  />
                </MaskedView>
              )}

              {!noButtonGradient && (
                <Box style={[StyleSheet.absoluteFill]}>
                  <LinearGradient
                    colors={
                      buttonGradientVariant === 'dark'
                        ? [
                            'transparent',
                            'rgba(19, 22, 21, 0.2)',
                            'rgba(19, 22, 21, 0.55)',
                            'rgba(19, 22, 21, 0.8)',
                            'rgba(19, 22, 21, 0.95)',
                          ]
                        : [
                            'transparent',
                            'rgba(249, 250, 250, 0.05)',
                            'rgba(249, 250, 250, 0.15)',
                            'rgba(249, 250, 250, 0.35)',
                            'rgba(249, 250, 250, 0.65)',
                            'rgba(249, 250, 250, 0.85)',
                            'rgba(249, 250, 250, 0.95)',
                          ]
                    }
                    locations={
                      buttonGradientVariant === 'dark'
                        ? [0, 0.5, 0.7, 0.85, 1]
                        : [0, 0.3, 0.5, 0.65, 0.8, 0.9, 1]
                    }
                    style={StyleSheet.absoluteFill}
                    {...buttonGradientOptions}
                  />
                </Box>
              )}

              <Box px={4} py={2}>
                {buttonTopLabel && <Box mb={2}>{buttonTopLabel}</Box>}
                <Button
                  onPress={handleButtonPress}
                  rounded="full"
                  size="lg"
                  {...buttonProps}
                >
                  {buttonText}
                </Button>
              </Box>
            </View>
          )}
        </SafeAreaView>

        {/* Custom background gradients rendered on top of everything */}
        {customBackgroundGradients}
      </Animated.View>
    );
  }
);

const styles = StyleSheet.create((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  flex: {
    flex: 1,
  },
  headerContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 12,
  },
  headerButton: {
    width: 60,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    paddingTop: 64,
    pointerEvents: 'auto',
  },
}));
