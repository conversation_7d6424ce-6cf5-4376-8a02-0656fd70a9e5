import { useTranslations } from '@hooks';
import type React from 'react';
import { useEffect, useRef } from 'react';
import { Animated, TouchableOpacity, View } from 'react-native';
import { RadioButton, Text } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

interface AccordionProps {
  id: string;
  icon?: React.ReactNode;
  titleKey: string;
  isSelected: boolean;
  onSelect: (id: string) => void;
  children?: React.ReactNode;
  withAnimation?: boolean;
}

export const Accordion = ({
  id,
  icon,
  titleKey,
  isSelected,
  onSelect,
  children,
  withAnimation = true,
}: AccordionProps) => {
  const t = useTranslations();
  const animatedHeight = useRef(new Animated.Value(0)).current;
  const { theme } = useUnistyles();

  useEffect(() => {
    if (withAnimation) {
      if (isSelected) {
        Animated.timing(animatedHeight, {
          toValue: 1,
          duration: 600,
          useNativeDriver: false,
        }).start();
      } else {
        Animated.timing(animatedHeight, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }).start();
      }
    }
  }, [isSelected, withAnimation, animatedHeight]);

  const handlePress = () => onSelect(id);

  const heightInterpolation = animatedHeight.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1000],
  });

  const contentContainer = withAnimation ? (
    <Animated.View style={[styles.content, { maxHeight: heightInterpolation }]}>
      <View style={styles.componentContainer}>{children}</View>
    </Animated.View>
  ) : (
    <View style={styles.content}>{isSelected && children}</View>
  );

  return (
    <TouchableOpacity activeOpacity={1} onPress={handlePress}>
      <View style={[styles.card, isSelected && styles.cardSelected]}>
        <View style={styles.header}>
          <View style={styles.iconTitle}>
            {icon}
            <Text style={styles.title}>{t(titleKey)}</Text>
          </View>
          <RadioButton
            onPress={handlePress}
            status={isSelected ? 'checked' : 'unchecked'}
            uncheckedColor={theme.colors.grey[300]}
            value={id}
          />
        </View>
        {contentContainer}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create((theme) => ({
  card: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.grey[100],
    backgroundColor: theme.colors.white,
    shadowColor: theme.colors.primary[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 10,
    elevation: 3,
  },
  cardSelected: {
    borderColor: theme.colors.primary[600],
  },
  content: {
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    width: '60%',
  },
  title: {
    fontSize: 18,
    fontWeight: '400',
  },
  componentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 12,
    gap: 4,
  },
}));
