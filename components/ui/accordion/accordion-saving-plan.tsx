import { useTranslations } from '@hooks';
import type React from 'react';
import { Text, View } from 'react-native';
import { Chip } from 'react-native-paper';
import { StyleSheet } from 'react-native-unistyles';
import { Alert } from '../alert';
import { Accordion } from './accordion';

type AlertType = 'success' | 'warning' | 'error' | 'info';

interface AccordionSavingPlanProps {
  id: string;
  icon: React.ReactNode;
  titleKey: string;
  descriptionKey: string;
  tagsKey?: string;
  alert?: {
    message: string;
    type: AlertType;
  };
  isSelected: boolean;
  onSelect: (id: string) => void;
  isTabSectionVisible: boolean;
  isAlertVisible: boolean;
}

export const AccordionSavingPlan = ({
  id,
  icon,
  titleKey,
  descriptionKey,
  tagsKey,
  alert,
  isSelected,
  onSelect,
  isTabSectionVisible = true,
  isAlertVisible = true,
}: AccordionSavingPlanProps) => {
  const t = useTranslations();
  const tags = tagsKey ? (t(tagsKey, { returnObjects: true }) as string[]) : [];

  return (
    <Accordion
      icon={icon}
      id={id}
      isSelected={isSelected}
      onSelect={onSelect}
      titleKey={titleKey}
      withAnimation={true}
    >
      <Text style={styles.description}>{t(descriptionKey)}</Text>
      {isTabSectionVisible && tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {tags.map((tag) => (
            <Chip
              key={tag}
              style={[
                styles.chip,
                id === 'adjusted' ? styles.chipAdjusted : styles.chipSimple,
              ]}
              textStyle={[
                styles.chipText,
                id === 'adjusted'
                  ? styles.chipTextAdjusted
                  : styles.chipTextSimple,
              ]}
            >
              {tag}
            </Chip>
          ))}
        </View>
      )}
      {isAlertVisible && alert && (
        <Alert message={alert.message} type={alert.type} />
      )}
    </Accordion>
  );
};

const styles = StyleSheet.create((theme) => ({
  description: {
    marginTop: 16,
    fontSize: 14,
    color: theme.colors.dark,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 12,
    gap: 4,
  },
  chip: {
    borderRadius: 100,
    paddingHorizontal: 6,
    paddingVertical: 3,
    marginBottom: 6,
    borderWidth: 1,
  },
  chipText: {
    fontWeight: '400',
    fontSize: 12,
  },
  chipAdjusted: {
    backgroundColor: theme.colors.primary[100],
    borderColor: theme.colors.primary[200],
  },
  chipTextAdjusted: {
    color: theme.colors.primary[800],
  },
  chipSimple: {
    backgroundColor: theme.colors.grey[50],
    borderColor: theme.colors.grey[100],
  },
  chipTextSimple: {
    color: theme.colors.dark,
  },
}));
