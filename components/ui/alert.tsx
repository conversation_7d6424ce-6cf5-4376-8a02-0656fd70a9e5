import { useTranslations } from '@hooks';
import { AlertStyles } from '@theme';
import type * as React from 'react';
import { Card, Icon, Text } from 'react-native-paper';
import { StyleSheet } from 'react-native-unistyles';

type AlertType = 'error' | 'warning' | 'info' | 'success';

interface AlertProps {
  message: string | React.ReactNode;
  type: AlertType;
}

export const Alert = ({ message, type }: AlertProps) => {
  const t = useTranslations();
  const { bgColor, borderColor, textColor, icon } = AlertStyles[type];

  return (
    <Card
      mode="outlined"
      style={{
        backgroundColor: bgColor,
        borderColor,
        borderRadius: 8,
      }}
    >
      <Card.Content style={styles.cardContent}>
        <Icon color={textColor} size={20} source={icon} />
        <Text style={[styles.text, { color: textColor }]}>
          {typeof message === 'string' ? t(message) : message}
        </Text>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create(() => ({
  cardContent: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  text: {
    flexShrink: 1,
    marginLeft: 10,
    fontSize: 14,
  },
}));
