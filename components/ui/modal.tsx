import useNotificationStore from '@store/notification.store';
import { CachedImage as Image } from 'components/ui/cached-image';
import { BlurView } from 'expo-blur';
import { useEffect } from 'react';
import { Keyboard, Modal as RNModal, View } from 'react-native';
import { Portal } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import { Button } from './buttons/button';
import { Box } from './layout/box';
import { Text } from './typography';

const GIF_DIMENSION = 90;

export const Modal = () => {
  const {
    isModalVisible,
    modalMessage,
    bottomButtonLabel,
    onBottomButtonPress,
    fontSize,
  } = useNotificationStore();
  const { theme } = useUnistyles();

  useEffect(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <Portal>
      <RNModal animationType="fade" transparent visible={isModalVisible}>
        <BlurView intensity={52} style={StyleSheet.absoluteFill} tint="light">
          <View style={styles.modalContent}>
            <Image
              contentFit="contain"
              source={require('@assets/reti-loading.gif')}
              style={styles.gif}
            />
            {modalMessage && (
              <Text
                size={fontSize}
                style={{ color: theme.colors.primary[600] }}
                textAlign="center"
              >
                {modalMessage}
              </Text>
            )}
          </View>

          {bottomButtonLabel && onBottomButtonPress ? (
            <View style={styles.bottomContainer}>
              <Box pb={8} px={4} style={styles.bottomInner}>
                <Button onPress={onBottomButtonPress} rounded="full" size="lg">
                  {bottomButtonLabel}
                </Button>
              </Box>
            </View>
          ) : null}
        </BlurView>
      </RNModal>
    </Portal>
  );
};

const styles = StyleSheet.create(() => ({
  modalContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  gif: {
    width: GIF_DIMENSION,
    height: GIF_DIMENSION,
  },
  bottomContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    paddingTop: 64,
  },
  bottomInner: {
    // Matches PageLayout bottom button padding/margins
  },
}));
