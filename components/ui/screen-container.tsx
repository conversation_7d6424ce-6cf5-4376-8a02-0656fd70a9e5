import type React from 'react';
import { useCallback } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  type ViewStyle,
} from 'react-native';
import Animated, { FadeOut, SlideInRight } from 'react-native-reanimated';
import type { BaseAnimationBuilder } from 'react-native-reanimated/src/layoutReanimation/animationBuilder/BaseAnimationBuilder';
import type { ReanimatedKeyframe } from 'react-native-reanimated/src/layoutReanimation/animationBuilder/Keyframe';
import { StyleSheet } from 'react-native-unistyles';

interface ScreenContainerProps {
  children: React.ReactNode;
  styles?: ViewStyle;
  entering?:
    | BaseAnimationBuilder
    | typeof BaseAnimationBuilder
    | ReanimatedKeyframe;
}

export const ScreenContainer = ({
  children,
  styles,
  entering,
}: ScreenContainerProps) => {
  const onPress = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styleComponent.container}
    >
      <TouchableWithoutFeedback onPress={onPress}>
        <Animated.View
          entering={entering ?? SlideInRight}
          exiting={FadeOut}
          style={[styleComponent.containerChildren, styles]}
        >
          {children}
        </Animated.View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styleComponent = StyleSheet.create((theme) => ({
  container: {
    flex: 1,
  },
  containerChildren: {
    flex: 1,
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.padding(4),
  },
}));
