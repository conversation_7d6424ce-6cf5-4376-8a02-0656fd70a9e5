import { useTranslations } from '@hooks';
import { Text, View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

type Props = {
  label?: string;
};

export const Divider = ({ label }: Props) => {
  const t = useTranslations();

  return (
    <View style={styles.container}>
      <View style={styles.divider} />
      {label && (
        <View style={styles.label}>
          <Text style={styles.labelText}>{t(label)}</Text>
        </View>
      )}
      <View style={styles.divider} />
    </View>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    marginHorizontal: 10,
  },
  labelText: {
    color: theme.colors.grey[500],
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.grey[100],
    flex: 3,
  },
}));
