import type React from 'react';
import { useEffect, useRef } from 'react';
import { Animated, type ViewStyle } from 'react-native';

interface SkeletonProps {
  width?: number | `${number}%` | 'auto';
  height?: number;
  style?: ViewStyle;
  borderRadius?: number;
  backgroundColor?: string;
  animationDuration?: number;
}

const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  style = {},
  borderRadius = 8,
  backgroundColor = '#E0E0E0',
  animationDuration = 600,
}) => {
  const opacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.5,
          duration: animationDuration,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();
    return () => pulse.stop();
  }, [opacity, animationDuration]);

  let animatedWidth: number | `${number}%` | 'auto';
  if (typeof width === 'string' && width.endsWith('%')) {
    animatedWidth = width as `${number}%`;
  } else if (width === 'auto') {
    animatedWidth = 'auto';
  } else {
    animatedWidth = width as number;
  }

  return (
    <Animated.View
      style={[
        {
          width: animatedWidth,
          height,
          backgroundColor,
          borderRadius,
          marginVertical: 4,
          overflow: 'hidden',
          opacity,
        },
        style,
      ]}
    />
  );
};

export default Skeleton;
