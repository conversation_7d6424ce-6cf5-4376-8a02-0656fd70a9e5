import type React from 'react';
import { Box } from '../layout/box';
import { Headline } from './headline';
import { Text } from './text';

interface UiFeatureSectionProps {
  icon: string;
  headline: string;
  children?: React.ReactNode;
}

export const FeatureSection = ({
  icon,
  headline,
  children,
}: UiFeatureSectionProps) => {
  return (
    <Box gap={0.5}>
      <Headline headline={headline} icon={icon} textColor="white" />
      <Text color="grey.200" ml={6}>
        {children}
      </Text>
    </Box>
  );
};
