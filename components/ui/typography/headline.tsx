import { useTranslations } from '@hooks';
import { Icon } from 'react-native-paper';
import { Box } from '../layout/box';
import { Text } from './text';

interface HeadlineProps {
  icon: string;
  headline: string;
  textColor: string;
}

export const Headline = ({ icon, headline, textColor }: HeadlineProps) => {
  const t = useTranslations();

  return (
    <Box align="center" flexDirection="row" gap={1}>
      <Icon color={textColor} size={20} source={icon} />
      <Text color={textColor} weight="semibold">
        {t(headline)}
      </Text>
    </Box>
  );
};
