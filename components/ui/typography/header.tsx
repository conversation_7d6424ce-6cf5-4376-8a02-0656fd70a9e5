import { useTranslations } from '@hooks';
import type { TextStyle } from 'react-native';
import { Text } from './text';

interface HeaderProps {
  translatedText: string;
  translate?: boolean;
  style?: TextStyle;
}

export const Header = ({ translatedText, style }: HeaderProps) => {
  const t = useTranslations();
  return (
    <Text
      style={[
        {
          marginTop: 25,
          fontSize: 32,
          lineHeight: 37,
          marginBottom: 40,
          textAlign: 'center',
        },
        style,
      ]}
    >
      {t(translatedText)}
    </Text>
  );
};
