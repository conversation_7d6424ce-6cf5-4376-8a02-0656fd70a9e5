import { Text, type TextProps, type TextVariants } from './text';

export type TitleSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
const sizeMap: Record<TitleSize, NonNullable<TextVariants['size']>> = {
  xs: 'h5',
  sm: 'h4',
  md: 'h3',
  lg: 'h2',
  xl: 'h1',
};

interface TitleProps extends Omit<TextProps, 'size'> {
  size?: TitleSize;
}

export const Title = ({ size = 'md', ...props }: TitleProps) => {
  return <Text size={sizeMap[size]} {...props} />;
};
