import { type SpacingProps, useSpacing } from '@hooks';
import { memo, useMemo } from 'react';
import {
  Text as RNText,
  type TextProps as RNTextProps,
  type TextStyle,
} from 'react-native';
import {
  StyleSheet,
  type UnistylesVariants,
  useUnistyles,
} from 'react-native-unistyles';
import { getColorFromTheme } from '../../../utils/theme-helpers';

const styles = StyleSheet.create((theme) => ({
  text: {
    fontFamily: 'Figtree',
    fontStyle: 'normal',
    color: theme.colors.dark,
    variants: {
      weight: {
        default: {
          fontWeight: '400',
        },
        regular: {
          fontWeight: '400',
        },
        medium: {
          fontWeight: '500',
        },
        semibold: {
          fontWeight: '600',
        },
        bold: {
          fontWeight: '700',
        },
      },
      size: {
        default: {
          fontSize: 16,
          lineHeight: 24,
        },
        xxs: {
          fontSize: 11,
          lineHeight: 14,
        },
        xs: {
          fontSize: 12,
          lineHeight: 16,
        },
        sm: {
          fontSize: 14,
          lineHeight: 20,
        },
        md: {
          fontSize: 16,
          lineHeight: 24,
        },
        lg: {
          fontSize: 18,
          lineHeight: 28,
        },
        xl: {
          fontSize: 20,
          lineHeight: 32,
        },
        xxl: {
          fontSize: 22,
          lineHeight: 36,
        },
        xxxl: {
          fontSize: 24,
          lineHeight: 32,
        },
        h1: {
          fontSize: 40,
          lineHeight: 48,
        },
        h2: {
          fontSize: 36,
          lineHeight: 44,
        },
        h3: {
          fontSize: 32,
          lineHeight: 40,
        },
        h4: {
          fontSize: 30,
          lineHeight: 38,
        },
        h5: {
          fontSize: 28,
          lineHeight: 36,
        },
      },
    },
  },
}));

export type TextVariants = UnistylesVariants<typeof styles>;

export interface TextProps extends RNTextProps, TextVariants, SpacingProps {
  style?: TextStyle | (TextStyle | undefined)[];
  children?: React.ReactNode;
  textAlign?: 'left' | 'center' | 'right';
  color?: string;
}

export const Text = memo(
  ({
    style,
    children,
    weight,
    color,
    size,
    textAlign,
    ...props
  }: TextProps) => {
    const spacingStyles = useSpacing(props);
    const { theme } = useUnistyles();

    styles.useVariants({
      weight,
      size,
    });

    const dynamicStyles = useMemo(() => {
      const newStyles: TextStyle = {};
      const customColor = getColorFromTheme(theme.colors, color);

      if (customColor) {
        newStyles.color = customColor;
      }
      if (textAlign) {
        newStyles.textAlign = textAlign;
      }

      return newStyles;
    }, [color, textAlign, theme.colors]);

    return (
      <RNText
        style={[styles.text, spacingStyles, dynamicStyles, style]}
        {...props}
      >
        {children}
      </RNText>
    );
  }
);
