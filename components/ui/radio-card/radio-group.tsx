import type React from 'react';
import { createContext, useContext } from 'react';
import { Box } from '../layout/box';

interface RadioGroupContextProps {
  value?: string | null;
  onValueChange: (value: string) => void;
}

const RadioGroupContext = createContext<RadioGroupContextProps | undefined>(
  undefined
);

export const useRadioGroup = () => {
  const context = useContext(RadioGroupContext);
  if (!context) {
    throw new Error('useRadioGroup must be used within a RadioGroup');
  }
  return context;
};

interface RadioGroupProps {
  value?: string | null;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
}

export const RadioGroup = ({
  value,
  onValueChange,
  children,
}: RadioGroupProps) => {
  return (
    <RadioGroupContext.Provider value={{ value, onValueChange }}>
      <Box accessibilityRole="radiogroup" gap={4}>
        {children}
      </Box>
    </RadioGroupContext.Provider>
  );
};
