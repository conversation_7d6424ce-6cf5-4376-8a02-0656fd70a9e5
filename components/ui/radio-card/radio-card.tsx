import type React from 'react';
import { Pressable } from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import { CachedImage, type CachedImageProps } from '../cached-image';
import { Box } from '../layout/box';
import { Text, type TextProps } from '../typography/text';
import { useRadioGroup } from './radio-group';

interface RadioCardProps {
  value: string;
  icon: string | undefined | null;
  title: string;
  children: React.ReactNode;
  notSelectedContent?: React.ReactNode;
  titleProps?: TextProps;
  iconProps?: CachedImageProps;
  disabled?: boolean;
}

export const RadioCard = ({
  value,
  icon,
  title,
  children,
  notSelectedContent,
  titleProps,
  iconProps,
  disabled,
}: RadioCardProps) => {
  const { value: selectedValue, onValueChange } = useRadioGroup();
  const { theme } = useUnistyles();
  const isSelected = selectedValue === value;

  const progress = useSharedValue(0);

  progress.value = withTiming(isSelected ? 1 : 0);

  const animatedRadioStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: progress.value }],
    };
  });

  const animatedContainerStyle = useAnimatedStyle(() => {
    const borderColor = isSelected ? theme.colors.primary[600] : 'transparent';
    const borderWidth = 1;
    return {
      borderColor: withTiming(borderColor),
      borderWidth: withTiming(borderWidth),
    };
  });

  return (
    <Pressable
      accessibilityRole="radio"
      accessibilityState={{ checked: isSelected }}
      disabled={disabled}
      onPress={() => onValueChange(value)}
    >
      <Animated.View
        style={[
          styles.container,
          disabled && styles.containerDisabled,
          animatedContainerStyle,
        ]}
      >
        <Box align="center" flexDirection="row" justify="between" p={4}>
          <Box
            align="center"
            flexDirection="row"
            gap={4}
            style={{ flexShrink: 1, marginRight: 16 }}
          >
            {icon && (
              <CachedImage
                source={icon}
                style={{ width: 56, height: 56 }}
                {...iconProps}
              />
            )}
            <Text pr={16} {...titleProps}>
              {title}
            </Text>
          </Box>
          <Box
            align="center"
            bgColor={isSelected ? 'primary.100' : 'white'}
            h={24}
            justify="center"
            style={{
              borderRadius: 12,
              borderWidth: 1,
              borderColor: isSelected
                ? theme.colors.primary[500]
                : theme.colors.grey[100],
            }}
            w={24}
          >
            <Animated.View
              style={[
                styles.radioIndicator,
                { backgroundColor: theme.colors.primary[600] },
                animatedRadioStyle,
              ]}
            />
          </Box>
        </Box>
        {isSelected && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <Box p={4} pt={0}>
              {children}
            </Box>
          </Animated.View>
        )}
        {!isSelected && notSelectedContent && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <Box p={4} pt={0}>
              {notSelectedContent}
            </Box>
          </Animated.View>
        )}
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: theme.colors.white,
    boxShadow: '0px 4px 24px -8px rgba(13, 61, 50, 0.10)',
  },
  containerDisabled: {
    opacity: 0.5,
  },
  radioIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
}));
