import { ProgressBar } from 'react-native-paper';
import { StyleSheet } from 'react-native-unistyles';
import { Box } from '../layout/box';

interface HeaderProgressProps {
  progress: number;
  color: string;
}

export const HeaderProgress = ({ progress, color }: HeaderProgressProps) => (
  <Box h={20} justify="center" w="100%">
    <ProgressBar color={color} progress={progress} style={styles.bar} />
  </Box>
);

const styles = StyleSheet.create({
  bar: {
    height: 6,
    borderRadius: 3,
  },
});
