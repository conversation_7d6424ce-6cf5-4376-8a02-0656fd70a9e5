import { StyleSheet } from 'react-native-unistyles';

export const styles = StyleSheet.create((theme) => ({
  container: {
    marginBottom: theme.gap(4),
  },
  label: {
    marginBottom: theme.gap(2),
    fontSize: 14,
    color: theme.colors.dark,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 58,
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: theme.padding(4),
    borderColor: theme.colors.grey[100],
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 18,
    color: theme.colors.dark,
  },
  eyeIcon: {
    width: 24,
    height: 24,
    color: theme.colors.grey[700],
  },
  errorMessage: {
    marginTop: theme.gap(1),
    fontSize: 14,
    color: theme.colors.red,
    height: 17,
  },
}));
