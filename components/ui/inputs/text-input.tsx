import type React from 'react';
import { forwardRef, useState } from 'react';
import {
  TextInput as RNTextInput,
  type TextInputProps as RNTextInputProps,
  TouchableOpacity,
  type ViewStyle,
} from 'react-native';
import { useUnistyles } from 'react-native-unistyles';
import EyeIcon from '../../../assets/eye-icon-password.svg';
import { Box } from '../layout/box';
import { Text } from '../typography';
import { styles } from './text-input.styles';

interface TextInputProps extends RNTextInputProps {
  label: string;
  errorMessage?: string;
  isPassword?: boolean;
  render?: (props: RNTextInputProps) => React.ReactNode;
  inputContainerStyle?: ViewStyle;
}

export const TextInput = forwardRef<RNTextInput, TextInputProps>(
  (
    {
      label,
      errorMessage,
      isPassword = false,
      render,
      inputContainerStyle,
      ...props
    },
    ref
  ) => {
    const { theme } = useUnistyles();
    const [isPasswordVisible, setIsPasswordVisible] = useState(!isPassword);

    const togglePasswordVisibility = () => {
      setIsPasswordVisible((prev) => !prev);
    };

    return (
      <Box gap={1}>
        <Text size="sm">{label}</Text>
        <Box
          align="center"
          borderColor="grey.100"
          borderWidth={1}
          flexDirection="row"
          h={58}
          px={4}
          radius={16}
          style={inputContainerStyle}
        >
          {render ? (
            render(props)
          ) : (
            <RNTextInput
              keyboardType={isPassword ? 'ascii-capable' : undefined}
              placeholderTextColor={theme.colors.grey[100]}
              ref={ref}
              secureTextEntry={!isPasswordVisible}
              selectTextOnFocus={false}
              style={styles.input}
              {...props}
            />
          )}

          {isPassword && (
            <TouchableOpacity
              activeOpacity={0.6}
              onPressIn={togglePasswordVisibility}
            >
              <EyeIcon style={styles.eyeIcon} />
            </TouchableOpacity>
          )}
        </Box>
        {errorMessage && (
          <Text style={styles.errorMessage}>{errorMessage}</Text>
        )}
      </Box>
    );
  }
);

export type TextInputType = RNTextInput;
