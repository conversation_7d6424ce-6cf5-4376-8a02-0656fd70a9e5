import { memo, useCallback, useEffect, useRef, useState } from 'react';
import {
  type NativeSyntheticEvent,
  TextInput,
  type TextInputKeyPressEventData,
  TouchableOpacity,
} from 'react-native';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';
import type { PinInputProps } from 'types/authentication.types';
import { Box } from '../layout/box';
import { Text } from '../typography/text';

const PIN_LENGTH = 6;

export const PinInput = memo(
  ({
    value,
    onChange,
    onComplete,
    disabled = false,
    hasError = false,
    errorMessage,
    autoFocus = true,
    clearOnError = true,
    style,
  }: PinInputProps) => {
    const { theme } = useUnistyles();
    const [focusedIndex, setFocusedIndex] = useState<number>(
      autoFocus ? 0 : -1
    );
    const inputRefs = useRef<(TextInput | null)[]>([]);

    const digits = value.padEnd(PIN_LENGTH, '').split('').slice(0, PIN_LENGTH);

    useEffect(() => {
      if (hasError && clearOnError) {
        onChange('');
        setFocusedIndex(0);
        inputRefs.current[0]?.focus();
      }
    }, [hasError, clearOnError, onChange]);

    useEffect(() => {
      if (autoFocus && !disabled) {
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
      }
    }, [autoFocus, disabled]);

    const handleDigitChange = useCallback(
      (digit: string, index: number) => {
        if (disabled) return;

        const sanitizedDigit = digit.replace(/[^0-9]/g, '').slice(-1);

        const newDigits = [...digits];
        newDigits[index] = sanitizedDigit;
        const newValue = newDigits.join('').replace(/\s/g, '');

        onChange(newValue);

        if (sanitizedDigit && index < PIN_LENGTH - 1) {
          inputRefs.current[index + 1]?.focus();
          setFocusedIndex(index + 1);
        }

        if (newValue.length === PIN_LENGTH) {
          onComplete?.(newValue);
        }
      },
      [digits, disabled, onChange, onComplete]
    );

    const handleKeyPress = useCallback(
      (e: NativeSyntheticEvent<TextInputKeyPressEventData>, index: number) => {
        if (disabled) return;

        if (e.nativeEvent.key === 'Backspace' && !digits[index] && index > 0) {
          inputRefs.current[index - 1]?.focus();
          setFocusedIndex(index - 1);
        }
      },
      [digits, disabled]
    );

    const handleFocus = useCallback(
      (index: number) => {
        if (disabled) return;
        setFocusedIndex(index);
      },
      [disabled]
    );

    const handleBlur = useCallback(() => {
      setFocusedIndex(-1);
    }, []);

    const handleInputPress = useCallback(
      (index: number) => {
        if (disabled) return;
        inputRefs.current[index]?.focus();
      },
      [disabled]
    );

    const setInputRef = useCallback((ref: TextInput | null, index: number) => {
      inputRefs.current[index] = ref;
    }, []);

    return (
      <Box style={style}>
        <Box flexDirection="row" gap={3} justify="center">
          {Array.from({ length: PIN_LENGTH }, (_, index) => (
            <TouchableOpacity
              activeOpacity={disabled ? 1 : 0.7}
              key={index}
              onPress={() => handleInputPress(index)}
              style={[
                styles.inputContainer,
                {
                  borderColor: hasError
                    ? theme.colors.red
                    : focusedIndex === index
                      ? theme.colors.primary[500]
                      : theme.colors.grey[200],
                  backgroundColor: disabled
                    ? theme.colors.grey[50]
                    : theme.colors.white,
                },
              ]}
            >
              <TextInput
                autoComplete="off"
                autoCorrect={false}
                contextMenuHidden
                editable={!disabled}
                keyboardType="number-pad"
                maxLength={1}
                onBlur={handleBlur}
                onChangeText={(text) => handleDigitChange(text, index)}
                onFocus={() => handleFocus(index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                ref={(ref) => setInputRef(ref, index)}
                secureTextEntry
                selectTextOnFocus
                spellCheck={false}
                style={[
                  styles.input,
                  {
                    color: hasError
                      ? theme.colors.red
                      : disabled
                        ? theme.colors.grey[400]
                        : theme.colors.dark,
                  },
                ]}
                textAlign="center"
                value={digits[index] || ''}
              />
            </TouchableOpacity>
          ))}
        </Box>

        {errorMessage && (
          <Box mt={3}>
            <Text color="red" size="sm" textAlign="center">
              {errorMessage}
            </Text>
          </Box>
        )}
      </Box>
    );
  }
);

PinInput.displayName = 'PinInput';

const styles = StyleSheet.create((theme) => ({
  inputContainer: {
    width: 48,
    height: 56,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
  },
  input: {
    fontSize: 24,
    fontWeight: '600',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    padding: 0,
  },
}));
