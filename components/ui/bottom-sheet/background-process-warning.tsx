import { useTranslations } from '@hooks';
import { BottomSheet, Box, Button } from '@ui';
import type { ReactNode } from 'react';
import { memo, useCallback } from 'react';

export interface BackgroundProcessWarningProps {
  isVisible: boolean;
  onConfirm: () => void;
  onDismiss: () => void;
  title?: string;
  description?: string | ReactNode;
  confirmLabel?: string;
}

export const BackgroundProcessWarningBottomSheet = memo(
  ({
    isVisible,
    onConfirm,
    onDismiss,
    title,
    description,
    confirmLabel,
  }: BackgroundProcessWarningProps) => {
    const tCommon = useTranslations('common');
    const tWallet = useTranslations('wallet');

    const handleConfirm = useCallback(() => {
      onConfirm();
      onDismiss();
    }, [onConfirm, onDismiss]);

    return (
      <BottomSheet
        autoHeight
        contentGap={12}
        description={
          typeof description === 'string'
            ? description
            : (description as any) ||
              tWallet('creationOverlay.backgroundWarning')
        }
        enablePanDownToClose={false}
        isVisible={isVisible}
        onDismiss={onDismiss}
        showHeader
        title={title || tWallet('creationOverlay.backgroundWarningTitle')}
      >
        <Box>
          <Button onPress={handleConfirm} rounded="full" size="lg">
            {confirmLabel || tCommon('common.ok')}
          </Button>
        </Box>
      </BottomSheet>
    );
  }
);
