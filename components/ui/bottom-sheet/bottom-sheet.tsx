/**
 * Reusable Bottom Sheet Component
 *
 * A flexible, reusable bottom sheet modal component that provides:
 * - Universal keyboard management with automatic positioning
 * - Consistent styling patterns used across the app
 * - Flexible content area accepting any React children
 * - Proper modal lifecycle with present/dismiss operations
 * - Customizable behavior (snap points, backdrop dismissal, etc.)
 *
 * Usage:
 * ```tsx
 * <BottomSheet
 *   isVisible={isVisible}
 *   onDismiss={handleDismiss}
 *   title="Modal Title"
 *   description="Modal description"
 * >
 *   <YourCustomContent />
 * </BottomSheet>
 * ```
 */

import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { Box, Button, CustomBackdrop, Text, Title } from '@ui';
import type { ReactNode } from 'react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Image,
  Keyboard,
  Platform,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

/**
 * Props for the BottomSheet component
 */
export interface BottomSheetProps {
  /** Whether the bottom sheet is visible */
  isVisible: boolean;
  /** Callback when the bottom sheet is dismissed */
  onDismiss: () => void;
  /** Optional title displayed at the top */
  title?: string;
  /** Optional description displayed below the title */
  description?: string;
  /** Content to display in the bottom sheet */
  children: ReactNode;
  /** Whether to enable pan down to close */
  enablePanDownToClose?: boolean;
  /** Whether to enable dynamic sizing */
  enableDynamicSizing?: boolean;
  /** Enable keyboard-aware snap point recalculations */
  enableKeyboardAwareSnapPoints?: boolean;
  /** Custom snap points (defaults to responsive keyboard-aware points) */
  snapPoints?: string[];
  /** Whether to show the handle indicator */
  showHandleIndicator?: boolean;
  /** Whether to animate on mount */
  animateOnMount?: boolean;
  /** Custom keyboard behavior */
  keyboardBehavior?: 'extend' | 'fillParent' | 'interactive';
  /** Custom keyboard blur behavior */
  keyboardBlurBehavior?: 'none' | 'restore';
  /** Whether to show header section with title/description */
  showHeader?: boolean;
  /** Custom content container style */
  contentContainerStyle?: StyleProp<ViewStyle>;
  /** Custom background style */
  backgroundStyle?: StyleProp<ViewStyle>;
  /** Minimum height for the content container */
  minHeight?: number;
  /** Whether content should be centered */
  centerContent?: boolean;
  /** Gap between header and content */
  contentGap?: number;
  /** Custom padding for content container */
  contentPadding?: number;
  /** Auto height to fit content (uses dynamic sizing) */
  autoHeight?: boolean;
  /** Optional image (ReactNode or URI string) */
  image?: ReactNode | string;
  /** Image size when image is provided */
  imageSize?: { width: number; height: number };
  /** Optional primary button text */
  buttonText?: string;
  /** Optional primary button press handler */
  onButtonPress?: () => void;
  /** Additional props for the primary button */
  buttonProps?: Record<string, unknown>;
}

/**
 * Keyboard management hook for bottom sheet positioning
 */
const useKeyboardManagement = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  return { keyboardHeight, isKeyboardVisible };
};

/**
 * Reusable Bottom Sheet Component
 */
export const BottomSheet = memo(
  ({
    isVisible,
    onDismiss,
    title,
    description,
    children,
    enablePanDownToClose = true,
    enableDynamicSizing = false,
    snapPoints: customSnapPoints,
    showHandleIndicator = false,
    animateOnMount = true,
    keyboardBehavior = 'extend',
    keyboardBlurBehavior = 'restore',
    showHeader = true,
    contentContainerStyle,
    backgroundStyle,
    minHeight = 400,
    centerContent = false,
    contentGap = 16,
    contentPadding = 24,
    enableKeyboardAwareSnapPoints = true,
    autoHeight = true,
    image,
    imageSize = { width: 72, height: 72 },
    buttonText,
    onButtonPress,
    buttonProps,
  }: BottomSheetProps) => {
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const wasVisibleRef = useRef(false);
    const { keyboardHeight, isKeyboardVisible } = useKeyboardManagement();

    // Calculate responsive snap points based on keyboard state
    const snapPoints = useMemo(() => {
      if (customSnapPoints) {
        return customSnapPoints;
      }

      if (
        enableKeyboardAwareSnapPoints &&
        isKeyboardVisible &&
        keyboardHeight > 0
      ) {
        const screenHeight = Platform.select({ ios: 800, android: 600 }) || 700;
        const keyboardPercentage = (keyboardHeight / screenHeight) * 100;
        const requiredPercentage = Math.min(keyboardPercentage + 45, 95);
        return [`${requiredPercentage}%`];
      }
      return ['60%', '80%'];
    }, [
      enableKeyboardAwareSnapPoints,
      isKeyboardVisible,
      keyboardHeight,
      customSnapPoints,
    ]);

    // Handle sheet changes and dismissal
    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1) {
          onDismiss();
        }
      },
      [onDismiss]
    );

    // Handle modal lifecycle
    useEffect(() => {
      if (isVisible) {
        bottomSheetModalRef.current?.present();
        if (!wasVisibleRef.current) {
          wasVisibleRef.current = true;
        }
      } else {
        bottomSheetModalRef.current?.dismiss();
        if (wasVisibleRef.current) {
          wasVisibleRef.current = false;
        }
      }
    }, [isVisible]);

    // Handle keyboard-aware snap point adjustments
    useEffect(() => {
      if (!enableKeyboardAwareSnapPoints) return;
      if (isVisible && bottomSheetModalRef.current) {
        const timeout = setTimeout(
          () => {
            bottomSheetModalRef.current?.snapToIndex(0);
          },
          Platform.OS === 'ios' ? 0 : 100
        );

        return () => clearTimeout(timeout);
      }
    }, [enableKeyboardAwareSnapPoints, isVisible, isKeyboardVisible]);

    // Determine dynamic sizing based on keyboard
    const dynamicEnableDynamicSizing = useMemo(() => {
      if (!enableKeyboardAwareSnapPoints) {
        return autoHeight ? true : enableDynamicSizing;
      }
      // When keyboard is visible, disable dynamic sizing to allow explicit snapPoints
      if (isKeyboardVisible) return false;
      return autoHeight ? true : enableDynamicSizing;
    }, [
      enableKeyboardAwareSnapPoints,
      isKeyboardVisible,
      autoHeight,
      enableDynamicSizing,
    ]);

    return (
      <BottomSheetModal
        animateOnMount={animateOnMount}
        backdropComponent={CustomBackdrop}
        backgroundStyle={[styles.modalBackground, backgroundStyle]}
        enableDynamicSizing={dynamicEnableDynamicSizing}
        enablePanDownToClose={enablePanDownToClose}
        handleIndicatorStyle={
          showHandleIndicator ? undefined : { display: 'none' }
        }
        index={0}
        keyboardBehavior={keyboardBehavior}
        keyboardBlurBehavior={keyboardBlurBehavior}
        onChange={handleSheetChanges}
        ref={bottomSheetModalRef}
        snapPoints={dynamicEnableDynamicSizing ? undefined : snapPoints}
      >
        <BottomSheetView
          style={[
            styles.contentContainer,
            {
              padding: contentPadding,
              gap: contentGap,
              ...(autoHeight ? {} : { minHeight }),
              justifyContent: centerContent ? 'center' : 'space-between',
              paddingBottom:
                (contentPadding ?? 0) +
                (enableKeyboardAwareSnapPoints && isKeyboardVisible
                  ? keyboardHeight + 16
                  : 0),
            },
            contentContainerStyle,
          ]}
        >
          {(showHeader && (title || description)) || image || buttonText ? (
            <Box align="center" gap={4} mb={8}>
              {image &&
                (typeof image === 'string' ? (
                  <Image
                    source={{ uri: image }}
                    style={{ width: imageSize.width, height: imageSize.height }}
                  />
                ) : (
                  image
                ))}

              {showHeader && title && (
                <Title size="lg" textAlign="center">
                  {title}
                </Title>
              )}
              {showHeader && description && (
                <Text color="grey.600" textAlign="center">
                  {description}
                </Text>
              )}
            </Box>
          ) : null}

          {children}

          {buttonText && (
            <Button onPress={onButtonPress} {...(buttonProps as any)}>
              {buttonText}
            </Button>
          )}
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

BottomSheet.displayName = 'BottomSheet';

const styles = StyleSheet.create(() => ({
  modalBackground: {
    borderRadius: 24,
  },
  contentContainer: {
    flex: 1,
  },
}));
