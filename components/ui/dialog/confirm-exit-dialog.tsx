import SvgLogGreen from '@assets/logo-reti-green.svg';
import { useTranslations } from '@hooks';
import { BottomSheet } from '@ui';
import { Button } from '../buttons/button';
import { Box } from '../layout/box';

type ConfirmExitDialogProps = {
  visible: boolean;
  onConfirm: () => void;
  onDismiss: () => void;
  title?: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
};

export const ConfirmExitDialog = ({
  visible,
  onConfirm,
  onDismiss,
  title,
  description,
  confirmLabel,
  cancelLabel,
}: ConfirmExitDialogProps) => {
  const t = useTranslations();

  return (
    <BottomSheet
      description={description || t('exitDialog.description')}
      enablePanDownToClose
      image={<SvgLogGreen height={58} width={58} />}
      imageSize={{ width: 58, height: 58 }}
      isVisible={visible}
      onDismiss={onDismiss}
      title={title || t('exitDialog.title')}
    >
      <Box flexDirection="row" gap={2} justify="between">
        <Box flex={1}>
          <Button onPress={onDismiss} variant="outlined">
            {cancelLabel || 'common.no'}
          </Button>
        </Box>
        <Box flex={1}>
          <Button onPress={onConfirm}>{confirmLabel || 'common.yes'}</Button>
        </Box>
      </Box>
    </BottomSheet>
  );
};
