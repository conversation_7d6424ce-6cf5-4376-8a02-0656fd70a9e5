import { useTranslations } from '@hooks';
import { Button, type ButtonProps } from '../buttons/button';
import { Box } from '../layout/box';
import { ModalDialog } from './dialog';

export interface ConfirmationDialogProps {
  visible: boolean;
  title?: string;
  message?: string | React.ReactNode;
  onConfirm: () => void;
  onCancel: () => void;
  confirmLabel?: string;
  cancelLabel?: string;
  showLogo?: boolean;
  /** When provided, controls dialog height (false fits content; true = 60%; string supports percentage or absolute) */
  fixedHeight?: boolean | string;
  confirmButtonVariant?: ButtonProps['variant'];
  cancelButtonVariant?: ButtonProps['variant'];
}

export const ConfirmationDialog = ({
  visible,
  title,
  message,
  onConfirm,
  onCancel,
  confirmLabel,
  cancelLabel,
  showLogo = true,
  fixedHeight = false,
  confirmButtonVariant,
  cancelButtonVariant,
}: ConfirmationDialogProps) => {
  const t = useTranslations('common');

  return (
    <ModalDialog
      animationType="fade"
      description={typeof message === 'string' ? message : undefined}
      fixedHeight={fixedHeight}
      onDismiss={onCancel}
      showLogo={showLogo}
      title={title}
      visible={visible}
    >
      {typeof message !== 'string' ? <Box>{message}</Box> : null}

      <Box flexDirection="row" gap={2}>
        <Box flex={1}>
          <Button
            onPress={onCancel}
            variant={cancelButtonVariant || 'outlined'}
          >
            {cancelLabel || t('common.cancel')}
          </Button>
        </Box>
        <Box flex={1}>
          <Button onPress={onConfirm} variant={confirmButtonVariant}>
            {confirmLabel || t('common.confirm')}
          </Button>
        </Box>
      </Box>
    </ModalDialog>
  );
};
