import PasswordSaved from '@assets/password-required.png';
import { useTranslations } from '@hooks';
import { BottomSheet } from '@ui';
import { memo } from 'react';
import { Trans } from 'react-i18next';
import { StyleSheet } from 'react-native-unistyles';
import { Button } from '../buttons';
import { CachedImage as Image } from '../cached-image';
import { Box } from '../layout/box';
import { Text, Title } from '../typography';

interface PasswordBackupConfirmationDialogProps {
  isVisible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const PasswordBackupConfirmationDialog = memo(
  ({
    isVisible,
    onConfirm,
    onCancel,
  }: PasswordBackupConfirmationDialogProps) => {
    const t = useTranslations('wallet', {
      keyPrefix: 'private.secure.confirm',
    });

    return (
      <BottomSheet
        backgroundStyle={styles.modalBackground}
        contentContainerStyle={{ justifyContent: 'space-between' }}
        contentGap={0}
        contentPadding={24}
        isVisible={isVisible}
        keyboardBlurBehavior="restore"
        onDismiss={onCancel}
        showHeader={false}
      >
        <Box align="center" gap={4} mb={8}>
          <Image
            contentFit="contain"
            source={PasswordSaved}
            style={styles.passwordSavedImage}
          />
          <Title textAlign="center">{t('title')}</Title>
          <Text textAlign="center">
            <Trans
              components={{ bold: <Text weight="semibold" /> }}
              i18nKey="wallet:private.secure.confirm.description"
            />
          </Text>
        </Box>
        <Button onPress={onConfirm}>{t('button')}</Button>
      </BottomSheet>
    );
  }
);

const styles = StyleSheet.create(() => ({
  modalBackground: {
    borderRadius: 24,
  },
  passwordSavedImage: {
    width: 74,
    height: 74,
  },
}));
