import LogoGreen from '@assets/logo-reti-green.svg';
import { useTranslations } from '@hooks';
import { BottomSheet } from '@ui';
import type { TextInputType } from 'components/ui/inputs/text-input';
import { useRef, useState } from 'react';
import { StyleSheet } from 'react-native-unistyles';
import { Button } from '../buttons';
import { TextInput } from '../inputs/text-input';
import { Box } from '../layout/box';
import { Text, Title } from '../typography';

interface PasswordConfirmDialogProps {
  visible: boolean;
  onConfirm: (password: string) => void;
  onDismiss: () => void;
  title?: string;
  description?: string;
}

export const PasswordConfirmDialog = ({
  visible,
  onConfirm,
  onDismiss,
  title,
  description,
}: PasswordConfirmDialogProps) => {
  const t = useTranslations('common');
  const [password, setPassword] = useState('');
  const passwordRef = useRef<TextInputType>(null);

  const handleConfirm = () => {
    onConfirm(password);
    setPassword('');
  };

  return (
    <BottomSheet
      backgroundStyle={styles.modalBackground}
      contentPadding={0}
      enablePanDownToClose
      isVisible={visible}
      minHeight={0}
      onDismiss={onDismiss}
      showHeader={false}
    >
      <Box align="center" gap={4} pb={10} pt={6} px={4}>
        <LogoGreen height={58} width={58} />
        <Title textAlign="center">{title}</Title>
        <Text textAlign="center">{description}</Text>
        <TextInput
          isPassword
          label={t('signIn.inputs.password.label')}
          onChangeText={setPassword}
          ref={passwordRef}
          style={{ width: '95%' }}
          value={password}
        />

        <Button mt={2} onPress={handleConfirm} w="100%">
          {t('common.show')}
        </Button>
      </Box>
    </BottomSheet>
  );
};

const styles = StyleSheet.create(() => ({
  modalBackground: {
    borderRadius: 24,
  },
}));
