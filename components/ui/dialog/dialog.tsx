import SvgLogGreen from '@assets/logo-reti-green.svg';
import type React from 'react';
import { useEffect } from 'react';
import {
  Dimensions,
  Modal as RNModal,
  Text,
  View,
  type ViewStyle,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet } from 'react-native-unistyles';

interface ModalDialogProps {
  visible: boolean;
  onDismiss: () => void;
  children: React.ReactNode;
  animationType?: 'none' | 'slide' | 'fade';
  modalStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  showLogo?: boolean;
  title?: string;
  description?: string;
  /**
   * Controls dialog height. When false (default), dialog fits content.
   * When true, uses a default fixed height (60%). When string, uses exact value (e.g., '50%').
   */
  fixedHeight?: boolean | string;
}

export const ModalDialog = ({
  visible,
  onDismiss,
  children,
  animationType = 'slide',
  modalStyle,
  contentStyle,
  showLogo = false,
  title,
  description,
  fixedHeight = false,
}: ModalDialogProps) => {
  const backdropOpacity = useSharedValue(0);
  const dialogTranslateY = useSharedValue(500);

  const useCustomAnimation = animationType === 'slide';
  // mistake apart vacuum vibrant grace middle close awake diary try can snow

  // Compute a numeric minHeight when fixedHeight is provided
  const screenHeight = Dimensions.get('window').height;
  const computedMinHeight = (() => {
    if (!fixedHeight) return;
    if (typeof fixedHeight === 'string') {
      const trimmed = fixedHeight.trim();
      if (trimmed.endsWith('%')) {
        const pct = Number.parseFloat(trimmed.slice(0, -1));
        if (!Number.isNaN(pct)) return (Math.max(0, pct) / 100) * screenHeight;
      }
      const asNumber = Number(trimmed);
      if (!Number.isNaN(asNumber)) return asNumber;
      // Fallback
      return 0.6 * screenHeight;
    }
    return 0.6 * screenHeight;
  })();

  useEffect(() => {
    if (!useCustomAnimation) {
      return;
    }

    if (visible) {
      backdropOpacity.value = withTiming(1, { duration: 250 });
      dialogTranslateY.value = withSpring(0, {
        damping: 20,
        stiffness: 200,
      });
    } else {
      backdropOpacity.value = withTiming(0, { duration: 200 });
      dialogTranslateY.value = withTiming(500, { duration: 200 });
    }
  }, [visible, backdropOpacity, dialogTranslateY, useCustomAnimation]);

  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: useCustomAnimation ? backdropOpacity.value : 1,
  }));

  const dialogAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: useCustomAnimation ? dialogTranslateY.value : 0 },
    ],
  }));

  return (
    <RNModal
      animationType={useCustomAnimation ? 'fade' : animationType}
      onRequestClose={onDismiss}
      transparent
      visible={visible}
    >
      <Animated.View
        style={[styles.backdrop, modalStyle, backdropAnimatedStyle]}
      >
        <Animated.View
          style={[
            styles.dialog,
            contentStyle,
            dialogAnimatedStyle,
            fixedHeight ? { minHeight: computedMinHeight } : undefined,
          ]}
        >
          {showLogo && (
            <View style={styles.iconContainer}>
              <SvgLogGreen height={58} width={58} />
            </View>
          )}

          {title && <Text style={styles.title}>{title}</Text>}
          {description && <Text style={styles.description}>{description}</Text>}

          {children}
        </Animated.View>
      </Animated.View>
    </RNModal>
  );
};

const styles = StyleSheet.create((theme) => ({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
    overflow: 'hidden',
  },
  dialog: {
    backgroundColor: theme.colors.grey[50],
    padding: 24,
    borderTopRightRadius: 32,
    borderTopLeftRadius: 32,
    gap: 16,
    // minHeight now controlled via prop
    paddingBottom: 24 + 100,
    marginBottom: -100,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    fontSize: 32,
    marginBottom: 16,
  },
  description: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
}));
