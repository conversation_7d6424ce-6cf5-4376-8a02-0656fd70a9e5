import { useTranslations } from '@hooks';
import type { FC } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { Icon } from 'react-native-paper';
import Animated, { FadeInDown, FadeOut } from 'react-native-reanimated';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

type NotificationType = 'Info' | 'Error' | 'Success';

export const NotificationType = {
  Info: 'Info',
  Error: 'Error',
  Success: 'Success',
} as const;

type TNotificationContainer = {
  type?: NotificationType;
  label?: string;
  icon?: string;
  show?: boolean;
  onPress: () => void;
  translate?: boolean;
};

const getIcon = (type: NotificationType) => {
  switch (type) {
    case 'Info':
      return 'information';
    case 'Error':
      return 'alert-outline';
    default:
      return 'check-outline';
  }
};

export const NotificationContainer: FC<TNotificationContainer> = ({
  type = NotificationType.Info,
  label,
  icon,
  show = false,
  translate,
  onPress,
}) => {
  const t = useTranslations();
  const { theme } = useUnistyles();

  if (!show) {
    return null;
  }

  return (
    <Animated.View
      entering={FadeInDown}
      exiting={FadeOut}
      style={[styles.container]}
    >
      <TouchableOpacity onPress={onPress} style={styles.touchableOpacity}>
        <Animated.View style={[styles.labelContainer]}>
          <View style={styles.iconContainer}>
            <Icon
              color={theme.colors.red}
              size={20}
              source={icon ? icon : getIcon(type)}
            />
          </View>
          <View>
            <Text style={{ color: theme.colors.red }}>
              {label && translate ? t(label) : label}
            </Text>
          </View>
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    width: '100%',
    marginBottom: 8,
  },
  touchableOpacity: {
    width: '100%',
  },
  labelContainer: {
    borderRadius: 8,
    padding: 8,
    borderStyle: 'solid',
    borderColor: theme.colors.red,
    borderWidth: 1,
    flexDirection: 'row',
  },
  iconContainer: {
    marginRight: 8,
  },
}));
