import { useTranslations } from '@hooks';
import { Text } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { CachedImage as Image } from '../cached-image';
import { Box } from '../layout/box';

export const AmbassadorBox = () => {
  const t = useTranslations();

  return (
    <Box
      bgColor="white"
      flex={1}
      h={100}
      justify="between"
      p={4}
      radius={16}
      style={styles.container}
    >
      <Box flex={1} gap={0.5} justify="between" zIndex={2}>
        <Text style={styles.label}>{t('ambassadorBox.subtitle')}</Text>
        <Box flexDirection="column" gap={0.5} justify="between">
          <Text style={styles.title}>RETI Ambasador</Text>
          <Text style={styles.period}>{t('ambassadorBox.reward')}</Text>
        </Box>
      </Box>
      <Image
        contentFit="contain"
        source={require('@assets/join-ambasador.png')}
        style={styles.ambassadorImage}
      />
    </Box>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    boxShadow: '0px 4px 24px -8px rgba(13, 61, 50, 0.10)',
    overflow: 'hidden',
    position: 'relative',
  },
  label: {
    color: theme.colors.grey[600],
    fontSize: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 500,
  },
  period: {
    fontSize: 10,
    color: theme.colors.grey[500],
  },
  ambassadorImage: {
    position: 'absolute',
    right: -48,
    top: -32,
    width: 130,
    height: 130,
    opacity: 0.5,
    transform: [{ rotate: '-10deg' }, { scale: 0.7 }, { scaleX: -1 }],
  },
}));
