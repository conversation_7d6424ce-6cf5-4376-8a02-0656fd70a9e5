import { useTranslations } from '@hooks';
import { Text } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { Box } from '../layout/box';

export const PlanBox = () => {
  const t = useTranslations();

  return (
    <Box
      bgColor="white"
      flex={1}
      h={100}
      justify="between"
      p={4}
      radius={16}
      style={styles.container}
    >
      <Box flex={1} justify="between">
        <Text style={styles.label}>{t('planBox.title')}</Text>
        <Box flexDirection="column" gap={0.5} justify="between">
          <Text style={styles.planTitle}>{t('planBox.typeBasic')}</Text>
          <Text style={styles.price}>
            0 PLN <Text style={styles.period}>/{t('planBox.monthly')}</Text>
          </Text>
        </Box>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create((theme) => ({
  container: {
    boxShadow: '0px 4px 24px -8px rgba(13, 61, 50, 0.10)',
  },
  label: {
    color: theme.colors.grey[500],
    fontSize: 12,
  },
  plan: {
    color: theme.colors.black,
    fontSize: 20,
    fontWeight: 'bold',
  },
  planTitle: {
    fontSize: 16,
    fontWeight: 500,
  },
  price: {
    fontSize: 10,
    fontWeight: 500,
  },
  period: {
    fontSize: 12,
    color: theme.colors.grey[300],
  },
}));
