import { usePluralUnit, useTranslations } from '@hooks';
import type { Frequency, PlanType } from '@store/saving-plan.store';
import useSavingPlanStore from '@store/saving-plan.store';
import { View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { Label, LabelContent } from './summary-parts';

export interface SavingPlanSummaryInfoProps {
  timeHorizon: number;
  selectedPlan?: PlanType;
  adjustedPlanOption?: PlanType | string;
  adjustedPlanPercentage?: number;
  fixedPlanValue?: number;
  savingAmount?: number;
  frequency?: Frequency | null;
  ignoreTransactionsAbove?: number;
}

export const SavingPlanSummaryInfo = ({
  timeHorizon,
  selectedPlan,
  adjustedPlanOption,
  adjustedPlanPercentage = 0,
  fixedPlanValue = 0,
  savingAmount = 0,
  frequency = null,
  ignoreTransactionsAbove = 0,
}: SavingPlanSummaryInfoProps) => {
  const t = useTranslations();
  const getPluralUnit = usePluralUnit();
  const unit = t(getPluralUnit(timeHorizon, 'savingPlan.timeHorizonStep.unit'));

  const isAdjusted = selectedPlan === 'AM' || selectedPlan === 'PR';
  const isRecurring = selectedPlan === 'RP';

  return (
    <View>
      <View style={styles.labelContainer}>
        <Label>{t('savingPlan.summary.content.timeHorizon')}</Label>
        <LabelContent>{`${timeHorizon} ${unit}`}</LabelContent>
      </View>
      <View style={styles.labelContainer}>
        <Label>{t('savingPlan.summary.content.savingType')}</Label>
        {isAdjusted && (
          <LabelContent>{t('savingPlan.plans.adjusted.title')}</LabelContent>
        )}
        {isRecurring && (
          <LabelContent>{t('savingPlan.plans.simple.title')}</LabelContent>
        )}
      </View>
      {isAdjusted && (
        <View style={styles.labelContainer}>
          {adjustedPlanOption === 'AM' && (
            <>
              <Label>{t('savingPlan.adjustedPlan.fixedPlan.heading')}</Label>
              <View style={styles.inlineContainer}>
                <Label>
                  {t('savingPlan.summary.content.savingFixedValue')}
                </Label>
                <LabelContent>{fixedPlanValue}</LabelContent>
                <Label>{t('units.money')}</Label>
              </View>
            </>
          )}
          {adjustedPlanOption === 'PR' && (
            <>
              <Label>
                {t('savingPlan.adjustedPlan.percentagePlan.heading')}
              </Label>
              <View style={styles.inlineContainer}>
                <Label>
                  {t('savingPlan.summary.content.savingPercentage')}
                </Label>
                <LabelContent>{adjustedPlanPercentage}</LabelContent>
                <Label>{t('units.percent')}</Label>
              </View>
            </>
          )}
        </View>
      )}
      {isRecurring && (
        <View style={styles.labelContainer}>
          <Label>{t('savingPlan.summary.content.savingFixedValue')}</Label>
          <View style={styles.inlineContainer}>
            <LabelContent>{savingAmount}</LabelContent>
            <Label>{t('units.money')}</Label>
            <Label> / </Label>
            {frequency && (
              <Label>
                {t(`savingPlan.amountStep.frequencies.${frequency}`)}
              </Label>
            )}
          </View>
        </View>
      )}
      {ignoreTransactionsAbove > 0 && (
        <View style={styles.labelContainer}>
          <Label>{t('savingPlan.summary.content.ignoreTransaction')}</Label>
          <View style={styles.inlineContainer}>
            <Label>
              {t('savingPlan.summary.content.ignoreTransactionPrefix')}
            </Label>
            <LabelContent>{ignoreTransactionsAbove}</LabelContent>
            <Label>{t('units.money')}</Label>
          </View>
        </View>
      )}
    </View>
  );
};

export const SavingPlanInfoFromStore = () => {
  const {
    timeHorizon,
    selectedPlan,
    adjustedPlanPercentage,
    ignoreTransactionsAbove,
    fixedPlanValue,
    savingAmountAndFrequency,
    adjustedPlanOption,
  } = useSavingPlanStore();

  return (
    <SavingPlanSummaryInfo
      adjustedPlanOption={adjustedPlanOption}
      adjustedPlanPercentage={adjustedPlanPercentage}
      fixedPlanValue={fixedPlanValue}
      frequency={savingAmountAndFrequency.frequency}
      ignoreTransactionsAbove={ignoreTransactionsAbove}
      savingAmount={savingAmountAndFrequency.amount}
      selectedPlan={selectedPlan}
      timeHorizon={timeHorizon}
    />
  );
};

const styles = StyleSheet.create(() => ({
  labelContainer: {
    marginBottom: 15,
  },
  inlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
}));
