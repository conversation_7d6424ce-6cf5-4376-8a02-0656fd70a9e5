import SvgChevronRight from '@assets/chevron-right.svg';
import SvgFileIcon from '@assets/file-icon.svg';
import type { Agreement } from '@endpoints/terms.endpoints';
import { Box, Text } from '@ui';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

interface TermsListProps {
  agreements: Agreement[];
  onAgreementPress: (agreement: Agreement) => void;
}

export default function TermsList({
  agreements,
  onAgreementPress,
}: TermsListProps) {
  return (
    <Box
      bgColor="grey.50"
      borderColor="grey.100"
      borderWidth={1}
      gap={8}
      mt={5}
      p={4}
      radius={16}
    >
      {agreements.map((agreement) => (
        <TouchableOpacity
          key={`agreement-${agreement.id}`}
          onPress={() => onAgreementPress(agreement)}
        >
          <Box align="center" flexDirection="row" justify="between">
            <Box align="center" flexDirection="row">
              <View style={styles.icon}>
                <SvgFileIcon />
              </View>
              <Text color="dark" ml={3} size="md">
                {agreement.name}
              </Text>
            </Box>
            <View style={styles.chevron}>
              <SvgChevronRight />
            </View>
          </Box>
        </TouchableOpacity>
      ))}
    </Box>
  );
}

const styles = StyleSheet.create(() => ({
  icon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chevron: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
}));
