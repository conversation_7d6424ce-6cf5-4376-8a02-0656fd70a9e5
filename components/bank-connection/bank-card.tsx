import { useTranslations } from '@hooks';
import { Box, PasswordConfirmDialog, Text } from '@ui';
import type React from 'react';
import { useState } from 'react';
import {
  Image,
  type ImageSourcePropType,
  TouchableOpacity,
} from 'react-native';
import { Icon } from 'react-native-paper';
import { StyleSheet, useUnistyles } from 'react-native-unistyles';

interface BankCardProps {
  logo: ImageSourcePropType;
  name: string;
  accountNumber: string;
  showMask?: boolean;
  children?: React.ReactNode;
  allowUnmask?: boolean;
}

export const BankCard: React.FC<BankCardProps> = ({
  logo,
  name,
  accountNumber,
  showMask = true,
  children,
  allowUnmask = false,
}) => {
  const [isMasked, setIsMasked] = useState(showMask);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const { theme } = useUnistyles();
  const t = useTranslations();

  const toggleMask = () => {
    if (!allowUnmask) {
      return;
    }

    if (isMasked) {
      setShowPasswordModal(true);
    } else {
      setIsMasked(true);
    }
  };

  const handlePasswordCorrect = (password: string) => {
    if (password === 'haslo123') {
      setIsMasked(false);
    }
    setShowPasswordModal(false);
  };

  const getMaskedAccount = (acc: string) => {
    if (!acc.startsWith('PL')) {
      return acc;
    }
    const visible = acc.slice(-4);
    return `PL  ••••  ••••  ••••  ••••  ${visible}`;
  };

  return (
    <Box
      bgColor="grey.50"
      borderColor="grey.100"
      borderWidth={1}
      gap={4}
      p={4}
      radius={24}
    >
      <Box align="center" flexDirection="row" gap={2}>
        <Image source={logo} style={styles.logo} />
        <Text>{name}</Text>
      </Box>

      <Box align="center" flexDirection="row" justify="between">
        <Text>
          {isMasked ? getMaskedAccount(accountNumber) : accountNumber}
        </Text>
        {allowUnmask && (
          <TouchableOpacity onPress={toggleMask}>
            <Icon
              color={theme.colors.grey[900]}
              size={24}
              source={isMasked ? 'eye' : 'eye-off'}
            />
          </TouchableOpacity>
        )}
      </Box>

      {children && <Box mt={2}>{children}</Box>}

      <PasswordConfirmDialog
        description={t('viewKeyModal.description')}
        onConfirm={handlePasswordCorrect}
        onDismiss={() => setShowPasswordModal(false)}
        title={t('viewKeyModal.title')}
        visible={showPasswordModal}
      />
    </Box>
  );
};
const styles = StyleSheet.create(() => ({
  logo: {
    width: 32,
    height: 32,
  },
}));
