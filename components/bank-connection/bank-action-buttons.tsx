import { useTranslations } from '@hooks';
import { Box, Button, ConfirmExitDialog } from '@ui';
import type React from 'react';
import { useState } from 'react';

interface BankActionButtonsProps {
  onLeftButtonClick: () => void;
  onRightButtonClick: () => void;
  leftButtonText?: string;
  rightButtonText?: string;
}

export const BankActionButtons: React.FC<BankActionButtonsProps> = ({
  onLeftButtonClick,
  onRightButtonClick,
  leftButtonText = 'bank-connection.details',
  rightButtonText = 'bank-connection.disconect',
}) => {
  const t = useTranslations();
  const [showConfirm, setShowConfirm] = useState(false);

  const handleConfirm = () => {
    onRightButtonClick();
    setShowConfirm(false);
  };

  const handleCancel = () => {
    setShowConfirm(false);
  };

  return (
    <>
      <Box flexDirection="row" justify="between" w="100%">
        <Button onPress={onLeftButtonClick} size="xs" w="49%">
          {t(leftButtonText)}
        </Button>
        <Button
          onPress={() => setShowConfirm(true)}
          size="xs"
          variant="outlinedGrey"
          w="49%"
        >
          {t(rightButtonText)}
        </Button>
      </Box>

      <ConfirmExitDialog
        cancelLabel={t('bank-connection.disconnectModal.cancel')}
        confirmLabel={t('bank-connection.disconnectModal.confirm')}
        description={t('bank-connection.disconnectModal.description')}
        onConfirm={handleConfirm}
        onDismiss={handleCancel}
        title={t('bank-connection.disconnectModal.title')}
        visible={showConfirm}
      />
    </>
  );
};
