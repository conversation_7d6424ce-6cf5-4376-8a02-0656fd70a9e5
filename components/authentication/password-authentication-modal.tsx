/**
 * Password Authentication Modal
 *
 * Bottom sheet modal for password entry as fallback authentication.
 * Features:
 * - Secure password input
 * - Error handling and validation
 * - Attempt tracking
 * - Consistent styling with app design
 */

import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslations } from '@hooks';
import { Box, Button, CustomBackdrop, Text, TextInput, Title } from '@ui';
import Security from 'assets/password-required.png';
import { CachedImage as Image } from 'components/ui/cached-image';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Keyboard, Platform } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { authenticationService } from 'services/authentication.service';
import type { AuthenticationModalProps } from 'types/authentication.types';
import {
  AuthenticationMethod,
  AuthenticationResult,
} from 'types/authentication.types';

interface PasswordAuthenticationModalProps
  extends Omit<AuthenticationModalProps, 'method'> {
  /** Custom error message for password validation */
  passwordError?: string;
}

export const PasswordAuthenticationModal = memo(
  ({
    isVisible,
    onSuccess,
    onFailure,
    onCancel,
    isLoading = false,
    title,
    description,
    passwordError,
  }: PasswordAuthenticationModalProps) => {
    const t = useTranslations('security');
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const passwordRef = useRef<any>(null);
    const [password, setPassword] = useState('');
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
    const wasVisibleRef = useRef(false);

    const snapPoints = useMemo(() => {
      if (isKeyboardVisible && keyboardHeight > 0) {
        const screenHeight = Platform.select({ ios: 800, android: 600 }) || 700;
        const keyboardPercentage = (keyboardHeight / screenHeight) * 100;
        const requiredPercentage = Math.min(keyboardPercentage + 45, 95);
        return [`${requiredPercentage}%`];
      }
      return ['60%', '80%'];
    }, [isKeyboardVisible, keyboardHeight]);

    useEffect(() => {
      const keyboardWillShowListener = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
        (event) => {
          setKeyboardHeight(event.endCoordinates.height);
          setIsKeyboardVisible(true);

          setTimeout(
            () => {
              bottomSheetModalRef.current?.snapToIndex(0);
            },
            Platform.OS === 'ios' ? 0 : 100
          );
        }
      );

      const keyboardWillHideListener = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
        () => {
          setIsKeyboardVisible(false);
          setKeyboardHeight(0);

          setTimeout(
            () => {
              bottomSheetModalRef.current?.snapToIndex(0);
            },
            Platform.OS === 'ios' ? 0 : 100
          );
        }
      );

      return () => {
        keyboardWillShowListener.remove();
        keyboardWillHideListener.remove();
      };
    }, []);

    useEffect(() => {
      if (isVisible) {
        bottomSheetModalRef.current?.present();
        if (!wasVisibleRef.current) {
          setPassword('');
          setHasError(false);
          setErrorMessage('');
          wasVisibleRef.current = true;
        }
        setTimeout(() => {
          passwordRef.current?.focus();
        }, 300);
      } else {
        bottomSheetModalRef.current?.dismiss();
        setIsKeyboardVisible(false);
        setKeyboardHeight(0);
        if (wasVisibleRef.current) {
          wasVisibleRef.current = false;
        }
      }
    }, [isVisible]);

    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1) {
          onCancel();
        }
      },
      [onCancel]
    );

    useEffect(() => {
      if (
        passwordError &&
        typeof passwordError === 'string' &&
        passwordError.trim().length > 0
      ) {
        setHasError(true);
        setErrorMessage(passwordError);
      }
    }, [passwordError]);

    const handlePasswordChange = useCallback(
      (newPassword: string) => {
        setPassword(newPassword);
        if (hasError) {
          setHasError(false);
          setErrorMessage('');
        }
      },
      [hasError]
    );

    const handleSubmit = useCallback(async () => {
      if (!password.trim()) {
        setHasError(true);
        setErrorMessage(t('password.errors.required'));
        return;
      }

      if (password.length < 8) {
        setHasError(true);
        setErrorMessage(t('password.errors.tooShort'));
        return;
      }

      try {
        setHasError(false);
        setErrorMessage('');

        const result = await authenticationService.verifyPassword(password);

        if (result === AuthenticationResult.SUCCESS) {
          onSuccess(AuthenticationMethod.PASSWORD);
        } else if (result === AuthenticationResult.FAILED) {
          setHasError(true);
          setErrorMessage(t('password.errors.incorrect'));
          onFailure(
            AuthenticationMethod.PASSWORD,
            t('password.errors.incorrect')
          );
        } else if (result === AuthenticationResult.NOT_AVAILABLE) {
          setHasError(true);
          setErrorMessage(t('password.errors.notConfigured'));
          onFailure(
            AuthenticationMethod.PASSWORD,
            t('password.errors.notConfigured')
          );
        }
      } catch (_error) {
        setHasError(true);
        setErrorMessage(t('password.errors.verificationFailed'));
        onFailure(
          AuthenticationMethod.PASSWORD,
          t('password.errors.verificationFailed')
        );
      }
    }, [password, onSuccess, onFailure, t]);

    const handleKeyPress = useCallback(
      (e: any) => {
        if (e.nativeEvent.key === 'Enter') {
          handleSubmit();
        }
      },
      [handleSubmit]
    );

    return (
      <BottomSheetModal
        animateOnMount={true}
        backdropComponent={CustomBackdrop}
        backgroundStyle={styles.modalBackground}
        enableDynamicSizing={false}
        enablePanDownToClose={!isLoading}
        handleIndicatorStyle={{ display: 'none' }}
        index={0}
        keyboardBehavior="extend"
        keyboardBlurBehavior="restore"
        onChange={handleSheetChanges}
        ref={bottomSheetModalRef}
        snapPoints={snapPoints}
      >
        <BottomSheetView style={styles.contentContainer}>
          <Box align="center" gap={4} mb={8}>
            <Image
              contentFit="contain"
              source={Security}
              style={styles.securityIcon}
            />

            <Title size="lg" textAlign="center">
              {title || t('password.title')}
            </Title>

            <Text color="grey.600" textAlign="center">
              {description || t('password.description')}
            </Text>
          </Box>

          <Box gap={6}>
            <TextInput
              autoComplete="password"
              editable={!isLoading}
              errorMessage={hasError ? errorMessage : undefined}
              isPassword
              label={t('password.label')}
              onChangeText={handlePasswordChange}
              onKeyPress={handleKeyPress}
              onSubmitEditing={handleSubmit}
              placeholder={t('password.placeholder')}
              ref={passwordRef}
              returnKeyType="done"
              textContentType="password"
              value={password}
            />

            <Button
              disabled={!password.trim() || isLoading}
              loading={isLoading}
              onPress={handleSubmit}
              variant="contained"
            >
              {t('password.authenticate')}
            </Button>
          </Box>
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

PasswordAuthenticationModal.displayName = 'PasswordAuthenticationModal';

const styles = StyleSheet.create(() => ({
  modalBackground: {
    borderRadius: 24,
  },
  contentContainer: {
    flex: 1,
    padding: 24,
    gap: 16,
    justifyContent: 'space-between',
    minHeight: 400,
  },
  securityIcon: {
    width: 72,
    height: 72,
  },
}));
