/**
 * PIN Authentication Modal
 *
 * Bottom sheet modal for PIN entry following the app's design patterns.
 * Features:
 * - 6-digit PIN input
 * - Error handling and validation
 * - Attempt tracking
 * - Consistent styling with app design
 */

import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslations } from '@hooks';
import { Box, Button, CustomBackdrop, Text, Title } from '@ui';
import Security from 'assets/password-required.png';
import { CachedImage as Image } from 'components/ui/cached-image';
import { PinInput } from 'components/ui/inputs/pin-input';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Keyboard, Platform } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import { authenticationService } from 'services/authentication.service';
import type { AuthenticationModalProps } from 'types/authentication.types';
import {
  AuthenticationMethod,
  AuthenticationResult,
} from 'types/authentication.types';

interface PinAuthenticationModalProps
  extends Omit<AuthenticationModalProps, 'method'> {
  /** Custom error message for PIN validation */
  pinError?: string;
  /** Whether to clear PIN input on error */
  clearOnError?: boolean;
}

export const PinAuthenticationModal = memo(
  ({
    isVisible,
    onSuccess,
    onFailure,
    onCancel,
    isLoading = false,
    title,
    description,
    pinError,
    clearOnError = true,
  }: PinAuthenticationModalProps) => {
    const t = useTranslations('security');
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const [pin, setPin] = useState('');
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
    const wasVisibleRef = useRef(false);

    const snapPoints = useMemo(() => {
      if (isKeyboardVisible && keyboardHeight > 0) {
        const screenHeight = Platform.select({ ios: 800, android: 600 }) || 700;
        const keyboardPercentage = (keyboardHeight / screenHeight) * 100;
        const requiredPercentage = Math.min(keyboardPercentage + 45, 95); // Cap at 95%
        return [`${requiredPercentage}%`];
      }
      return ['60%', '80%'];
    }, [isKeyboardVisible, keyboardHeight]);

    useEffect(() => {
      const keyboardWillShowListener = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
        (event) => {
          setKeyboardHeight(event.endCoordinates.height);
          setIsKeyboardVisible(true);

          setTimeout(
            () => {
              bottomSheetModalRef.current?.snapToIndex(0);
            },
            Platform.OS === 'ios' ? 0 : 100
          );
        }
      );

      const keyboardWillHideListener = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
        () => {
          setIsKeyboardVisible(false);
          setKeyboardHeight(0);

          setTimeout(
            () => {
              bottomSheetModalRef.current?.snapToIndex(0);
            },
            Platform.OS === 'ios' ? 0 : 100
          );
        }
      );

      return () => {
        keyboardWillShowListener.remove();
        keyboardWillHideListener.remove();
      };
    }, []);

    useEffect(() => {
      if (isVisible) {
        bottomSheetModalRef.current?.present();
      } else {
        bottomSheetModalRef.current?.dismiss();
        setIsKeyboardVisible(false);
        setKeyboardHeight(0);
      }
    }, [isVisible]);

    useEffect(() => {
      if (
        pinError &&
        typeof pinError === 'string' &&
        pinError.trim().length > 0
      ) {
        setHasError(true);
        setErrorMessage(pinError);
      }
    }, [pinError]);

    useEffect(() => {
      if (isVisible && !wasVisibleRef.current) {
        setPin('');
        setHasError(false);
        setErrorMessage('');
        wasVisibleRef.current = true;
      } else if (!isVisible && wasVisibleRef.current) {
        wasVisibleRef.current = false;
      }
    }, [isVisible]);

    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1) {
          onCancel();
        }
      },
      [onCancel]
    );

    const handlePinChange = useCallback((newPin: string) => {
      setPin((prevPin) => {
        if (prevPin !== newPin) {
          return newPin;
        }
        return prevPin;
      });

      setHasError((prevHasError) => {
        if (prevHasError && newPin.length > 0) {
          setErrorMessage('');
          return false;
        }
        return prevHasError;
      });
    }, []);

    const handlePinComplete = useCallback(
      async (completedPin: string) => {
        if (isLoading) return;

        if (completedPin.length !== 6) {
          setHasError(true);
          setErrorMessage(t('pin.errors.invalidLength'));
          return;
        }

        try {
          const result = await authenticationService.verifyPin(completedPin);

          if (result === AuthenticationResult.SUCCESS) {
            setHasError(false);
            setErrorMessage('');
            onSuccess(AuthenticationMethod.PIN);
          } else if (result === AuthenticationResult.FAILED) {
            setHasError(true);
            setErrorMessage(t('pin.errors.incorrect'));
            onFailure(AuthenticationMethod.PIN, t('pin.errors.incorrect'));
          } else if (result === AuthenticationResult.NOT_AVAILABLE) {
            setHasError(true);
            setErrorMessage(t('pin.errors.notConfigured'));
            onFailure(AuthenticationMethod.PIN, t('pin.errors.notConfigured'));
          }
        } catch (_error) {
          setHasError(true);
          setErrorMessage(t('pin.errors.verificationFailed'));
          onFailure(
            AuthenticationMethod.PIN,
            t('pin.errors.verificationFailed')
          );
        }
      },
      [isLoading, onSuccess, onFailure, t]
    );

    const handleSubmit = useCallback(async () => {
      if (pin.length !== 6) {
        setHasError(true);
        setErrorMessage(t('pin.errors.invalidLength'));
        return;
      }

      try {
        const result = await authenticationService.verifyPin(pin);

        if (result === AuthenticationResult.SUCCESS) {
          setHasError(false);
          setErrorMessage('');
          onSuccess(AuthenticationMethod.PIN);
        } else if (result === AuthenticationResult.FAILED) {
          setHasError(true);
          setErrorMessage(t('pin.errors.incorrect'));
          onFailure(AuthenticationMethod.PIN, t('pin.errors.incorrect'));
        } else if (result === AuthenticationResult.NOT_AVAILABLE) {
          setHasError(true);
          setErrorMessage(t('pin.errors.notConfigured'));
          onFailure(AuthenticationMethod.PIN, t('pin.errors.notConfigured'));
        }
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: .
        console.error('PIN verification error:', error);
        setHasError(true);
        setErrorMessage(t('pin.errors.verificationFailed'));
        onFailure(AuthenticationMethod.PIN, t('pin.errors.verificationFailed'));
      }
    }, [pin, onSuccess, onFailure, t]);

    return (
      <BottomSheetModal
        animateOnMount={true}
        backdropComponent={CustomBackdrop}
        backgroundStyle={styles.modalBackground}
        enableDynamicSizing={false}
        enablePanDownToClose={!isLoading}
        handleIndicatorStyle={{ display: 'none' }}
        index={0}
        keyboardBehavior="extend"
        keyboardBlurBehavior="restore"
        onChange={handleSheetChanges}
        ref={bottomSheetModalRef}
        snapPoints={snapPoints}
      >
        <BottomSheetView style={styles.contentContainer}>
          <Box align="center" gap={4} mb={8}>
            <Image
              contentFit="contain"
              source={Security}
              style={styles.securityIcon}
            />

            <Title size="lg" textAlign="center">
              {title || t('pin.title')}
            </Title>

            <Text color="grey.600" textAlign="center">
              {description || t('pin.description')}
            </Text>
          </Box>

          <Box gap={6}>
            <PinInput
              autoFocus
              clearOnError={clearOnError}
              disabled={isLoading}
              errorMessage={errorMessage}
              hasError={hasError}
              onChange={handlePinChange}
              onComplete={handlePinComplete}
              value={pin}
            />

            <Button
              disabled={pin.length !== 6 || isLoading}
              loading={isLoading}
              onPress={handleSubmit}
              variant="contained"
            >
              {t('pin.authenticate')}
            </Button>
          </Box>
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

PinAuthenticationModal.displayName = 'PinAuthenticationModal';

const styles = StyleSheet.create(() => ({
  modalBackground: {
    borderRadius: 24,
  },
  contentContainer: {
    flex: 1,
    padding: 24,
    gap: 16,
    justifyContent: 'space-between',
    minHeight: 400,
  },
  securityIcon: {
    width: 72,
    height: 72,
  },
}));
