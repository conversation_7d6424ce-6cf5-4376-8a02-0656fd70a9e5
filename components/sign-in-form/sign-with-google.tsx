import { useTranslations } from '@hooks';
import { Button } from '@ui';
import { CachedImage as Image } from 'components/ui/cached-image';
import { useUnistyles } from 'react-native-unistyles';

export const SignWithGoogleButton = () => {
  const t = useTranslations();
  const { theme } = useUnistyles();

  return (
    <Button
      buttonStyle={{ borderColor: theme.colors.grey[100] }}
      icon={
        <Image
          source={require('@assets/google-button.png')}
          style={{ width: 30, height: 30 }}
        />
      }
      iconPosition="farLeft"
      labelStyle={{ color: theme.colors.grey[500] }}
      mt={2}
      rounded="full"
      variant="outlined"
    >
      {t('signIn.buttons.signIn.signInByGoogleAccount')}
    </Button>
  );
};
