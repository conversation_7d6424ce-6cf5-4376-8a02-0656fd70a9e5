import { useOnKeyboardHide, useTranslations, useValidator } from '@hooks';
import useErrorStore from '@store/error.store';
import { Box, Button, TextInput, type TextInputType } from '@ui';
import { emailSchema } from '@utils';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type IEmailForm = {
  redirectPath: string;
};

export const EmailForm = ({ redirectPath }: IEmailForm) => {
  const t = useTranslations();
  const router = useRouter();
  const emailRef = useRef<TextInputType>(null);

  const [email, setEmail] = useState('');

  const [emailValidatorMessage, validateEmail, isValidEmail] =
    useValidator(emailSchema);
  const { clearAllErrors } = useErrorStore();

  const handleEmailChange = (text: string) => {
    validateEmail(text);
    setEmail(text);
    clearAllErrors();
  };

  const submitForm = useCallback(() => {
    clearAllErrors();
    router.push({
      pathname: redirectPath,
      params: { email },
    });
  }, [email, clearAllErrors, redirectPath, router]);

  useOnKeyboardHide(() => {
    clearAllErrors();
  });

  const isButtonEnabled = useMemo(
    () => isValidEmail(email),
    [email, isValidEmail]
  );

  useEffect(() => {
    emailRef.current?.focus();
  }, []);

  return (
    <Box gap={2}>
      <TextInput
        autoCapitalize="none"
        autoComplete="email"
        errorMessage={emailValidatorMessage}
        keyboardType="email-address"
        label={t('signIn.inputs.email.label')}
        onChangeText={handleEmailChange}
        placeholder={t('signIn.inputs.email.placeholder')}
        ref={emailRef}
        value={email}
      />
      <Button disabled={!isButtonEnabled} onPress={submitForm}>
        {t('common.continue')}
      </Button>
    </Box>
  );
};
