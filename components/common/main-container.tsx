import type { FC } from 'react';
import { View, type ViewProps } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';

export const MainContainer: FC<ViewProps> = (props) => {
  return <View style={style.container}>{props.children}</View>;
};

const style = StyleSheet.create((theme) => ({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
    backgroundColor: theme.colors.primary[600],
  },
}));
