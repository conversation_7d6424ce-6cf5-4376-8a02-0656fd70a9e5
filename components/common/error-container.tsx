import { useOnError } from '@hooks';
import useErrorStore from '@store/error.store';
import { NotificationContainer, NotificationType } from '@ui';
import { useCallback, useState } from 'react';
import { type AppError, ErrorKey } from 'types/error.types';

interface Props {
  callback?: () => void;
  errorKey: Error<PERSON>ey;
}

export const ErrorContainer = ({
  callback,
  errorKey = ErrorKey.AuthError,
}: Props) => {
  const [show, setShow] = useState(false);
  const [errorMessage, setErrorMessage] = useState<AppError | undefined>(
    undefined
  );
  const { clearError } = useErrorStore();

  useOnError(errorKey, (error) => {
    if (error) {
      callback?.();
      setShow(true);
      setErrorMessage(error);
    } else {
      setShow(false);
      setErrorMessage(undefined);
    }
  });

  const onPress = useCallback(() => {
    setShow(false);
    callback?.();
    clearError(errorKey);
  }, [callback, clearError, errorKey]);

  return (
    <NotificationContainer
      label={errorMessage?.errorText}
      onPress={onPress}
      show={show}
      type={NotificationType.Error}
    />
  );
};
