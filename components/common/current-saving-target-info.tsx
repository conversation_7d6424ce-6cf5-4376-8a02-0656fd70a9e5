import useSavingPlanStore from '@store/saving-plan.store';
import { SavingPlanInfoFromStore } from 'components/saving-plan/summary-info';
import { View } from 'react-native';
import { Card } from 'react-native-paper';
import { StyleSheet } from 'react-native-unistyles';

export const CurrentSavingTargetInfo = () => {
  const { planName, selectedTarget } = useSavingPlanStore();

  return (
    <Card>
      <Card.Title
        subtitle={selectedTarget?.icon_label}
        subtitleStyle={{ fontSize: 22, marginVertical: 10 }}
        title={`${planName}`}
        titleStyle={{ fontSize: 26, marginTop: 30 }}
      />
      <View style={styles.container}>
        <SavingPlanInfoFromStore />
      </View>
    </Card>
  );
};

const styles = StyleSheet.create(() => ({
  container: {
    padding: 30,
  },
}));
